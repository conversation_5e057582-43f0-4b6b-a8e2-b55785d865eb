{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopPermission.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopPermission.js", "mtime": 1750218870452}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlsL3JlcXVlc3QnOwppbXBvcnQgeyBjb25maWdVcmwgfSBmcm9tICdAL2NvbmZpZyc7CmNvbnN0IGJhc2VVUkwgPSBjb25maWdVcmxbcHJvY2Vzcy5lbnYuVlVFX0FQUF9TRVJWRV0uYmFzZVVSTF9ERk07Ci8qKgogKiDmlofmoaMv55uu5b2V5p2D6ZmQ5o6n5Yi26KGo5YiG6aG15p+l6K+iCiAqIEBwYXJhbSB75p+l6K+i5p2h5Lu2fSBkYXRhCiAqLwoKZXhwb3J0IGZ1bmN0aW9uIGdldFNvcFBlcm1pc3Npb25MaXN0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGJhc2VVUkwgKyAnL2FwaS9Tb3BQZXJtaXNzaW9uL0dldFBhZ2VMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YQogIH0pOwp9Ci8qKgogKiDkv53lrZjmlofmoaMv55uu5b2V5p2D6ZmQ5o6n5Yi26KGoCiAqIEBwYXJhbSBkYXRhCiAqLwoKZXhwb3J0IGZ1bmN0aW9uIHNhdmVTb3BQZXJtaXNzaW9uRm9ybShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBiYXNlVVJMICsgJy9hcGkvU29wUGVybWlzc2lvbi9TYXZlRm9ybScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQovKioKICog6I635Y+W5paH5qGjL+ebruW9leadg+mZkOaOp+WItuihqOivpuaDhQogKiBAcGFyYW0ge0lkfQogKi8KCmV4cG9ydCBmdW5jdGlvbiBnZXRTb3BQZXJtaXNzaW9uRGV0YWlsKGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBiYXNlVVJMICsgJy9hcGkvU29wUGVybWlzc2lvbi9HZXRFbnRpdHknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBpZAogIH0pOwp9Ci8qKgogKiDliKDpmaTmlofmoaMv55uu5b2V5p2D6ZmQ5o6n5Yi26KGoCiAqIEBwYXJhbSB75Li76ZSufSBkYXRhCiAqLwoKZXhwb3J0IGZ1bmN0aW9uIGRlbFNvcFBlcm1pc3Npb24oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYmFzZVVSTCArICcvYXBpL1NvcFBlcm1pc3Npb24vRGVsZXRlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "configUrl", "baseURL", "process", "env", "VUE_APP_SERVE", "baseURL_DFM", "getSopPermissionList", "data", "url", "method", "saveSopPermissionForm", "getSopPermissionDetail", "id", "delSopPermission"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/api/SOP/sopPermission.js"], "sourcesContent": ["import request from '@/util/request'\nimport { configUrl } from '@/config'\nconst baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM\n\n/**\n * 文档/目录权限控制表分页查询\n * @param {查询条件} data\n */\nexport function getSopPermissionList(data) {\n    return request({\n        url: baseURL + '/api/SopPermission/GetPageList',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 保存文档/目录权限控制表\n * @param data\n */\nexport function saveSopPermissionForm(data) {\n    return request({\n        url: baseURL + '/api/SopPermission/SaveForm',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 获取文档/目录权限控制表详情\n * @param {Id}\n */\nexport function getSopPermissionDetail(id) {\n    return request({\n        url: baseURL + '/api/SopPermission/GetEntity',\n        method: 'post',\n        data: id\n    })\n}\n\n/**\n * 删除文档/目录权限控制表\n * @param {主键} data\n */\nexport function delSopPermission(data) {\n    return request({\n        url: baseURL + '/api/SopPermission/Delete',\n        method: 'post',\n        data\n    })\n}\n\n\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB;AACA,SAASC,SAAT,QAA0B,UAA1B;AACA,MAAMC,OAAO,GAAGD,SAAS,CAACE,OAAO,CAACC,GAAR,CAAYC,aAAb,CAAT,CAAqCC,WAArD;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,oBAAT,CAA8BC,IAA9B,EAAoC;EACvC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,gCADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASG,qBAAT,CAA+BH,IAA/B,EAAqC;EACxC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,6BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASI,sBAAT,CAAgCC,EAAhC,EAAoC;EACvC,OAAOb,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,8BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF,IAAI,EAAEK;EAHK,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASC,gBAAT,CAA0BN,IAA1B,EAAgC;EACnC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,2BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH"}]}