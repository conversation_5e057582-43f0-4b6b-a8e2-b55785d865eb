<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile kind="Docker">SEFA.DFM.Api/SEFA.DFM.Api.csproj</projectFile>
    <projectFile profileName="IIS Express">SEFA.DFM.Api/SEFA.DFM.Api.csproj</projectFile>
    <projectFile profileName="SEFA.DFM">SEFA.DFM.Api/SEFA.DFM.Api.csproj</projectFile>
    <projectFile pubXmlPath="SEFA.DFM.Api/Properties/PublishProfiles/FolderProfile.pubxml">SEFA.DFM.Api/SEFA.DFM.Api.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2e714c3e-e365-42ed-abf8-9329a0b5df24" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3ce8f64ebeaa486897a02c3b0fdcd99350e00/eb/7dc2bb1b/IUser.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/716e033f7aec41839fc17bcdeb26600820200/09/c44dd57d/IBaseRepository`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/75332b1874eb4f2c9f17f3ad112dc8fdac00/66/24dcd036/IMinioFileService.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/75332b1874eb4f2c9f17f3ad112dc8fdac00/a2/e5ec0889/IBaseServices`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ae604afeb6574617a94df2a743c6288e46000/18/1174014c/IMapperBase.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/41/429b278c/Monitor.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e300fc3bcf2547fd9aa67e2916204333a25118/9d/141201f2/List`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/SEFA.DFM.Services/SopDirServices.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2w1ONAFwamnwbSf8IQRxisgxjmE" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 启动设置配置文件.SEFA.DFM.Api: SEFA.DFM.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET 启动设置配置文件.SEFA.DFM.Api: SEFA.DFM">
    <configuration name="SEFA.DFM.Api: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/SEFA.DFM.Api/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="8018558093272957986" uuid_low="-4939505218139161185" />
      <method v="2" />
    </configuration>
    <configuration name="SEFA.DFM.Api: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/SEFA.DFM.Api/SEFA.DFM.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="SEFA.DFM.Api: SEFA.DFM" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/SEFA.DFM.Api/SEFA.DFM.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="SEFA.DFM" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="SEFA.DFM.Api/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="sefa.dfm.api" />
          <option name="contextFolderPath" value="C:\work\syngentagroup\SEFA_XZD\SEFA.DFM" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="SEFA.DFM.Api/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2e714c3e-e365-42ed-abf8-9329a0b5df24" name="更改" comment="" />
      <created>1745205575460</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745205575460</updated>
      <workItem from="1745205579627" duration="10877000" />
      <workItem from="1745287355396" duration="5372000" />
      <workItem from="1746520774099" duration="1896000" />
      <workItem from="1746598052505" duration="639000" />
      <workItem from="1746701212492" duration="1618000" />
      <workItem from="1746754575288" duration="8803000" />
      <workItem from="1747031888754" duration="5883000" />
      <workItem from="1747098270206" duration="14495000" />
      <workItem from="1747183935481" duration="17906000" />
      <workItem from="1747287803561" duration="7088000" />
      <workItem from="1747362061490" duration="1714000" />
      <workItem from="1747615184160" duration="620000" />
      <workItem from="1747796625720" duration="5255000" />
      <workItem from="1748243791866" duration="1786000" />
      <workItem from="1748403366926" duration="1123000" />
      <workItem from="1748584308777" duration="3330000" />
      <workItem from="1748913890727" duration="1647000" />
      <workItem from="1748917597583" duration="5298000" />
      <workItem from="1749173455290" duration="156000" />
      <workItem from="1749173640490" duration="2172000" />
      <workItem from="1749432300442" duration="2968000" />
      <workItem from="1749515888673" duration="4757000" />
      <workItem from="1749733351285" duration="1703000" />
      <workItem from="1749777160555" duration="614000" />
      <workItem from="1750159837893" duration="16378000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>