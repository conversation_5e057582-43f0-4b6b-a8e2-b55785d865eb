{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750238003355}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsJA;AAEA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACA;MACAC,kBAFA;MAGA;MACAC,YAJA;MAKAC,kBALA;MAMAC,iBANA;MAOAC;QACAC,oBADA;QAEAC;MAFA,CAPA;MAWA;MACAC;QACAC,kBADA;QAEAC,kBAFA;QAGAC,mBAHA;QAIAC,qBAJA;QAKAC,oBALA;QAMAC,kBANA;QAOAC;MAPA,CAZA;MAqBAC,QArBA;MAsBAC,aAtBA;MAuBAC,mBAvBA;MAwBAC;IAxBA;EA0BA,CAhCA;;EAiCA;IACA;MACA;MACA,wBAFA,CAGA;;MACA,iCAJA,CAIA;;MACA;MACA;QAAA;;QACA,6BACA,2KADA,EAEA,yKAFA;MAIA,CALA;;MAMAC;QAAA;;QACA,6BACA,8KADA,EAEA,yKAFA;MAIA,CALA;IAMA,CAlBA,CAkBA;MACAC;MACA;IACA,CArBA,SAqBA;MACA;IACA;EACA,CA1DA;;EA2DAC;IACA;IACA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAD;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CAlBA;;IAoBA;IACAE;MACA,wBADA,CAEA;;MACA;QAAAd;QAAAC;MAAA;MACA;QACAD,OADA;QAEAC,OAFA;QAGAC,iBAHA;QAIAC,aAJA;QAIA;QACAC,oBALA;QAMAC,0CANA;QAMA;QACAC;MAPA;MASA;IACA,CAnCA;;IAqCA;IACAS;MACA;;MACAC;QACA;MACA,CAFA;IAGA,CA3CA;;IA6CA;IACAC;MACA;;MACAD;QACA;MACA,CAFA;IAGA,CAnDA;;IAqDA;IACAE;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA,CA9DA;;IAgEA;IACAC;MACAR;MACA;MACA;IACA,CArEA;;IAuEA;IACAS;MACA;MACA,uCAFA,CAEA;;MACA,wCAHA,CAGA;;MACA,sCAJA,CAIA;;MACA,sCALA,CAKA;;MACA,sCANA,CAMA;;MAEAC,sCARA,CASA;;MACAV;IACA,CAnFA;;IAqFA;IACAW;MACAX;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA9FA;;IAgGA;IACAY;MACAZ;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAzGA;;IA2GA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA,gCANA,CAQA;;MACA;QACA;MACA,CAXA,CAaA;;;MACA,sGAdA,CAgBA;;MACAC;QACA;UACA;YACA,gCADA,CACA;;YACA;;UACA;YACA,iCADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;;UACA;YACA,+BADA,CACA;;YACA;QAfA;MAiBA,CAlBA;IAmBA,CAhJA;;IAiJAC;MACA;IACA,CAnJA;;IAqJA;IACAC;MACA;QACA5B,kBADA;QAEAC,kBAFA;QAGAC,kCAHA;QAGA;QACAC,sCAJA;QAKAC,oBALA;QAMAC,gCANA;QAMA;QACAC;MAPA;MASA;IACA,CAjKA;;IAmKA;IACAuB;MACA;QACA;MACA,CAFA,EAEAC,MAFA;IAGA,CAxKA;;IA4KA;MACA;;MACA;QACA;QACAlB,6BAFA,CAIA;;QACA;;QAEA;UACA;YACA;YACArB;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA;;UAEA;UACAqB,yCApBA,CAsBA;;UACA;YACA;UACA,CAFA;QAGA,CA1BA,MA0BA;UACAA;UACA;UACA;UACA;QACA;MACA,CAvCA,CAuCA;QACAA;QACA;QACA;QACA;MACA,CA5CA,SA4CA;QACA;MACA;IACA;;EA7NA;AA3DA", "names": ["name", "components", "SplitPane", "data", "initLoading", "treeData", "treeLoading", "selectedDir", "defaultProps", "children", "label", "searchForm", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "tableLoading", "mainH", "window", "console", "methods", "handleNodeClick", "expandAll", "nodes", "collapseAll", "expandNodes", "node", "handlePermissionChange", "updatePermLevel", "row", "selectAll", "cancelAll", "parsePermLevel", "permissions", "getSearchBtn", "resetForm", "getConfiguredCount", "length"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 数据统计信息 -->\n                            <div class=\"data-summary\" v-if=\"tableData.length > 0\">\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">总计角色：</span>\n                                    <span class=\"summary-value\">{{ total }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">已配置权限：</span>\n                                    <span class=\"summary-value\">{{ getConfiguredCount() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">未配置权限：</span>\n                                    <span class=\"summary-value\">{{ total - getConfiguredCount() }}</span>\n                                </div>\n                            </div>\n\n                            <el-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%; border-radius: 4px;\"\n                                v-loading=\"tableLoading\"\n                                :empty-text=\"'暂无数据'\"\n                                ref=\"permissionTable\">\n\n                                <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n                                <el-table-column prop=\"RoleName\" :label=\"$t('SopPermission.table.RoleName')\" min-width=\"150\" show-overflow-tooltip></el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Preview\" @input=\"handlePermissionChange(scope.row, 'Preview', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Download\" @input=\"handlePermissionChange(scope.row, 'Download', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Search\" @input=\"handlePermissionChange(scope.row, 'Search', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Upload\" @input=\"handlePermissionChange(scope.row, 'Upload', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Delete\" @input=\"handlePermissionChange(scope.row, 'Delete', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-button size=\"mini\" type=\"primary\" plain @click=\"selectAll(scope.row)\" style=\"margin-right: 4px;\">\n                                            {{ $t('SopPermission.table.SelectAll') }}\n                                        </el-button>\n                                        <el-button size=\"mini\" type=\"warning\" plain @click=\"cancelAll(scope.row)\">\n                                            {{ $t('SopPermission.table.CancelAll') }}\n                                        </el-button>\n                                    </template>\n                                </el-table-column>\n                            </el-table>\n                        </div>\n\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求根目录权限数据\n            this.searchForm.grantId = 'root'; // 设置默认查询根目录\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留查询条件，更新目标目录\n            const { dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: data.id || data.dirCode || 'root', // 使用目录ID或编码作为grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 处理权限变更\n        handlePermissionChange(row, permission, value) {\n            console.log('权限变更:', permission, value);\n            this.$set(row, permission, value);\n            this.updatePermLevel(row);\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            console.log('全选权限:', row.RoleName);\n            this.$set(row, 'Preview', true);\n            this.$set(row, 'Download', true);\n            this.$set(row, 'Search', true);\n            this.$set(row, 'Upload', true);\n            this.$set(row, 'Delete', true);\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            console.log('取消全选:', row.RoleName);\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 使用$set确保响应式\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        this.$set(row, 'Preview', true);     // 预览\n                        break;\n                    case '2':\n                        this.$set(row, 'Download', true);    // 下载\n                        break;\n                    case '3':\n                        this.$set(row, 'Search', true);      // 检索\n                        break;\n                    case '4':\n                        this.$set(row, 'Upload', true);      // 上传\n                        break;\n                    case '8':\n                        this.$set(row, 'Delete', true);      // 删除\n                        break;\n                }\n            });\n        },\n        getSearchBtn() {\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: this.searchForm.grantId, // 保留当前的grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 获取已配置权限的角色数量\n        getConfiguredCount() {\n            return this.tableData.filter(row => {\n                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;\n            }).length;\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .data-summary {\n      background: #fff;\n      border-radius: 4px;\n      padding: 12px;\n      margin-bottom: 12px;\n      display: flex;\n      justify-content: space-around;\n      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n      border: 1px solid #ebeef5;\n\n      .summary-item {\n        text-align: center;\n\n        .summary-label {\n          display: block;\n          font-size: 12px;\n          color: #606266;\n          margin-bottom: 4px;\n        }\n\n        .summary-value {\n          display: block;\n          font-size: 18px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}