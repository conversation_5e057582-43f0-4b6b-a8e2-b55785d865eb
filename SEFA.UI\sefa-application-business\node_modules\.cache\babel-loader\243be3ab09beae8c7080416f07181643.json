{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750218254563}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsEA;AAEA;AACA;AACA;AACA;AAEA;AAEA;EACAA,iBADA;EAEAC;IACAC,UADA;IAEAC;EAFA,CAFA;;EAMAC;IACA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,mBALA;QAMAC,qBANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA,CADA;MAYAC,QAZA;MAaAC,eAbA;MAcAC,uCAdA;MAeAC,aAfA;MAgBAC,cAhBA;MAiBAC,eAjBA;MAkBAC,QAlBA;MAmBAC;QACAtB,qBADA;QAEAuB,sBAFA;QAGAC,0CAHA;QAGA;QACAC,0CAJA;QAIA;QACAC,kDALA,CAKA;;MALA;IAnBA;EA2BA,CAlCA;;EAmCAC;IACA;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CA5CA;;EA6CAC;IACAC;MACA;MACA,+BAFA,CAEA;;MACA;IACA,CALA;;IAOAC;MACA;QACA;MACA;IACA,CAXA;;IAYAC;MACA;IACA,CAdA;;IAeAC;MACA;MACA;IACA,CAlBA;;IAmBAC;MACA;MACA;IACA,CAtBA;;IAuBAC;MACA;MACA;IACA,CA1BA;;IA4BAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAMAC,IANA,CAMA;QACAC;UACA;UACA;QACA,CAHA;MAIA,CAXA,EAYAC,KAZA,CAYAC;QACAC;MACA,CAdA;IAeA,CA5CA;;IA8CAC;MACAC;QACA;QACA;MACA,CAHA;IAIA;;EAnDA;AA7CA", "names": ["name", "components", "FormDialog", "SopDir", "data", "searchForm", "pageIndex", "pageSize", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "loading", "tableOption", "mainH", "buttonOption", "serveIp", "uploadUrl", "exportUrl", "DownLoadUrl", "mounted", "window", "methods", "handleDirClick", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "delRow", "title", "message", "confirmText", "cancelText", "then", "delSopPermission", "catch", "err", "console", "getTableData", "getSopPermissionList"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n                    <el-form-item>\n                        <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n                            {{ $t('DFM_JSGL._FPQX') }}\n                        </el-button>\n                    </el-form-item>\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <el-table class=\"mt-3\" :height=\"mainH\" border :data=\"tableData\" style=\"width: 100%\">\n                    <el-table-column\n                        v-for=\"item in tableName\"\n                        :default-sort=\"{ prop: 'date', order: 'descending' }\"\n                        :key=\"item.ID\"\n                        :prop=\"item.field\"\n                        :label=\"item.label\"\n                        :width=\"item.width\"\n                        :align=\"item.alignType\"\n                        sortable\n                        show-overflow-tooltip\n                    >\n                        <template slot-scope=\"scope\">\n                            {{ scope.row[item.field] }}\n                        </template>\n                    </el-table-column>\n                    <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                        <template slot-scope=\"scope\">\n                            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n                            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                        </template>\n                    </el-table-column>\n                </el-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n            <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog';\nimport SopDir from '../sopDir/index.vue';\nimport { delSopPermission, getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getTableHead } from '@/util/dataDictionary.js';\n\nimport { sopPermissionColumn } from '@/api/SOP/sopPermission.js';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        FormDialog,\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [{}],\n            hansObj: this.$t('SopPermission.table'),\n            tableName: [],\n            loading: false,\n            tableOption: [],\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getZHHans();\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        getZHHans() {\n            for (let key in this.hansObj) {\n                this.tableName = getTableHead(this.hansObj, this.tableOption);\n            }\n        },\n        showDialog(row) {\n            this.$refs.formDialog.show(row);\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        delRow(row) {\n            this.$confirms({\n                title: this.$t('GLOBAL._TS'),\n                message: this.$t('GLOBAL._COMFIRM'),\n                confirmText: this.$t('GLOBAL._QD'),\n                cancelText: this.$t('GLOBAL._QX')\n            })\n                .then(async () => {\n                    delSopPermission([row.ID]).then(res => {\n                        this.$message.success(res.msg);\n                        this.getTableData();\n                    });\n                })\n                .catch(err => {\n                    console.log(err);\n                });\n        },\n\n        getTableData(data) {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data;\n                this.total = res.response.dataCount;\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}