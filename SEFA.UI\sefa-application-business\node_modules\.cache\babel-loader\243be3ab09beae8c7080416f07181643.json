{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236826787}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoJA;AAEA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACA;MACAC,kBAFA;MAGA;MACAC,YAJA;MAKAC,kBALA;MAMAC,iBANA;MAOAC;QACAC,oBADA;QAEAC;MAFA,CAPA;MAWA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,mBALA;QAMAC,qBANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA,CAZA;MAuBAC,QAvBA;MAwBAC,aAxBA;MAyBAC,mBAzBA;MA0BAC;IA1BA;EA4BA,CAlCA;;EAmCA;IACA;MACA;MACA,wBAFA,CAGA;;MACA;MACA;QAAA;;QACA,6BACA,2KADA,EAEA,yKAFA;MAIA,CALA;;MAMAC;QAAA;;QACA,6BACA,8KADA,EAEA,yKAFA;MAIA,CALA;IAMA,CAjBA,CAiBA;MACAC;MACA;IACA,CApBA,SAoBA;MACA;IACA;EACA,CA3DA;;EA4DAC;IACA;IACA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAD;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CAlBA;;IAoBA;IACAE;MACA,wBADA,CAEA;;MACA;QAAAhB;QAAAC;QAAAC;QAAAC;MAAA;MACA;QACAH,SADA;QAEAC,QAFA;QAGAC,OAHA;QAIAC,OAJA;QAKAC,iBALA;QAMAC,aANA;QAMA;QACAC,oBAPA;QAQAC,kBARA;QASAC;MATA;MAWA;IACA,CArCA;;IAuCA;IACAS;MACA;;MACAC;QACA;MACA,CAFA;IAGA,CA7CA;;IA+CA;IACAC;MACA;;MACAD;QACA;MACA,CAFA;IAGA,CArDA;;IAuDA;IACAE;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA,CAhEA;;IAkEA;IACAC;MACA;MACA,uCAFA,CAEA;;MACA,wCAHA,CAGA;;MACA,sCAJA,CAIA;;MACA,sCALA,CAKA;;MACA,sCANA,CAMA;;MAEAC,sCARA,CASA;;MACAT;IACA,CA9EA;;IAgFA;IACAU;MACAD;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAxFA;;IA0FA;IACAE;MACAF;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAlGA;;IAoGA;IACAG;MACA;MACAH;MACAA;MACAA;MACAA;MACAA,mBANA,CAQA;;MACA;QACA;MACA,CAXA,CAaA;;;MACA,sGAdA,CAgBA;;MACAI;QACA;UACA;YACAJ,mBADA,CACA;;YACA;;UACA;YACAA,oBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;QAfA;MAiBA,CAlBA;IAmBA,CAzIA;;IA0IAK;MACA;MACA;IACA,CA7IA;;IA8IAC;MACA;MACA;IACA,CAjJA;;IAkJAC;MACA;MACA;IACA,CArJA;;IAuJA;IACAC;MACA;QACA/B,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,kCALA;QAKA;QACAC,sCANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA;MAWA;IACA,CArKA;;IAyKA;MACA;;MACA;QACA;QACAM,6BAFA,CAIA;;QACA;;QAEA;UACA;YACA;YACAvB;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA,CAJA,MAIA;YACA;YACAA;YACA;UACA;;UAEA;UACAuB,yCApBA,CAsBA;;UACA;YACA;UACA,CAFA;QAGA,CA1BA,MA0BA;UACAA;UACA;UACA;UACA;QACA;MACA,CAvCA,CAuCA;QACAA;QACA;QACA;QACA;MACA,CA5CA,SA4CA;QACA;MACA;IACA;;EA1NA;AA5DA", "names": ["name", "components", "SplitPane", "data", "initLoading", "treeData", "treeLoading", "selectedDir", "defaultProps", "children", "label", "searchForm", "pageIndex", "pageSize", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "tableLoading", "mainH", "window", "console", "methods", "handleNodeClick", "expandAll", "nodes", "collapseAll", "expandNodes", "node", "updatePermLevel", "row", "selectAll", "cancelAll", "parsePermLevel", "permissions", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "resetForm"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 调试信息 -->\n                            <div v-if=\"!tableLoading && tableData.length === 0\" style=\"text-align: center; padding: 20px; color: #999;\">\n                                暂无数据\n                            </div>\n                            <div v-if=\"tableData.length > 0\" style=\"margin-bottom: 8px; font-size: 12px; color: #666;\">\n                                共 {{ total }} 条数据\n                            </div>\n\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求权限数据\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}