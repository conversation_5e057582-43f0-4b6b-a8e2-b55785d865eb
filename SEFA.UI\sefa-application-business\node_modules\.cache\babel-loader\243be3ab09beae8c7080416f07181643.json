{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236593164}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA4IA;AAEA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACA;MACAC,kBAFA;MAGA;MACAC,YAJA;MAKAC,kBALA;MAMAC,iBANA;MAOAC;QACAC,oBADA;QAEAC;MAFA,CAPA;MAWA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,mBALA;QAMAC,qBANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA,CAZA;MAuBAC,QAvBA;MAwBAC,aAxBA;MAyBAC,mBAzBA;MA0BAC;IA1BA;EA4BA,CAlCA;;EAmCA;IACA;MACA;MACA;MACA;QAAA;;QACA,6BACA,2KADA,EAEA,yKAFA;MAIA,CALA;;MAMAC;QAAA;;QACA,6BACA,8KADA,EAEA,yKAFA;MAIA,CALA;IAMA,CAfA,CAeA;MACAC;MACA;IACA,CAlBA,SAkBA;MACA;IACA;EACA,CAzDA;;EA0DAC;IACA;IACA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAD;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CAlBA;;IAoBA;IACAE;MACA,wBADA,CAEA;;MACA;QAAAhB;QAAAC;QAAAC;QAAAC;MAAA;MACA;QACAH,SADA;QAEAC,QAFA;QAGAC,OAHA;QAIAC,OAJA;QAKAC,iBALA;QAMAC,aANA;QAMA;QACAC,oBAPA;QAQAC,kBARA;QASAC;MATA;MAWA;IACA,CArCA;;IAuCA;IACAS;MACA;;MACAC;QACA;MACA,CAFA;IAGA,CA7CA;;IA+CA;IACAC;MACA;;MACAD;QACA;MACA,CAFA;IAGA,CArDA;;IAuDA;IACAE;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA,CAhEA;;IAkEA;IACAC;MACA;MACA,uCAFA,CAEA;;MACA,wCAHA,CAGA;;MACA,sCAJA,CAIA;;MACA,sCALA,CAKA;;MACA,sCANA,CAMA;;MAEAC,sCARA,CASA;;MACAT;IACA,CA9EA;;IAgFA;IACAU;MACAD;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAxFA;;IA0FA;IACAE;MACAF;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAlGA;;IAoGA;IACAG;MACA;MACAH;MACAA;MACAA;MACAA;MACAA;MAEA,uBARA,CAUA;;MACA,sFAXA,CAaA;;MACAI;QACA;UACA;YACAJ,mBADA,CACA;;YACA;;UACA;YACAA,oBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;;UACA;YACAA,kBADA,CACA;;YACA;QAfA;MAiBA,CAlBA;IAmBA,CAtIA;;IAuIAK;MACA;MACA;IACA,CA1IA;;IA2IAC;MACA;MACA;IACA,CA9IA;;IA+IAC;MACA;MACA;IACA,CAlJA;;IAoJA;IACAC;MACA;QACA/B,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,kCALA;QAKA;QACAC,sCANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA;MAWA;IACA,CAlKA;;IAsKA;MACA;QACA;QACA;QACA;MACA;;MAEA;;MACA;QACA;;QACA;UACA;UACA,yCAFA,CAIA;;UACA;YACA;UACA,CAFA;QAGA,CARA,MAQA;UACA;QACA;MACA,CAbA,CAaA;QACAM,gCADA,CAEA;;QACA,kBACA;UACAkB,OADA;UAEAC,eAFA;UAGAC,sBAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CADA,EASA;UACAL,OADA;UAEAC,gBAFA;UAGAC,kBAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CATA,EAiBA;UACAL,OADA;UAEAC,cAFA;UAGAC,cAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CAjBA,EAyBA;UACAL,OADA;UAEAC,eAFA;UAGAC,oBAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CAzBA;QAkCA,mCArCA,CAuCA;;QACA;UACA;QACA,CAFA;MAGA,CAxDA,SAwDA;QACA;MACA;IACA;;EAzOA;AA1DA", "names": ["name", "components", "SplitPane", "data", "initLoading", "treeData", "treeLoading", "selectedDir", "defaultProps", "children", "label", "searchForm", "pageIndex", "pageSize", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "tableLoading", "mainH", "window", "console", "methods", "handleNodeClick", "expandAll", "nodes", "collapseAll", "expandNodes", "node", "updatePermLevel", "row", "selectAll", "cancelAll", "parsePermLevel", "permissions", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "resetForm", "ID", "RoleName", "PermLevel", "TargetId", "GrantType", "GrantId"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            if (!permLevel) return;\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p);\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            if (!this.searchForm.targetId) {\n                this.tableData = [];\n                this.total = 0;\n                return;\n            }\n\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                if (res.success) {\n                    this.tableData = res.response.data || [];\n                    this.total = res.response.dataCount || 0;\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    this.$message.error(res.msg || '获取权限数据失败');\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '1,2,3,4,8', // 所有权限：预览,下载,检索,上传,删除\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '1,2,3', // 预览,下载,检索\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 仅预览\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    },\n                    {\n                        ID: '4',\n                        RoleName: '编辑者',\n                        PermLevel: '1,2,3,4', // 预览,下载,检索,上传\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'editor-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}