{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236171338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAsHA;AAEA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACA;MACAC,kBAFA;MAGA;MACAC,YAJA;MAKAC,kBALA;MAMAC,iBANA;MAOAC;QACAC,oBADA;QAEAC;MAFA,CAPA;MAWA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,mBAHA;QAIAC,qBAJA;QAKAC,oBALA;QAMAC,kBANA;QAOAC;MAPA,CAZA;MAqBAC,QArBA;MAsBAC,aAtBA;MAuBAC,mBAvBA;MAwBAC;IAxBA;EA0BA,CAhCA;;EAiCA;IACA;MACA;MACA;MACA;QAAA;;QACA,6BACA,2KADA,EAEA,yKAFA;MAIA,CALA;;MAMAC;QAAA;;QACA,6BACA,8KADA,EAEA,yKAFA;MAIA,CALA;IAMA,CAfA,CAeA;MACAC;MACA;IACA,CAlBA,SAkBA;MACA;IACA;EACA,CAvDA;;EAwDAC;IACA;IACA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAD;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CAlBA;;IAoBA;IACAE;MACA,wBADA,CAEA;;MACA;QAAAd;QAAAC;MAAA;MACA;QACAD,SADA;QAEAC,QAFA;QAGAC,iBAHA;QAIAC,aAJA;QAIA;QACAC,oBALA;QAMAC,kBANA;QAOAC;MAPA;MASA;IACA,CAnCA;;IAqCA;IACAS;MACA;;MACAC;QACA;MACA,CAFA;IAGA,CA3CA;;IA6CA;IACAC;MACA;;MACAD;QACA;MACA,CAFA;IAGA,CAnDA;;IAqDA;IACAE;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA,CA9DA;;IAgEA;IACAC;MACA;MACA,gCAFA,CAEA;;MACA,iCAHA,CAGA;;MACA,+BAJA,CAIA;;MACA,+BALA,CAKA;;MACA,+BANA,CAMA;;MAEAC,qCARA,CASA;;MACAT;IACA,CA5EA;;IA8EA;IACAU;MACAD;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAtFA;;IAwFA;IACAE;MACAF;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAhGA;;IAkGA;IACAG;MACA;MACAH,8BAFA,CAEA;;MACAA,+BAHA,CAGA;;MACAA,6BAJA,CAIA;;MACAA,6BALA,CAKA;;MACAA,6BANA,CAMA;IACA,CA1GA;;IA2GAI;MACA;MACA;IACA,CA9GA;;IA+GAC;MACA;MACA;IACA,CAlHA;;IAmHAC;MACA;MACA;IACA,CAtHA;;IA0HA;MACA;QACA;QACA;QACA;MACA;;MAEA;;MACA;QACA;;QACA;UACA;UACA,yCAFA,CAIA;;UACA;YACA;UACA,CAFA;QAGA,CARA,MAQA;UACA;QACA;MACA,CAbA,CAaA;QACAf,gCADA,CAEA;;QACA,kBACA;UACAgB,OADA;UAEAC,eAFA;UAGAC,eAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CADA,EASA;UACAL,OADA;UAEAC,gBAFA;UAGAC,cAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CATA,EAiBA;UACAL,OADA;UAEAC,cAFA;UAGAC,cAHA;UAGA;UACAC,kCAJA;UAKAC,YALA;UAMAC;QANA,CAjBA;QA0BA,mCA7BA,CA+BA;;QACA;UACA;QACA,CAFA;MAGA,CAhDA,SAgDA;QACA;MACA;IACA;;EArLA;AAxDA", "names": ["name", "components", "SplitPane", "data", "initLoading", "treeData", "treeLoading", "selectedDir", "defaultProps", "children", "label", "searchForm", "pageIndex", "pageSize", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "tableLoading", "mainH", "window", "console", "methods", "handleNodeClick", "expandAll", "nodes", "collapseAll", "expandNodes", "node", "updatePermLevel", "row", "selectAll", "cancelAll", "parsePermLevel", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "ID", "RoleName", "PermLevel", "TargetId", "GrantType", "GrantId"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"root-head\">\n                            <div class=\"selected-dir-info\" v-if=\"selectedDir\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置,重置其他搜索条件\n            const { pageIndex, pageSize } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permLevel = 0;\n            if (row.Preview) permLevel |= 1;    // 预览 (1)\n            if (row.Download) permLevel |= 2;   // 下载 (2)\n            if (row.Search) permLevel |= 3;     // 检索 (3)\n            if (row.Upload) permLevel |= 4;     // 上传 (4)\n            if (row.Delete) permLevel |= 8;     // 删除 (8)\n\n            row.PermLevel = permLevel.toString();\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            const level = parseInt(permLevel) || 0;\n            row.Preview = (level & 1) > 0;      // 预览 (1)\n            row.Download = (level & 2) > 0;     // 下载 (2)\n            row.Search = (level & 3) > 0;       // 检索 (3)\n            row.Upload = (level & 4) > 0;       // 上传 (4)\n            row.Delete = (level & 8) > 0;       // 删除 (8)\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            if (!this.searchForm.targetId) {\n                this.tableData = [];\n                this.total = 0;\n                return;\n            }\n\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                if (res.success) {\n                    this.tableData = res.response.data || [];\n                    this.total = res.response.dataCount || 0;\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    this.$message.error(res.msg || '获取权限数据失败');\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '3', // 1+2 = 3 (预览+下载)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 1 (仅预览)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}