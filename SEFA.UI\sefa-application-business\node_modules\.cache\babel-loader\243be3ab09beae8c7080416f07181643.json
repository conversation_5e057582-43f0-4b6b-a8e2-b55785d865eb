{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750235480077}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2FA;AAEA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,mBALA;QAMAC,qBANA;QAOAC,oBAPA;QAQAC,kBARA;QASAC;MATA,CADA;MAYAC,QAZA;MAaAC,aAbA;MAcAC,cAdA;MAeAC,QAfA;MAgBAC;QACAlB,qBADA;QAEAmB,sBAFA;QAGAC,0CAHA;QAGA;QACAC,0CAJA;QAIA;QACAC,kDALA,CAKA;;MALA;IAhBA;EAwBA,CA9BA;;EA+BAC;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CAvCA;;EAwCAC;IACAC;MACA;MACA,+BAFA,CAEA;;MACA;IACA,CALA;;IAOA;IACAC;MACA;MACA,gCAFA,CAEA;;MACA,iCAHA,CAGA;;MACA,+BAJA,CAIA;;MACA,+BALA,CAKA;;MACA,+BANA,CAMA;;MAEAC,qCARA,CASA;;MACAC;IACA,CAnBA;;IAqBA;IACAC;MACAF;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CA7BA;;IA+BA;IACAG;MACAH;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAvCA;;IAyCA;IACAI;MACA;MACAJ,8BAFA,CAEA;;MACAA,+BAHA,CAGA;;MACAA,6BAJA,CAIA;;MACAA,6BALA,CAKA;;MACAA,6BANA,CAMA;IACA,CAjDA;;IAkDAK;MACA;MACA;IACA,CArDA;;IAsDAC;MACA;MACA;IACA,CAzDA;;IA0DAC;MACA;MACA;IACA,CA7DA;;IAiEAC;MACAC;QACA;QACA,yCAFA,CAIA;;QACA;UACA;QACA,CAFA;MAGA,CARA,EAQAC,KARA,CAQAC;QACAV,gCADA,CAEA;;QACA,kBACA;UACAW,OADA;UAEAC,eAFA;UAGAC,eAHA;UAGA;UACAC,yBAJA;UAKAC,YALA;UAMAC;QANA,CADA,EASA;UACAL,OADA;UAEAC,gBAFA;UAGAC,cAHA;UAGA;UACAC,yBAJA;UAKAC,YALA;UAMAC;QANA,CATA,EAiBA;UACAL,OADA;UAEAC,cAFA;UAGAC,cAHA;UAGA;UACAC,yBAJA;UAKAC,YALA;UAMAC;QANA,CAjBA;QA0BA,mCA7BA,CA+BA;;QACA;UACA;QACA,CAFA;MAGA,CA3CA;IA4CA;;EA9GA;AAxCA", "names": ["name", "components", "SopDir", "data", "searchForm", "pageIndex", "pageSize", "<PERSON><PERSON><PERSON>", "dirCode", "targetId", "targetType", "grantType", "grantId", "permLevel", "total", "tableData", "loading", "mainH", "buttonOption", "serveIp", "uploadUrl", "exportUrl", "DownLoadUrl", "mounted", "window", "methods", "handleDirClick", "updatePermLevel", "row", "console", "selectAll", "cancelAll", "parsePermLevel", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "getTableData", "getSopPermissionList", "catch", "err", "ID", "RoleName", "PermLevel", "TargetId", "GrantType", "GrantId"], "sourceRoot": "src/views/SOP/sopPermission", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <vxe-table\n                    class=\"mt-3\"\n                    :height=\"mainH\"\n                    border\n                    :data=\"tableData\"\n                    style=\"width: 100%\"\n                    :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                    ref=\"xTable\">\n\n                    <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                    <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                            <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                        </template>\n                    </vxe-column>\n                </vxe-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SopDir from '../sopDir/index.vue';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            loading: false,\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permLevel = 0;\n            if (row.Preview) permLevel |= 1;    // 预览 (1)\n            if (row.Download) permLevel |= 2;   // 下载 (2)\n            if (row.Search) permLevel |= 3;     // 检索 (3)\n            if (row.Upload) permLevel |= 4;     // 上传 (4)\n            if (row.Delete) permLevel |= 8;     // 删除 (8)\n\n            row.PermLevel = permLevel.toString();\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            const level = parseInt(permLevel) || 0;\n            row.Preview = (level & 1) > 0;      // 预览 (1)\n            row.Download = (level & 2) > 0;     // 下载 (2)\n            row.Search = (level & 3) > 0;       // 检索 (3)\n            row.Upload = (level & 4) > 0;       // 上传 (4)\n            row.Delete = (level & 8) > 0;       // 删除 (8)\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n\n\n        getTableData() {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data || [];\n                this.total = res.response.dataCount || 0;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            }).catch(err => {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '3', // 1+2 = 3 (预览+下载)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 1 (仅预览)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}