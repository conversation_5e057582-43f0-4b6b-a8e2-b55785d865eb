{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue", "mtime": 1750231625148}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";AA4EA,SACAA,eADA,EAEAC,cAFA,EAGAC,aAHA,QAIA,kBAJA;AAKA;AACA;AACA;AAEA;EACAC,wBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC;QACAC,WADA;QAEAC,WAFA;QAGAC,WAHA;QAIAC;MAJA,CADA;MAOAC;QACAH,UACA;UAAAI;UAAAC;UAAAC;QAAA,CADA,EAEA;UAAAC;UAAAC;UAAAH;UAAAC;QAAA,CAFA,CADA;QAKAL,UACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,EAEA;UAAAG;UAAAJ;UAAAC;QAAA,CAFA,CALA;QASAJ,cACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MATA,CAPA;MAoBAI,oBApBA;MAqBAC,kBArBA;MAsBAC,cAtBA;MAuBAC,gBAvBA;MAuBA;MACAC,YAxBA;MAwBA;MACAC,qBAzBA;MA0BAC,gBA1BA;MA2BAC,aA3BA;MA4BAC;QACAC,WADA;QAEAC,mBAFA;QAGAC;MAHA;IA5BA;EAkCA,CAxCA;;EAyCAC;IACA;MACA;QACA;QACA;QACA;;QACA;UACA;UACA;UACA;QACA,CAJA,MAIA;UACA;QACA;MACA,CAXA,CAWA;QACAC;QACA;MACA,CAdA,SAcA;QACA;MACA;IACA,CAnBA;;IAoBA;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAA;QACA;MACA;IACA,CAhCA;;IAiCA;MACA;QACA;QACA;QAEA;UACAxB,sCADA;UACA;UACAC,WAFA;UAGAC,WAHA;UAIAC;QAJA;QAMA;QACA;QACA;MACA;;MAEA;;MACA;QACA;UACAiB,WADA;UAEApB,4CAFA;UAEA;UACAC,kBAHA;UAIAC,mBAJA;UAKAC;QALA;QAOA;MACA;;MACA;IACA,CA7DA;;IA8DA;MACA;QACA;;QACA;UACA,qCADA,CAEA;;UACA;YACA;UACA;QACA,CANA,MAMA;UACA;QACA;MACA,CAXA,CAWA;QACAqB;QACA;MACA;IACA,CA9EA;;IA+EAC;MACA,2BADA,CAEA;;MACA;QACA;MACA,CAFA;IAGA,CArFA;;IAuFA;IACAC;MACA;QACAF,oCADA,CACA;;QAEA;UACA;QACA;;QAEA;QACA,6GARA,CAUA;;QACA;UACA;UACA;QACA,CAHA;MAKA,CAhBA,CAgBA;QACAA;QACA;QACA;MACA;IACA;;EA9GA;AAzCA", "names": ["getSopDirDetail", "saveSopDirForm", "getSopDirTree", "name", "components", "SelectUser", "data", "dialogForm", "parentId", "<PERSON><PERSON><PERSON>", "dirCode", "ownerUserid", "rules", "required", "message", "trigger", "min", "max", "pattern", "dialogVisible", "formLoading", "currentRow", "isRootDir", "dataList", "showUserDialog", "selected<PERSON>ser", "parentDir", "normalizer", "id", "label", "children", "methods", "console", "handleClose", "handleSelectUser"], "sourceRoot": "src/views/SOP/sopDir", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n  <div class=\"sop-dir-form\">\n    <el-dialog\n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :modal=\"true\"\n      :append-to-body=\"true\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"handleClose\"\n    >\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"父级目录\" prop=\"parentId\">\n                <span class=\"parent-dir\" :title=\"parentDir\">{{ parentDir || '未选择父级目录' }}</span>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                <el-input v-model=\"dialogForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                <el-input v-model=\"dialogForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"负责人\" prop=\"ownerUserid\">\n                <div class=\"user-selector\">\n                  <span class=\"user-text\" :title=\"selectedUser\">{{ selectedUser || '未选择负责人' }}</span>\n                  <el-button \n                    type=\"primary\" \n                    size=\"small\" \n                    @click=\"showUserDialog = true\"\n                    class=\"select-btn\"\n                    icon=\"el-icon-user\">\n                    选择\n                  </el-button>\n                </div>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button-group>\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\"\n            v-loading=\"formLoading\" \n            :disabled=\"formLoading\" \n            element-loading-spinner=\"el-icon-loading\"\n            @click=\"submit()\">\n            确 定\n          </el-button>\n        </el-button-group>\n      </div>\n    </el-dialog>\n    <select-user \n      :visible.sync=\"showUserDialog\" \n      @select-user=\"handleSelectUser\"\n      v-if=\"dialogVisible\"\n    />\n\n  </div>\n</template>\n  \n<script>\nimport {\n  getSopDirDetail,\n  saveSopDirForm,\n  getSopDirTree\n} from \"@/api/SOP/sopDir\";\nimport Treeselect from '@riophae/vue-treeselect'\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\nimport SelectUser from './components/selectUser.vue'\n\nexport default {\n  name: 'SopDirFormDialog',\n  components: {\n    SelectUser\n  },\n  data() {\n    return {\n      dialogForm: {\n        parentId: 0,\n        dirName: '',\n        dirCode: '',\n        ownerUserid: ''\n      },\n      rules: {\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: 'blur' },\n          { min: 2, max: 50, message: this.$t('SOP.DirNameLength'), trigger: 'blur' }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: 'blur' },\n          { pattern: /^[A-Za-z0-9-_]+$/, message: this.$t('SOP.DirCodeFormat'), trigger: 'blur' }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      currentRow: {},\n      isRootDir: false, // 是否新增根目录\n      dataList: [], // 目录树数据\n      showUserDialog: false,\n      selectedUser: '',\n      parentDir: '',\n      normalizer: node => ({\n        id: node.ID,\n        label: node.DirName,\n        children: node.Children\n      })\n    }\n  },\n  methods: {\n    async submit() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n        const res = await saveSopDirForm(this.dialogForm)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    async loadTreeData() {\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.dataList = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n      }\n    },\n    async show(data, type) {\n      if(type === 'add'){\n        // 判断是否为新增根目录\n        this.isRootDir = !data.id && !data.parentId && !data.parentId != '0'\n\n        this.dialogForm = {\n          parentId: this.isRootDir ? 0 : data.id, // 根目录parentId为0，否则为选中节点的id\n          dirName: '',\n          dirCode: '',\n          ownerUserid: ''\n        }\n        this.selectedUser = ''\n        this.parentDir = this.isRootDir ? '' : data.value + '-' + data.name\n        this.currentRow = data\n      }\n\n      debugger;\n      if(type === 'show'){\n        this.dialogForm = {\n          id: data.id,\n          parentId: this.isRootDir ? 0 : data.parentId, // 根目录parentId为0，否则为选中节点的id\n          dirName: data.name,\n          dirCode: data.value,\n          ownerUserid: data.remark\n        }\n        this.selectedUser = data.extendField\n      }      \n      this.dialogVisible = true      \n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDirDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          // 如果有负责人信息，设置显示名称\n          if (this.dialogForm.ownerUserid && this.dialogForm.ownerUserName) {\n            this.selectedUser = this.dialogForm.ownerUserName\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n      // 添加表单重置逻辑\n      this.$nextTick(() => {\n        this.$refs.dialogForm.clearValidate()\n      })\n    },\n    \n    // 处理用户选择\n    handleSelectUser(user) {\n      try {\n        console.log('Selected user:', user) // 调试日志\n        \n        if (!user || !user.ID) {\n          throw new Error('无效的用户选择')\n        }\n\n        this.dialogForm.ownerUserid = user.LoginName\n        this.selectedUser = `${user.DepartmentName || ''} - ${user.LoginName || ''} - ${user.UserName || ''}`.trim()\n        \n        // 关闭对话框并验证表单\n        this.$nextTick(() => {\n          this.showUserDialog = false\n          this.$refs.dialogForm.validateField('ownerUserid')\n        })\n        \n      } catch (error) {\n        console.error('处理用户选择失败:', error)\n        this.$message.error('选择负责人失败: ' + (error.message || '未知错误'))\n        this.showUserDialog = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-dir-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 10px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n    }\n  }\n\n  .form-body {\n    padding: 10px 0;\n\n    .el-form-item {\n      margin-bottom: 18px;\n    }\n  }\n\n  .parent-dir {\n    display: block;\n    padding: 5px 12px;\n    line-height: 1.5;\n    border: 1px solid #dcdfe6;\n    border-radius: 4px;\n    background-color: #f5f7fa;\n    color: #606266;\n  }\n\n  .user-selector {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    \n    .user-text {\n      flex: 1;\n      padding: 5px 12px;\n      border: 1px solid #dcdfe6;\n      border-radius: 4px;\n      background-color: #f5f7fa;\n      color: #606266;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    \n    .select-btn {\n      flex-shrink: 0;\n      width: 80px;\n      padding: 7px 12px;\n    }\n  }\n}\n</style>\n"]}]}