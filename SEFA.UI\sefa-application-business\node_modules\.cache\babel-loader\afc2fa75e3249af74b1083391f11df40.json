{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue", "mtime": 1750225849524}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAwGA,SACAA,cADA,EAEAC,UAFA,EAGAC,SAHA,EAIAC,SAJA,EAKAC,YALA,EAMAC,SANA,QAOA,qBAPA;AAQA;AACA;AAEA;EACAC,cADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,mBADA;MAEAC,uBAFA;MAGA;MACAC,OAJA;MAKA;MACAC,YANA;MAOA;MACAC,cARA;MASA;MACAC,cAVA;MAWA;MACAC,gBAZA;MAaA;MACAC;QACAC,UADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC;MALA,CAdA;MAqBA;MACAC,SAtBA;MAuBA;MACAC,WAxBA;MAyBA;MACAC,WA1BA;MA2BA;MACAC,kBA5BA;MA6BA;MACAC,kBA9BA;MA+BA;MACAC,QAhCA;MAiCAC,UACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,CAXA,CAjCA;MA8CA;MACAC,mBA/CA;MAgDA;MACAC,sBAjDA;MAkDAC,YAlDA;MAmDAC,QAnDA;MAoDAC;QACAC,WACA;UAAAC;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAtB,UACA;UAAAoB;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOArB,UACA;UAAAmB;UAAAC;UAAAC;QAAA,CADA,CAPA;QAUApB,cACA;UAAAkB;UAAAC;UAAAC;QAAA,CADA;MAVA;IApDA;EAmEA,CAzEA;;EA0EAC;IACA;IACA;EACA,CA7EA;;EA8EAC;IACA;IACAC;MACA;MACA7C;QACA;UACA;UACA;QACA;MACA,CALA;IAMA,CAVA;;IAWA;IACA8C;MACA;QACA;MACA;;MACA;QACAC,WADA;QAEAf,mBAFA;QAGAgB;MAHA;IAKA,CArBA;;IAsBA;IACAC;MACA;MACA;IACA,CA1BA;;IA2BA;IACAC;MACA;QACAH,aADA;QAEAR,mBAFA;QAGAnB,kBAHA;QAIAC,kBAJA;QAKAC,sBALA;QAMA6B,qBANA;QAOAC,uBAPA;QAQAC,qBARA;QASAC,uBATA;QAUAC,0BAVA;QAWAC;MAXA;MAaA;IACA,CA3CA;;IA4CA;IACAC;MACA;MACA;MACA;IACA,CAjDA;;IAkDA;IACAC;MACA;MACA;MACA;IACA,CAvDA;;IAwDA;IACAC;MACA;MACA;IACA,CA5DA;;IA6DA;IACAC;MACA;MACA;MACA;MACA;IACA,CAnEA;;IAoEA;IACAC;MACA;MAEA,+CACAC,IADA,CACA;QACA;MACA,CAHA,EAIAA,IAJA,CAIA;QACA;QACA;MACA,CAPA,EAQAC,KARA,CAQA,QARA;IASA,CAjFA;;IAkFA;IACAC;MACA;MACA;MACA3D;QACA;UAAA4D;UAAAxD;QAAA;;QACA;UACA;UACA;UACA;UAEA,cACA;UADA;QAGA;MACA,CAXA;IAYA,CAlGA;;IAmGA;IACAyD;MACA;QACA;UACA;YACA9D,wBACA0D,IADA,CACAK;cACA;cACA;cACA;YACA,CALA;UAMA,CAPA,MAOA;YACAjE,qBACA4D,IADA,CACAK;cACA;cACA;cACA;YACA,CALA;UAMA;QACA;MACA,CAlBA;IAmBA,CAxHA;;IAyHA;IACAC;MACA;MACA;MACA;QACA;MACA,CAFA;IAGA;;EAhIA;AA9EA", "names": ["treelistSopDir", "listSopDir", "addSopDir", "delSopDir", "updateSopDir", "getSopDir", "name", "components", "UserSelect", "data", "labelWidth", "form<PERSON>abe<PERSON><PERSON>", "ids", "single", "multiple", "loading", "showSearch", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "dirCode", "ownerUserid", "title", "opertype", "open", "isExpandAll", "refreshTable", "form", "columns", "index", "key", "label", "checked", "parentIdOptions", "ownerUseridOptions", "dataList", "total", "rules", "parentId", "required", "message", "trigger", "created", "methods", "getList", "normalizer", "id", "children", "cancel", "reset", "createdate", "createuserid", "modifydate", "modifyuserid", "updatetimestamp", "deleted", "reset<PERSON><PERSON>y", "handleSelectionChange", "handleQuery", "handleAdd", "handleDelete", "then", "catch", "handleUpdate", "code", "submitForm", "res", "toggleExpandAll"], "sourceRoot": "src/views/SOP/sopDir", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"search-container\">\n      <el-form :model=\"queryParams\" size=\"small\" label-position=\"right\" inline ref=\"queryForm\" :label-width=\"labelWidth\" \n        v-show=\"showSearch\" @submit.native.prevent>\n        \n        <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n          <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n          <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirOwner')\" prop=\"ownerUserid\">\n          <user-select v-model=\"searchForm.ownerUserid\" />\n        </el-form-item>\n\n        <el-form-item>\n          <el-button-group>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-button-group>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"button-group\">\n        <el-button-group>\n          <el-button \n            type=\"primary\" \n            v-hasPermi=\"['DFM:SOP:add']\" \n            plain \n            icon=\"el-icon-plus\" \n            size=\"mini\"\n            @click=\"handleAdd\">新增</el-button>\n          <el-button \n            type=\"info\" \n            plain \n            icon=\"el-icon-sort\" \n            size=\"mini\"\n            @click=\"toggleExpandAll\">展开/折叠</el-button>\n          <el-button \n            type=\"danger\" \n            :disabled=\"multiple\" \n            v-hasPermi=\"['DFM:SOP:delete']\" \n            plain \n            icon=\"el-icon-delete\" \n            size=\"mini\"\n            @click=\"handleDelete\">删除</el-button>\n        </el-button-group>\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n      </div>\n    </div>\n\n    <!-- 数据区域 -->\n    <el-table v-if=\"refreshTable\" :data=\"dataList\" v-loading=\"loading\" ref=\"table\" border highlight-current-row @selection-change=\"handleSelectionChange\"\n      :default-expand-all=\"isExpandAll\" row-key=\"id\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n      <el-table-column prop=\"parentId\" label=\"父目录ID\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\" parentIdOptions\" :value=\"scope.row.parentId\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"dirName\" :label=\"$t('SOP.DirName')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"dirCode\" :label=\"$t('SOP.DirCode')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"ownerUserid\" :label=\"$t('SOP.DirOwner')\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.ownerUserName || scope.row.ownerUserid }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"createdate\" label=\"创建时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"createuserid\" label=\"创建人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifydate\" label=\"修改时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifyuserid\" label=\"修改人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"deleted\" label=\"删除标记(0-未删 1-已删)\" align=\"center\" />\n\n      <el-table-column label=\"操作\" align=\"center\" width=\"140\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:edit']\" type=\"success\" icon=\"el-icon-edit\" title=\"编辑\" \n            @click=\"handleUpdate(scope.row)\"></el-button>\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:delete']\" type=\"danger\" icon=\"el-icon-delete\" title=\"删除\" \n            @click=\"handleDelete(scope.row)\"></el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination class=\"mt10\" background :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n\n    <!-- 添加或修改SOP文档目录树结构表对话框 -->\n    <el-dialog :title=\"title\" :lock-scroll=\"false\" :visible.sync=\"open\" >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :label-width=\"formLabelWidth\">\n        <el-row :gutter=\"20\">\n        \n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"text\" @click=\"cancel\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { \n  treelistSopDir,\n  listSopDir,\n  addSopDir,\n  delSopDir,\n  updateSopDir,\n  getSopDir,\n} from '@/api/SOP/sopDir.js';\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport UserSelect from '@/components/UserSelect';\n\nexport default {\n  name: \"sopdir\",\n  components: { \n    UserSelect \n  },\n  data() {\n    return {\n      labelWidth: \"100px\",\n      formLabelWidth:\"100px\",\n      // 选中id数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 遮罩层\n      loading: false,\n      // 显示搜索条件\n      showSearch: true,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n      },\n      // 弹出层标题\n      title: \"\",\n      // 操作类型 1、add 2、edit\n      opertype: 0,\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 表单参数\n      form: {},\n      columns: [\n        { index: 0, key: 'id', label: this.$t('SOP.PrimaryKey'), checked:  true  },\n        { index: 1, key: 'parentId', label: this.$t('SOP.ParentDirId'), checked:  true  },\n        { index: 2, key: 'dirName', label: this.$t('SOP.DirName'), checked:  true  },\n        { index: 3, key: 'dirCode', label: this.$t('SOP.DirCode'), checked:  true  },\n        { index: 4, key: 'ownerUserid', label: this.$t('SOP.DirOwnerId'), checked:  true  },\n        { index: 5, key: 'createdate', label: this.$t('SOP.CreateTime'), checked:  true  },\n        { index: 6, key: 'createuserid', label: this.$t('SOP.CreateUserId'), checked:  true  },\n        { index: 7, key: 'modifydate', label: this.$t('SOP.ModifyTime'), checked:  true  },\n        { index: 8, key: 'modifyuserid', label: this.$t('SOP.ModifyUserId'), checked:  true  },\n        { index: 9, key: 'updatetimestamp', label: this.$t('SOP.Timestamp'), checked:  false  },\n        { index: 10, key: 'deleted', label: this.$t('SOP.DeleteFlag'), checked:  false  },\n      ],\n      // 父目录ID选项列表\n      parentIdOptions: [],\n      // 目录负责人ID选项列表\n      ownerUseridOptions: [],\n      dataList: [],\n      total: 0,\n      rules: {\n        parentId: [\n          { required: true, message: this.$t('SOP.ParentDirIdRequired'), trigger: \"change\" }\n        ],\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: \"blur\" }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: \"blur\" }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: \"change\" }\n        ],\n      },\n    };\n  },\n  created() {\n    // 列表数据查询\n    this.getList();\n  },\n  methods: {\n    // 查询数据\n    getList() {\n      this.loading = true;\n      treelistSopDir(this.queryParams).then(res => {\n         if (res.code == 200) {\n           this.dataList = res.data;\n           this.loading = false;\n         }\n       })\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.id,\n        label: node.dirName,\n        children: node.children,\n      };\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 重置数据表单\n    reset() {\n      this.form = {\n        id: undefined,\n        parentId: undefined,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n        createdate: undefined,\n        createuserid: undefined,\n        modifydate: undefined,\n        modifyuserid: undefined,\n        updatetimestamp: undefined,\n        deleted: undefined,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 重置查询操作 */\n    resetQuery() {\n      this.timeRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length != 1\n      this.multiple = !selection.length;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加\";\n      this.opertype = 1;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const Ids = row.id || this.ids;\n\n      this.$confirm('是否确认删除参数编号为\"' + Ids + '\"的数据项？')\n        .then(function () {\n          return delSopDir(Ids);\n        })\n        .then(() => {\n          this.handleQuery();\n          this.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {});\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getSopDir(id).then((res) => {\n        const { code, data } = res;\n        if (code == 200) {\n          this.open = true;\n          this.title = \"修改数据\";\n          this.opertype = 2;\n\n          this.form = {\n            ...data,\n          };\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.id != undefined && this.opertype === 2) {\n            updateSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n            })\n          } else {\n            addSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n            })\n          }\n        }\n      });\n    },\n    //展开/折叠操作\n    toggleExpandAll() {\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        this.refreshTable = true;\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n  \n  .search-container {\n    margin-bottom: 15px;\n    \n    .el-form {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      padding-bottom: 15px;\n      border-bottom: 1px solid #ebeef5;\n      \n      .el-form-item {\n        margin-bottom: 0;\n        margin-right: 20px;\n        \n        .el-input {\n          width: 200px;\n        }\n      }\n    }\n    \n    .button-group {\n      display: flex;\n      align-items: center;\n      margin-top: 15px;\n      \n      .el-button-group {\n        margin-right: 10px;\n        \n        .el-button {\n          margin-right: 0;\n          border-radius: 0;\n          \n          &:first-child {\n            border-top-left-radius: 4px;\n            border-bottom-left-radius: 4px;\n          }\n          \n          &:last-child {\n            border-top-right-radius: 4px;\n            border-bottom-right-radius: 4px;\n          }\n        }\n      }\n    }\n  }\n\n  .el-table {\n    margin: 15px 0;\n    border: 1px solid #ebeef5;\n    border-radius: 4px;\n    \n    &::before {\n      display: none;\n    }\n\n    .el-table__header {\n      th {\n        background-color: #f8fafc;\n        color: #333;\n        font-weight: 600;\n      }\n    }\n    \n    .el-table__body {\n      tr:hover > td {\n        background-color: #f5f7fa !important;\n      }\n    }\n  }\n\n  .pagination {\n    margin-top: 15px;\n    text-align: right;\n  }\n}\n</style>"]}]}