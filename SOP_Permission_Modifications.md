# SOP权限管理页面修改总结

## 修改概述

根据用户需求，对SOP权限管理页面进行了全面重构，主要实现以下功能：

1. **左侧目录树**: 使用与sopDoc相同的目录树结构（移除新建按钮）
2. **右侧权限表格**: 使用vxe-table实现可编辑权限管理
3. **后端接口**: 改为使用 `SopPermission/GetList`
4. **交互逻辑**: 点击左侧目录树节点，右侧显示该目录的权限列表
5. **权限编辑**: 实现权限多选框的可编辑功能
6. **布局样式**: 完全采用sopDoc页面的布局和样式

## 主要修改内容

### 1. API接口修改

**文件**: `SEFA.UI/sefa-application-business/src/api/SOP/sopPermission.js`

- 将 `getSopPermissionList` 接口地址从 `/api/SopPermission/GetPageList` 改为 `/api/SopPermission/GetList`

### 2. 国际化文件更新

**文件**: 
- `SEFA.UI/sefa-application-business/src/locale/zh-Hans.json`
- `SEFA.UI/sefa-application-business/src/locale/en.json`

添加了权限管理相关的国际化键值：
```json
"SopPermission": {
    "table": {
        "RoleName": "角色名称",
        "Upload": "上传",
        "Download": "下载", 
        "Preview": "预览",
        "Search": "检索",
        "Delete": "删除",
        "Operation": "操作",
        "SelectAll": "全选",
        "CancelAll": "取消全选"
    }
}
```

### 3. 权限管理页面重构

**文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopPermission/index.vue`

#### 主要变更：

1. **页面布局**：
   - 采用与sopDoc相同的split-pane分割布局
   - 左侧：目录树（移除新建按钮，保留刷新、展开、收起）
   - 右侧：查询区域 + 权限管理表格

2. **目录树功能**：
   - 使用 `getSopDirTree` API获取目录树数据
   - 点击目录节点触发 `handleNodeClick` 事件
   - 支持展开/收起所有节点功能
   - 显示当前选中的目录信息

3. **查询功能**：
   - 右侧顶部添加查询区域，样式与sopDoc保持一致
   - 支持根据目录名称、目录编码进行模糊查询
   - 提供查询和重置按钮
   - 查询条件与目录选择相互独立

4. **权限表格**：
   - 使用 `vxe-table` 替换原有的 `el-table`
   - 启用可编辑模式：`:edit-config="{ trigger: 'click', mode: 'cell' }"`
   - 根据选中的目录ID和查询条件获取权限列表

4. **表格列配置**：
   - 角色名称列：显示角色名称
   - 权限多选框列：上传、下载、预览、检索、删除
   - 操作列：全选、取消全选按钮

5. **权限管理逻辑**：
   - `updatePermLevel(row)`: 更新权限级别，使用位运算
   - `selectAll(row)`: 全选所有权限
   - `cancelAll(row)`: 取消所有权限
   - `parsePermLevel(permLevel, row)`: 解析权限级别字符串

6. **权限级别编码**：
   - 预览: 1
   - 下载: 2
   - 检索: 3
   - 上传: 4
   - 删除: 8
   - 权限字符串格式：使用逗号分隔，如"1,2,3"表示拥有预览、下载、检索权限

7. **样式设计**：
   - 完全采用sopDoc页面的样式设计
   - 左侧树区域：灰色背景，圆角边框
   - 右侧内容区域：白色卡片样式，带阴影效果

### 4. SopDir组件修复

**文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopDir/index.vue`

修复了以下问题：
- 修正了模板中 `searchForm` 与 `queryParams` 的不一致问题
- 修正了API函数导入名称不匹配的问题
- 添加了行点击事件 `handleRowClick` 用于与权限管理页面交互

## 权限级别计算说明

权限级别使用逗号分隔的字符串进行编码和解码：

### 编码（设置权限）：
```javascript
let permissions = [];
if (row.Preview) permissions.push('1');    // 预览 (1)
if (row.Download) permissions.push('2');   // 下载 (2)
if (row.Search) permissions.push('3');     // 检索 (3)
if (row.Upload) permissions.push('4');     // 上传 (4)
if (row.Delete) permissions.push('8');     // 删除 (8)
row.PermLevel = permissions.join(',');
```

### 解码（读取权限）：
```javascript
const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');
permissions.forEach(perm => {
    switch (perm) {
        case '1': row.Preview = true; break;    // 预览
        case '2': row.Download = true; break;   // 下载
        case '3': row.Search = true; break;     // 检索
        case '4': row.Upload = true; break;     // 上传
        case '8': row.Delete = true; break;     // 删除
    }
});
```

### 权限字符串示例：
- `"1"` - 仅预览权限
- `"1,2"` - 预览和下载权限
- `"1,2,3,4,8"` - 所有权限

## 测试数据

为了便于测试，在API调用失败时会加载以下测试数据：

1. **管理员**: 权限级别15 (所有权限)
2. **普通用户**: 权限级别3 (预览+下载)
3. **访客**: 权限级别1 (仅预览)

## 页面交互流程

1. **页面初始化**：
   - 自动加载目录树数据
   - 右侧表格为空，显示"请选择目录"提示

2. **目录选择**：
   - 点击左侧目录树节点
   - 右侧显示当前选中目录信息
   - 自动加载该目录的权限列表

3. **权限编辑**：
   - 通过多选框直接编辑权限
   - 使用"全选"/"取消全选"按钮快速设置
   - 权限变更实时更新PermLevel字段

## 使用说明

1. **目录树操作**：
   - 刷新：重新加载目录树数据
   - 展开：展开所有目录节点
   - 收起：收起所有目录节点

2. **查询功能**：
   - 目录名称：支持模糊查询
   - 目录编码：支持模糊查询
   - 查询：根据条件筛选权限列表
   - 重置：清空查询条件（保留当前选中目录）

3. **权限管理**：
   - 点击目录节点查看权限列表
   - 直接点击多选框编辑权限
   - 使用操作按钮批量设置权限

3. **数据同步**：
   - 权限变更会自动计算PermLevel值
   - 支持分页显示权限列表

## 数据处理说明

### 后端数据结构适配：
- 支持多种后端返回格式：`res.response.data`、`res.response`、直接数组
- 自动处理 `PermLevel` 为 `null` 的情况
- 页面加载时自动请求权限数据
- 添加详细的调试日志便于排查问题

### 权限字段处理：
- `PermLevel` 为 `null` 时，所有权限默认为 `false`
- 支持逗号分隔的权限字符串格式
- 自动过滤空值和 `null` 字符串

## 注意事项

1. 权限级别字段 `PermLevel` 使用逗号分隔的字符串格式，如 `"1,2,3"`
2. 页面加载时会自动请求权限数据，无需先选择目录
3. 权限变更目前只在前端更新，需要后端API支持保存功能
4. 支持多种后端数据返回格式，具有良好的兼容性
5. 页面布局和样式完全遵循sopDoc页面的设计规范
6. 添加了调试信息显示，便于排查数据显示问题

## 最新更新 (2024-12-19)

### ✅ 新增功能和优化：

1. **页面初始化优化**：
   - 默认加载根目录权限数据，通过 `grantId='root'` 进行查询
   - 点击目录树节点时使用目录ID作为 `grantId` 进行查询

2. **全选/取消全选功能修复**：
   - 修复了按钮点击不生效的问题
   - 美化了按钮样式，使用渐变色和悬停效果
   - 全选按钮：蓝色渐变，取消全选按钮：橙色渐变

3. **表格样式全面升级**：
   - 从 `vxe-table` 改为 `el-table`，参照SOP文件管理样式
   - 添加序号列和更好的列宽设置
   - 表格头部使用渐变背景
   - 行悬停效果：轻微上移和阴影
   - 复选框美化：圆角设计和渐变选中效果

4. **页面美化和空白区域优化**：
   - 添加数据统计卡片：总计角色、已配置权限、未配置权限
   - 查询区域使用紫色渐变背景
   - 分页器使用橙色渐变背景
   - 所有按钮添加悬停动画效果
   - 输入框和按钮使用圆角设计

5. **视觉效果增强**：
   - 使用多种渐变色彩搭配
   - 添加阴影和动画过渡效果
   - 统一的圆角设计语言
   - 响应式的悬停交互

## 最新更新 (2024-12-19 下午)

### ✅ 新增功能和优化：

1. **按钮样式优化**：
   - 全选和取消全选按钮在一行内显示
   - 使用统一的主题色：全选(primary蓝色)，取消全选(info灰色)
   - 按钮间距和布局优化

2. **权限保存功能**：
   - 点击复选框自动保存权限到后端
   - 全选/取消全选操作自动保存
   - 使用 `SopPermission/SaveForm` API进行保存
   - 添加保存成功/失败的消息提示

3. **统计栏增强**：
   - 添加当前目录信息显示
   - 4个统计项：当前目录、总计角色、已配置权限、未配置权限
   - 当前目录名称使用蓝色高亮显示
   - 统计项布局优化，平均分布

### 🔧 技术实现：

1. **异步权限保存**：
   ```javascript
   async handlePermissionChange(row, permission, value) {
       this.$set(row, permission, value);
       await this.updatePermLevel(row);
   }

   async savePermission(row) {
       const saveData = {
           ID: row.ID,
           TargetId: row.TargetId,
           PermLevel: row.PermLevel,
           // ... 其他字段
       };
       await saveSopPermissionForm(saveData);
   }
   ```

2. **权限字符串拼接**：
   ```javascript
   let permissions = [];
   if (row.Preview) permissions.push('1');
   if (row.Download) permissions.push('2');
   if (row.Search) permissions.push('3');
   if (row.Upload) permissions.push('4');
   if (row.Delete) permissions.push('8');
   row.PermLevel = permissions.join(',');
   ```

3. **目录信息获取**：
   ```javascript
   getCurrentDirName() {
       if (this.selectedDir) {
           return this.selectedDir.name || this.selectedDir.dirName || '未知目录';
       }
       return '根目录';
   }
   ```

### 🎨 设计特色：

- **简洁的设计风格**：参考SOP文件管理的简洁样式
- **统一的主题色**：蓝色和灰色的搭配
- **实时保存反馈**：操作后立即显示保存状态
- **信息密度优化**：统计栏显示关键信息
