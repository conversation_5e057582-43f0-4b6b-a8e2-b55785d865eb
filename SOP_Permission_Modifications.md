# SOP权限管理页面修改总结

## 修改概述

根据用户需求，对SOP权限管理页面进行了全面重构，主要实现以下功能：

1. **左侧目录树**: 使用与sopDoc相同的目录树结构（移除新建按钮）
2. **右侧权限表格**: 使用vxe-table实现可编辑权限管理
3. **后端接口**: 改为使用 `SopPermission/GetList`
4. **交互逻辑**: 点击左侧目录树节点，右侧显示该目录的权限列表
5. **权限编辑**: 实现权限多选框的可编辑功能
6. **布局样式**: 完全采用sopDoc页面的布局和样式

## 主要修改内容

### 1. API接口修改

**文件**: `SEFA.UI/sefa-application-business/src/api/SOP/sopPermission.js`

- 将 `getSopPermissionList` 接口地址从 `/api/SopPermission/GetPageList` 改为 `/api/SopPermission/GetList`

### 2. 国际化文件更新

**文件**: 
- `SEFA.UI/sefa-application-business/src/locale/zh-Hans.json`
- `SEFA.UI/sefa-application-business/src/locale/en.json`

添加了权限管理相关的国际化键值：
```json
"SopPermission": {
    "table": {
        "RoleName": "角色名称",
        "Upload": "上传",
        "Download": "下载", 
        "Preview": "预览",
        "Search": "检索",
        "Delete": "删除",
        "Operation": "操作",
        "SelectAll": "全选",
        "CancelAll": "取消全选"
    }
}
```

### 3. 权限管理页面重构

**文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopPermission/index.vue`

#### 主要变更：

1. **页面布局**：
   - 采用与sopDoc相同的split-pane分割布局
   - 左侧：目录树（移除新建按钮，保留刷新、展开、收起）
   - 右侧：权限管理表格

2. **目录树功能**：
   - 使用 `getSopDirTree` API获取目录树数据
   - 点击目录节点触发 `handleNodeClick` 事件
   - 支持展开/收起所有节点功能
   - 显示当前选中的目录信息

3. **权限表格**：
   - 使用 `vxe-table` 替换原有的 `el-table`
   - 启用可编辑模式：`:edit-config="{ trigger: 'click', mode: 'cell' }"`
   - 根据选中的目录ID获取权限列表

4. **表格列配置**：
   - 角色名称列：显示角色名称
   - 权限多选框列：上传、下载、预览、检索、删除
   - 操作列：全选、取消全选按钮

5. **权限管理逻辑**：
   - `updatePermLevel(row)`: 更新权限级别，使用位运算
   - `selectAll(row)`: 全选所有权限
   - `cancelAll(row)`: 取消所有权限
   - `parsePermLevel(permLevel, row)`: 解析权限级别字符串

6. **权限级别编码**：
   - 预览: 1 (二进制: 0001)
   - 下载: 2 (二进制: 0010)
   - 检索: 3 (二进制: 0011)
   - 上传: 4 (二进制: 0100)
   - 删除: 8 (二进制: 1000)

7. **样式设计**：
   - 完全采用sopDoc页面的样式设计
   - 左侧树区域：灰色背景，圆角边框
   - 右侧内容区域：白色卡片样式，带阴影效果

### 4. SopDir组件修复

**文件**: `SEFA.UI/sefa-application-business/src/views/SOP/sopDir/index.vue`

修复了以下问题：
- 修正了模板中 `searchForm` 与 `queryParams` 的不一致问题
- 修正了API函数导入名称不匹配的问题
- 添加了行点击事件 `handleRowClick` 用于与权限管理页面交互

## 权限级别计算说明

权限级别使用位运算进行编码和解码：

### 编码（设置权限）：
```javascript
let permLevel = 0;
if (row.Preview) permLevel |= 1;    // 预览 (1)
if (row.Download) permLevel |= 2;   // 下载 (2)
if (row.Search) permLevel |= 3;     // 检索 (3)
if (row.Upload) permLevel |= 4;     // 上传 (4)
if (row.Delete) permLevel |= 8;     // 删除 (8)
```

### 解码（读取权限）：
```javascript
const level = parseInt(permLevel) || 0;
row.Preview = (level & 1) > 0;      // 预览 (1)
row.Download = (level & 2) > 0;     // 下载 (2)
row.Search = (level & 3) > 0;       // 检索 (3)
row.Upload = (level & 4) > 0;       // 上传 (4)
row.Delete = (level & 8) > 0;       // 删除 (8)
```

## 测试数据

为了便于测试，在API调用失败时会加载以下测试数据：

1. **管理员**: 权限级别15 (所有权限)
2. **普通用户**: 权限级别3 (预览+下载)
3. **访客**: 权限级别1 (仅预览)

## 页面交互流程

1. **页面初始化**：
   - 自动加载目录树数据
   - 右侧表格为空，显示"请选择目录"提示

2. **目录选择**：
   - 点击左侧目录树节点
   - 右侧显示当前选中目录信息
   - 自动加载该目录的权限列表

3. **权限编辑**：
   - 通过多选框直接编辑权限
   - 使用"全选"/"取消全选"按钮快速设置
   - 权限变更实时更新PermLevel字段

## 使用说明

1. **目录树操作**：
   - 刷新：重新加载目录树数据
   - 展开：展开所有目录节点
   - 收起：收起所有目录节点

2. **权限管理**：
   - 点击目录节点查看权限列表
   - 直接点击多选框编辑权限
   - 使用操作按钮批量设置权限

3. **数据同步**：
   - 权限变更会自动计算PermLevel值
   - 支持分页显示权限列表

## 注意事项

1. 权限级别字段 `PermLevel` 使用字符串类型存储数字值
2. 必须先选择目录才能查看权限列表
3. 权限变更目前只在前端更新，需要后端API支持保存功能
4. 测试数据仅在API调用失败时显示，正常情况下会从后端获取真实数据
5. 页面布局和样式完全遵循sopDoc页面的设计规范
