import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

/**
 * 文档变更审计表分页查询
 * @param {查询条件} data
 */
export function getSopAuditList(data) {
    return request({
        url: baseURL + '/api/SopAudit/GetPageList',
        method: 'post',
        data
    })
}

/**
 * 保存文档变更审计表
 * @param data
 */
export function saveSopAuditForm(data) {
    return request({
        url: baseURL + '/api/SopAudit/SaveForm',
        method: 'post',
        data
    })
}

/**
 * 获取文档变更审计表详情
 * @param {Id}
 */
export function getSopAuditDetail(id) {
    return request({
        url: baseURL + '/api/SopAudit/GetEntity',
        method: 'post',
        data: id
    })
}

/**
 * 删除文档变更审计表
 * @param {主键} data
 */
export function delSopAudit(data) {
    return request({
        url: baseURL + '/api/SopAudit/Delete',
        method: 'post',
        data
    })
}


