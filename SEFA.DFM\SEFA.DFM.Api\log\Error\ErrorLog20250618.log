2025-06-18 15:23:24.987 +08:00 [ERR] 
2025-06-18 15:23:25.325 +08:00 [ERR] DELETE 语句与 REFERENCE 约束"FK__DFM_B_SOP__DIR_I__762D5431"冲突。该冲突发生于数据库"SEFADB_XZD"，表"dbo.DFM_B_SOP_DOC", column 'DIR_ID'。
语句已终止。
【自定义错误】：DELETE 语句与 REFERENCE 约束"FK__DFM_B_SOP__DIR_I__762D5431"冲突。该冲突发生于数据库"SEFADB_XZD"，表"dbo.DFM_B_SOP_DOC", column 'DIR_ID'。
语句已终止。 
【异常类型】：SqlException 
【异常信息】：DELETE 语句与 REFERENCE 约束"FK__DFM_B_SOP__DIR_I__762D5431"冲突。该冲突发生于数据库"SEFADB_XZD"，表"dbo.DFM_B_SOP_DOC", column 'DIR_ID'。
语句已终止。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
   at SqlSugar.DeleteableProvider`1.ExecuteCommandHasChangeAsync()
   at SEFA.Base.Repository.Base.BaseRepository`1.DeleteByIds(Object[] ids)
   at SEFA.Base.Services.BASE.BaseServices`1.DeleteByIds(Object[] ids)
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.Delete(String[] ids) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 100
   at lambda_method1147(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): DELETE 语句与 REFERENCE 约束"FK__DFM_B_SOP__DIR_I__762D5431"冲突。该冲突发生于数据库"SEFADB_XZD"，表"dbo.DFM_B_SOP_DOC", column 'DIR_ID'。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.DeleteableProvider`1.ExecuteCommandAsync()
   at SqlSugar.DeleteableProvider`1.ExecuteCommandHasChangeAsync()
   at SEFA.Base.Repository.Base.BaseRepository`1.DeleteByIds(Object[] ids)
   at SEFA.Base.Services.BASE.BaseServices`1.DeleteByIds(Object[] ids)
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.Delete(String[] ids) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 100
   at lambda_method1147(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:4bf77bb0-24ea-4b19-ba56-d591440647c0
Error Number:547,State:0,Class:16
2025-06-18 15:29:55.913 +08:00 [ERR] 
2025-06-18 15:29:56.074 +08:00 [ERR] Value cannot be null. (Parameter 'key')
【自定义错误】：Value cannot be null. (Parameter 'key') 
【异常类型】：ArgumentNullException 
【异常信息】：Value cannot be null. (Parameter 'key') 
【堆栈调用】：   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Collections.Generic.Dictionary`2.Add(TKey key, TValue value)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](List`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 149
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method674(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Collections.Generic.Dictionary`2.Add(TKey key, TValue value)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](List`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 149
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method674(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-18 15:30:11.823 +08:00 [ERR] 
2025-06-18 15:30:11.935 +08:00 [ERR] Value cannot be null. (Parameter 'key')
【自定义错误】：Value cannot be null. (Parameter 'key') 
【异常类型】：ArgumentNullException 
【异常信息】：Value cannot be null. (Parameter 'key') 
【堆栈调用】：   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Collections.Generic.Dictionary`2.Add(TKey key, TValue value)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](List`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 149
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method674(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.ArgumentNullException: Value cannot be null. (Parameter 'key')
   at System.Collections.Generic.Dictionary`2.TryInsert(TKey key, TValue value, InsertionBehavior behavior)
   at System.Collections.Generic.Dictionary`2.Add(TKey key, TValue value)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](List`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 149
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method674(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-18 16:26:10.178 +08:00 [ERR] 
2025-06-18 16:26:10.325 +08:00 [ERR] PermLevel can't  convert string to byte
【自定义错误】：PermLevel can't  convert string to byte 
【异常类型】：SqlSugarException 
【异常信息】：PermLevel can't  convert string to byte 
【堆栈调用】：   at SqlSugar.IDataReaderEntityBuilder`1.CheckType(List`1 invalidTypes, String bindProperyTypeName, String validPropertyType, String propertyName)
   at SqlSugar.IDataReaderEntityBuilder`1.BindMethod(ILGenerator generator, EntityColumnInfo columnInfo, Int32 ordinal)
   at SqlSugar.IDataReaderEntityBuilder`1.BindField(ILGenerator generator, LocalBuilder result, EntityColumnInfo columnInfo, String fieldName)
   at SqlSugar.IDataReaderEntityBuilder`1.CreateBuilder(Type type)
   at SqlSugar.DbBindAccessory.<>c__DisplayClass5_0`1.<GetEntityListAsync>b__1()
   at SqlSugar.ReflectionInoCore`1.GetOrCreate(String cacheKey, Func`1 create)
   at SqlSugar.ReflectionInoCacheService.GetOrCreate[V](String cacheKey, Func`1 create, Int32 cacheDurationInSeconds)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.DFM.Services.SopPermissionServices.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopPermissionServices.cs:line 25
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopPermissionController.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopPermissionController.cs:line 31
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
SqlSugar.SqlSugarException: PermLevel can't  convert string to byte
   at SqlSugar.IDataReaderEntityBuilder`1.CheckType(List`1 invalidTypes, String bindProperyTypeName, String validPropertyType, String propertyName)
   at SqlSugar.IDataReaderEntityBuilder`1.BindMethod(ILGenerator generator, EntityColumnInfo columnInfo, Int32 ordinal)
   at SqlSugar.IDataReaderEntityBuilder`1.BindField(ILGenerator generator, LocalBuilder result, EntityColumnInfo columnInfo, String fieldName)
   at SqlSugar.IDataReaderEntityBuilder`1.CreateBuilder(Type type)
   at SqlSugar.DbBindAccessory.<>c__DisplayClass5_0`1.<GetEntityListAsync>b__1()
   at SqlSugar.ReflectionInoCore`1.GetOrCreate(String cacheKey, Func`1 create)
   at SqlSugar.ReflectionInoCacheService.GetOrCreate[V](String cacheKey, Func`1 create, Int32 cacheDurationInSeconds)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.DFM.Services.SopPermissionServices.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopPermissionServices.cs:line 25
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopPermissionController.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopPermissionController.cs:line 31
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-18 16:26:22.577 +08:00 [ERR] 
2025-06-18 16:26:22.701 +08:00 [ERR] PermLevel can't  convert string to byte
【自定义错误】：PermLevel can't  convert string to byte 
【异常类型】：SqlSugarException 
【异常信息】：PermLevel can't  convert string to byte 
【堆栈调用】：   at SqlSugar.IDataReaderEntityBuilder`1.CheckType(List`1 invalidTypes, String bindProperyTypeName, String validPropertyType, String propertyName)
   at SqlSugar.IDataReaderEntityBuilder`1.BindMethod(ILGenerator generator, EntityColumnInfo columnInfo, Int32 ordinal)
   at SqlSugar.IDataReaderEntityBuilder`1.BindField(ILGenerator generator, LocalBuilder result, EntityColumnInfo columnInfo, String fieldName)
   at SqlSugar.IDataReaderEntityBuilder`1.CreateBuilder(Type type)
   at SqlSugar.DbBindAccessory.<>c__DisplayClass5_0`1.<GetEntityListAsync>b__1()
   at SqlSugar.ReflectionInoCore`1.GetOrCreate(String cacheKey, Func`1 create)
   at SqlSugar.ReflectionInoCacheService.GetOrCreate[V](String cacheKey, Func`1 create, Int32 cacheDurationInSeconds)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.DFM.Services.SopPermissionServices.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopPermissionServices.cs:line 25
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopPermissionController.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopPermissionController.cs:line 31
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
SqlSugar.SqlSugarException: PermLevel can't  convert string to byte
   at SqlSugar.IDataReaderEntityBuilder`1.CheckType(List`1 invalidTypes, String bindProperyTypeName, String validPropertyType, String propertyName)
   at SqlSugar.IDataReaderEntityBuilder`1.BindMethod(ILGenerator generator, EntityColumnInfo columnInfo, Int32 ordinal)
   at SqlSugar.IDataReaderEntityBuilder`1.BindField(ILGenerator generator, LocalBuilder result, EntityColumnInfo columnInfo, String fieldName)
   at SqlSugar.IDataReaderEntityBuilder`1.CreateBuilder(Type type)
   at SqlSugar.DbBindAccessory.<>c__DisplayClass5_0`1.<GetEntityListAsync>b__1()
   at SqlSugar.ReflectionInoCore`1.GetOrCreate(String cacheKey, Func`1 create)
   at SqlSugar.ReflectionInoCacheService.GetOrCreate[V](String cacheKey, Func`1 create, Int32 cacheDurationInSeconds)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.DFM.Services.SopPermissionServices.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopPermissionServices.cs:line 25
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopPermissionController.GetList(SopPermissionRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopPermissionController.cs:line 31
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
