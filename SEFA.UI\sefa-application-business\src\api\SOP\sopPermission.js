import request from '@/util/request'
import { configUrl } from '@/config'
const baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM

/**
 * 文档/目录权限控制表分页查询
 * @param {查询条件} data
 */
export function getSopPermissionList(data) {
    return request({
        url: baseURL + '/api/SopPermission/GetPageList',
        method: 'post',
        data
    })
}

/**
 * 保存文档/目录权限控制表
 * @param data
 */
export function saveSopPermissionForm(data) {
    return request({
        url: baseURL + '/api/SopPermission/SaveForm',
        method: 'post',
        data
    })
}

/**
 * 获取文档/目录权限控制表详情
 * @param {Id}
 */
export function getSopPermissionDetail(id) {
    return request({
        url: baseURL + '/api/SopPermission/GetEntity',
        method: 'post',
        data: id
    })
}

/**
 * 删除文档/目录权限控制表
 * @param {主键} data
 */
export function delSopPermission(data) {
    return request({
        url: baseURL + '/api/SopPermission/Delete',
        method: 'post',
        data
    })
}


