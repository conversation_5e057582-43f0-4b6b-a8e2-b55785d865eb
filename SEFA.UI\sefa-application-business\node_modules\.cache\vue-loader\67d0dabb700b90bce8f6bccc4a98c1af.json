{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue?vue&type=style&index=0&id=488e02f1&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue", "mtime": 1750225849524}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hcHAtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4OwogIAogIC5zZWFyY2gtY29udGFpbmVyIHsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgICAKICAgIC5lbC1mb3JtIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC13cmFwOiB3cmFwOwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBwYWRkaW5nLWJvdHRvbTogMTVweDsKICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7CiAgICAgIAogICAgICAuZWwtZm9ybS1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICAgIG1hcmdpbi1yaWdodDogMjBweDsKICAgICAgICAKICAgICAgICAuZWwtaW5wdXQgewogICAgICAgICAgd2lkdGg6IDIwMHB4OwogICAgICAgIH0KICAgICAgfQogICAgfQogICAgCiAgICAuYnV0dG9uLWdyb3VwIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgbWFyZ2luLXRvcDogMTVweDsKICAgICAgCiAgICAgIC5lbC1idXR0b24tZ3JvdXAgewogICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgICAgICAKICAgICAgICAuZWwtYnV0dG9uIHsKICAgICAgICAgIG1hcmdpbi1yaWdodDogMDsKICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDA7CiAgICAgICAgICAKICAgICAgICAgICY6Zmlyc3QtY2hpbGQgewogICAgICAgICAgICBib3JkZXItdG9wLWxlZnQtcmFkaXVzOiA0cHg7CiAgICAgICAgICAgIGJvcmRlci1ib3R0b20tbGVmdC1yYWRpdXM6IDRweDsKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgICAgYm9yZGVyLXRvcC1yaWdodC1yYWRpdXM6IDRweDsKICAgICAgICAgICAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDRweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CgogIC5lbC10YWJsZSB7CiAgICBtYXJnaW46IDE1cHggMDsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAKICAgICY6OmJlZm9yZSB7CiAgICAgIGRpc3BsYXk6IG5vbmU7CiAgICB9CgogICAgLmVsLXRhYmxlX19oZWFkZXIgewogICAgICB0aCB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZmFmYzsKICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICB9CiAgICB9CiAgICAKICAgIC5lbC10YWJsZV9fYm9keSB7CiAgICAgIHRyOmhvdmVyID4gdGQgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmEgIWltcG9ydGFudDsKICAgICAgfQogICAgfQogIH0KCiAgLnBhZ2luYXRpb24gewogICAgbWFyZ2luLXRvcDogMTVweDsKICAgIHRleHQtYWxpZ246IHJpZ2h0OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuUA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopDir", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"search-container\">\n      <el-form :model=\"queryParams\" size=\"small\" label-position=\"right\" inline ref=\"queryForm\" :label-width=\"labelWidth\" \n        v-show=\"showSearch\" @submit.native.prevent>\n        \n        <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n          <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n          <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirOwner')\" prop=\"ownerUserid\">\n          <user-select v-model=\"searchForm.ownerUserid\" />\n        </el-form-item>\n\n        <el-form-item>\n          <el-button-group>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-button-group>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"button-group\">\n        <el-button-group>\n          <el-button \n            type=\"primary\" \n            v-hasPermi=\"['DFM:SOP:add']\" \n            plain \n            icon=\"el-icon-plus\" \n            size=\"mini\"\n            @click=\"handleAdd\">新增</el-button>\n          <el-button \n            type=\"info\" \n            plain \n            icon=\"el-icon-sort\" \n            size=\"mini\"\n            @click=\"toggleExpandAll\">展开/折叠</el-button>\n          <el-button \n            type=\"danger\" \n            :disabled=\"multiple\" \n            v-hasPermi=\"['DFM:SOP:delete']\" \n            plain \n            icon=\"el-icon-delete\" \n            size=\"mini\"\n            @click=\"handleDelete\">删除</el-button>\n        </el-button-group>\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n      </div>\n    </div>\n\n    <!-- 数据区域 -->\n    <el-table v-if=\"refreshTable\" :data=\"dataList\" v-loading=\"loading\" ref=\"table\" border highlight-current-row @selection-change=\"handleSelectionChange\"\n      :default-expand-all=\"isExpandAll\" row-key=\"id\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n      <el-table-column prop=\"parentId\" label=\"父目录ID\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\" parentIdOptions\" :value=\"scope.row.parentId\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"dirName\" :label=\"$t('SOP.DirName')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"dirCode\" :label=\"$t('SOP.DirCode')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"ownerUserid\" :label=\"$t('SOP.DirOwner')\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.ownerUserName || scope.row.ownerUserid }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"createdate\" label=\"创建时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"createuserid\" label=\"创建人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifydate\" label=\"修改时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifyuserid\" label=\"修改人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"deleted\" label=\"删除标记(0-未删 1-已删)\" align=\"center\" />\n\n      <el-table-column label=\"操作\" align=\"center\" width=\"140\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:edit']\" type=\"success\" icon=\"el-icon-edit\" title=\"编辑\" \n            @click=\"handleUpdate(scope.row)\"></el-button>\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:delete']\" type=\"danger\" icon=\"el-icon-delete\" title=\"删除\" \n            @click=\"handleDelete(scope.row)\"></el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination class=\"mt10\" background :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n\n    <!-- 添加或修改SOP文档目录树结构表对话框 -->\n    <el-dialog :title=\"title\" :lock-scroll=\"false\" :visible.sync=\"open\" >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :label-width=\"formLabelWidth\">\n        <el-row :gutter=\"20\">\n        \n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"text\" @click=\"cancel\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { \n  treelistSopDir,\n  listSopDir,\n  addSopDir,\n  delSopDir,\n  updateSopDir,\n  getSopDir,\n} from '@/api/SOP/sopDir.js';\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport UserSelect from '@/components/UserSelect';\n\nexport default {\n  name: \"sopdir\",\n  components: { \n    UserSelect \n  },\n  data() {\n    return {\n      labelWidth: \"100px\",\n      formLabelWidth:\"100px\",\n      // 选中id数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 遮罩层\n      loading: false,\n      // 显示搜索条件\n      showSearch: true,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n      },\n      // 弹出层标题\n      title: \"\",\n      // 操作类型 1、add 2、edit\n      opertype: 0,\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 表单参数\n      form: {},\n      columns: [\n        { index: 0, key: 'id', label: this.$t('SOP.PrimaryKey'), checked:  true  },\n        { index: 1, key: 'parentId', label: this.$t('SOP.ParentDirId'), checked:  true  },\n        { index: 2, key: 'dirName', label: this.$t('SOP.DirName'), checked:  true  },\n        { index: 3, key: 'dirCode', label: this.$t('SOP.DirCode'), checked:  true  },\n        { index: 4, key: 'ownerUserid', label: this.$t('SOP.DirOwnerId'), checked:  true  },\n        { index: 5, key: 'createdate', label: this.$t('SOP.CreateTime'), checked:  true  },\n        { index: 6, key: 'createuserid', label: this.$t('SOP.CreateUserId'), checked:  true  },\n        { index: 7, key: 'modifydate', label: this.$t('SOP.ModifyTime'), checked:  true  },\n        { index: 8, key: 'modifyuserid', label: this.$t('SOP.ModifyUserId'), checked:  true  },\n        { index: 9, key: 'updatetimestamp', label: this.$t('SOP.Timestamp'), checked:  false  },\n        { index: 10, key: 'deleted', label: this.$t('SOP.DeleteFlag'), checked:  false  },\n      ],\n      // 父目录ID选项列表\n      parentIdOptions: [],\n      // 目录负责人ID选项列表\n      ownerUseridOptions: [],\n      dataList: [],\n      total: 0,\n      rules: {\n        parentId: [\n          { required: true, message: this.$t('SOP.ParentDirIdRequired'), trigger: \"change\" }\n        ],\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: \"blur\" }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: \"blur\" }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: \"change\" }\n        ],\n      },\n    };\n  },\n  created() {\n    // 列表数据查询\n    this.getList();\n  },\n  methods: {\n    // 查询数据\n    getList() {\n      this.loading = true;\n      treelistSopDir(this.queryParams).then(res => {\n         if (res.code == 200) {\n           this.dataList = res.data;\n           this.loading = false;\n         }\n       })\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.id,\n        label: node.dirName,\n        children: node.children,\n      };\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 重置数据表单\n    reset() {\n      this.form = {\n        id: undefined,\n        parentId: undefined,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n        createdate: undefined,\n        createuserid: undefined,\n        modifydate: undefined,\n        modifyuserid: undefined,\n        updatetimestamp: undefined,\n        deleted: undefined,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 重置查询操作 */\n    resetQuery() {\n      this.timeRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length != 1\n      this.multiple = !selection.length;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加\";\n      this.opertype = 1;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const Ids = row.id || this.ids;\n\n      this.$confirm('是否确认删除参数编号为\"' + Ids + '\"的数据项？')\n        .then(function () {\n          return delSopDir(Ids);\n        })\n        .then(() => {\n          this.handleQuery();\n          this.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {});\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getSopDir(id).then((res) => {\n        const { code, data } = res;\n        if (code == 200) {\n          this.open = true;\n          this.title = \"修改数据\";\n          this.opertype = 2;\n\n          this.form = {\n            ...data,\n          };\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.id != undefined && this.opertype === 2) {\n            updateSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n            })\n          } else {\n            addSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n            })\n          }\n        }\n      });\n    },\n    //展开/折叠操作\n    toggleExpandAll() {\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        this.refreshTable = true;\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n  \n  .search-container {\n    margin-bottom: 15px;\n    \n    .el-form {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      padding-bottom: 15px;\n      border-bottom: 1px solid #ebeef5;\n      \n      .el-form-item {\n        margin-bottom: 0;\n        margin-right: 20px;\n        \n        .el-input {\n          width: 200px;\n        }\n      }\n    }\n    \n    .button-group {\n      display: flex;\n      align-items: center;\n      margin-top: 15px;\n      \n      .el-button-group {\n        margin-right: 10px;\n        \n        .el-button {\n          margin-right: 0;\n          border-radius: 0;\n          \n          &:first-child {\n            border-top-left-radius: 4px;\n            border-bottom-left-radius: 4px;\n          }\n          \n          &:last-child {\n            border-top-right-radius: 4px;\n            border-bottom-right-radius: 4px;\n          }\n        }\n      }\n    }\n  }\n\n  .el-table {\n    margin: 15px 0;\n    border: 1px solid #ebeef5;\n    border-radius: 4px;\n    \n    &::before {\n      display: none;\n    }\n\n    .el-table__header {\n      th {\n        background-color: #f8fafc;\n        color: #333;\n        font-weight: 600;\n      }\n    }\n    \n    .el-table__body {\n      tr:hover > td {\n        background-color: #f5f7fa !important;\n      }\n    }\n  }\n\n  .pagination {\n    margin-top: 15px;\n    text-align: right;\n  }\n}\n</style>"]}]}