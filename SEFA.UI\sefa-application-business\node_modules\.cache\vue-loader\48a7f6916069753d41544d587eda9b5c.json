{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\index.vue", "mtime": 1750225849524}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopDir", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div class=\"search-container\">\n      <el-form :model=\"queryParams\" size=\"small\" label-position=\"right\" inline ref=\"queryForm\" :label-width=\"labelWidth\" \n        v-show=\"showSearch\" @submit.native.prevent>\n        \n        <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n          <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n          <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n        </el-form-item>\n\n        <el-form-item :label=\"$t('SOP.DirOwner')\" prop=\"ownerUserid\">\n          <user-select v-model=\"searchForm.ownerUserid\" />\n        </el-form-item>\n\n        <el-form-item>\n          <el-button-group>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-button-group>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"button-group\">\n        <el-button-group>\n          <el-button \n            type=\"primary\" \n            v-hasPermi=\"['DFM:SOP:add']\" \n            plain \n            icon=\"el-icon-plus\" \n            size=\"mini\"\n            @click=\"handleAdd\">新增</el-button>\n          <el-button \n            type=\"info\" \n            plain \n            icon=\"el-icon-sort\" \n            size=\"mini\"\n            @click=\"toggleExpandAll\">展开/折叠</el-button>\n          <el-button \n            type=\"danger\" \n            :disabled=\"multiple\" \n            v-hasPermi=\"['DFM:SOP:delete']\" \n            plain \n            icon=\"el-icon-delete\" \n            size=\"mini\"\n            @click=\"handleDelete\">删除</el-button>\n        </el-button-group>\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n      </div>\n    </div>\n\n    <!-- 数据区域 -->\n    <el-table v-if=\"refreshTable\" :data=\"dataList\" v-loading=\"loading\" ref=\"table\" border highlight-current-row @selection-change=\"handleSelectionChange\"\n      :default-expand-all=\"isExpandAll\" row-key=\"id\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n      <el-table-column prop=\"parentId\" label=\"父目录ID\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\" parentIdOptions\" :value=\"scope.row.parentId\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"dirName\" :label=\"$t('SOP.DirName')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"dirCode\" :label=\"$t('SOP.DirCode')\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"ownerUserid\" :label=\"$t('SOP.DirOwner')\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.ownerUserName || scope.row.ownerUserid }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"createdate\" label=\"创建时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"createuserid\" label=\"创建人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifydate\" label=\"修改时间\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"modifyuserid\" label=\"修改人ID\" align=\"center\" :show-overflow-tooltip=\"true\" />\n      <el-table-column prop=\"deleted\" label=\"删除标记(0-未删 1-已删)\" align=\"center\" />\n\n      <el-table-column label=\"操作\" align=\"center\" width=\"140\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:edit']\" type=\"success\" icon=\"el-icon-edit\" title=\"编辑\" \n            @click=\"handleUpdate(scope.row)\"></el-button>\n          <el-button size=\"mini\" v-hasPermi=\"['DFM:SOP:delete']\" type=\"danger\" icon=\"el-icon-delete\" title=\"删除\" \n            @click=\"handleDelete(scope.row)\"></el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination class=\"mt10\" background :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n\n    <!-- 添加或修改SOP文档目录树结构表对话框 -->\n    <el-dialog :title=\"title\" :lock-scroll=\"false\" :visible.sync=\"open\" >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" :label-width=\"formLabelWidth\">\n        <el-row :gutter=\"20\">\n        \n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"text\" @click=\"cancel\">{{ $t('GLOBAL._QX') }}</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">{{ $t('GLOBAL._QD') }}</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { \n  treelistSopDir,\n  listSopDir,\n  addSopDir,\n  delSopDir,\n  updateSopDir,\n  getSopDir,\n} from '@/api/SOP/sopDir.js';\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport UserSelect from '@/components/UserSelect';\n\nexport default {\n  name: \"sopdir\",\n  components: { \n    UserSelect \n  },\n  data() {\n    return {\n      labelWidth: \"100px\",\n      formLabelWidth:\"100px\",\n      // 选中id数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 遮罩层\n      loading: false,\n      // 显示搜索条件\n      showSearch: true,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n      },\n      // 弹出层标题\n      title: \"\",\n      // 操作类型 1、add 2、edit\n      opertype: 0,\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 表单参数\n      form: {},\n      columns: [\n        { index: 0, key: 'id', label: this.$t('SOP.PrimaryKey'), checked:  true  },\n        { index: 1, key: 'parentId', label: this.$t('SOP.ParentDirId'), checked:  true  },\n        { index: 2, key: 'dirName', label: this.$t('SOP.DirName'), checked:  true  },\n        { index: 3, key: 'dirCode', label: this.$t('SOP.DirCode'), checked:  true  },\n        { index: 4, key: 'ownerUserid', label: this.$t('SOP.DirOwnerId'), checked:  true  },\n        { index: 5, key: 'createdate', label: this.$t('SOP.CreateTime'), checked:  true  },\n        { index: 6, key: 'createuserid', label: this.$t('SOP.CreateUserId'), checked:  true  },\n        { index: 7, key: 'modifydate', label: this.$t('SOP.ModifyTime'), checked:  true  },\n        { index: 8, key: 'modifyuserid', label: this.$t('SOP.ModifyUserId'), checked:  true  },\n        { index: 9, key: 'updatetimestamp', label: this.$t('SOP.Timestamp'), checked:  false  },\n        { index: 10, key: 'deleted', label: this.$t('SOP.DeleteFlag'), checked:  false  },\n      ],\n      // 父目录ID选项列表\n      parentIdOptions: [],\n      // 目录负责人ID选项列表\n      ownerUseridOptions: [],\n      dataList: [],\n      total: 0,\n      rules: {\n        parentId: [\n          { required: true, message: this.$t('SOP.ParentDirIdRequired'), trigger: \"change\" }\n        ],\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: \"blur\" }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: \"blur\" }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: \"change\" }\n        ],\n      },\n    };\n  },\n  created() {\n    // 列表数据查询\n    this.getList();\n  },\n  methods: {\n    // 查询数据\n    getList() {\n      this.loading = true;\n      treelistSopDir(this.queryParams).then(res => {\n         if (res.code == 200) {\n           this.dataList = res.data;\n           this.loading = false;\n         }\n       })\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.id,\n        label: node.dirName,\n        children: node.children,\n      };\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 重置数据表单\n    reset() {\n      this.form = {\n        id: undefined,\n        parentId: undefined,\n        dirName: undefined,\n        dirCode: undefined,\n        ownerUserid: undefined,\n        createdate: undefined,\n        createuserid: undefined,\n        modifydate: undefined,\n        modifyuserid: undefined,\n        updatetimestamp: undefined,\n        deleted: undefined,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 重置查询操作 */\n    resetQuery() {\n      this.timeRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length != 1\n      this.multiple = !selection.length;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加\";\n      this.opertype = 1;\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const Ids = row.id || this.ids;\n\n      this.$confirm('是否确认删除参数编号为\"' + Ids + '\"的数据项？')\n        .then(function () {\n          return delSopDir(Ids);\n        })\n        .then(() => {\n          this.handleQuery();\n          this.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {});\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getSopDir(id).then((res) => {\n        const { code, data } = res;\n        if (code == 200) {\n          this.open = true;\n          this.title = \"修改数据\";\n          this.opertype = 2;\n\n          this.form = {\n            ...data,\n          };\n        }\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.id != undefined && this.opertype === 2) {\n            updateSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n            })\n          } else {\n            addSopDir(this.form)\n              .then((res) => {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n            })\n          }\n        }\n      });\n    },\n    //展开/折叠操作\n    toggleExpandAll() {\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(() => {\n        this.refreshTable = true;\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n  \n  .search-container {\n    margin-bottom: 15px;\n    \n    .el-form {\n      display: flex;\n      flex-wrap: wrap;\n      align-items: center;\n      padding-bottom: 15px;\n      border-bottom: 1px solid #ebeef5;\n      \n      .el-form-item {\n        margin-bottom: 0;\n        margin-right: 20px;\n        \n        .el-input {\n          width: 200px;\n        }\n      }\n    }\n    \n    .button-group {\n      display: flex;\n      align-items: center;\n      margin-top: 15px;\n      \n      .el-button-group {\n        margin-right: 10px;\n        \n        .el-button {\n          margin-right: 0;\n          border-radius: 0;\n          \n          &:first-child {\n            border-top-left-radius: 4px;\n            border-bottom-left-radius: 4px;\n          }\n          \n          &:last-child {\n            border-top-right-radius: 4px;\n            border-bottom-right-radius: 4px;\n          }\n        }\n      }\n    }\n  }\n\n  .el-table {\n    margin: 15px 0;\n    border: 1px solid #ebeef5;\n    border-radius: 4px;\n    \n    &::before {\n      display: none;\n    }\n\n    .el-table__header {\n      th {\n        background-color: #f8fafc;\n        color: #333;\n        font-weight: 600;\n      }\n    }\n    \n    .el-table__body {\n      tr:hover > td {\n        background-color: #f5f7fa !important;\n      }\n    }\n  }\n\n  .pagination {\n    margin-top: 15px;\n    text-align: right;\n  }\n}\n</style>"]}]}