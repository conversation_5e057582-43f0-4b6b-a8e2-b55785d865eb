<template>
  <div class="root usemystyle">
    <div class="root-layout" v-loading="initLoading">
      <split-pane 
        :min-percent="15" 
        :max-percent="40" 
        :default-percent="20" 
        split="vertical">
        <template slot="pane1">
          <div class="root-left">
            <div class="tree-toolbar">
              <el-button-group>
                <el-button 
                  size="small" 
                  type="primary"
                  icon="el-icon-plus"
                  @click="addSopDirChild({})">新建</el-button>
                <el-button 
                  size="small"
                  icon="el-icon-refresh"
                  @click="getDirTree">刷新</el-button>
                <el-button 
                  size="small"
                  @click="expandAll">展开</el-button>
                <el-button 
                  size="small"
                  @click="collapseAll">收起</el-button>
              </el-button-group>
            </div>
            <el-tree
              ref="tree"
              :data="treeData"
              :props="defaultProps"
              highlight-current
              @node-click="handleNodeClick"
              v-loading="treeLoading">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <div style="line-height: 22px;">
                  <div class="tree-title">{{ node.data.name }}</div>
                </div>
                <span class="tree-node-actions">
                  <el-button type="text" size="mini" @click.stop="() => showSopDirDialog(data)">
                    <i class="el-icon-edit"></i>
                  </el-button>
                  <el-button type="text" size="mini" @click.stop="() => addSopDirChild(data)">
                    <i class="el-icon-plus"></i>
                  </el-button>
                  <el-button type="text" size="mini" @click.stop="() => deleteSopDir(data)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </span>
              </span>
            </el-tree>
          </div>
        </template>
        <template slot="pane2">
          <div class="root-right">
            <div class="InventorySearchBox">
              <div class="search-form">
                <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
                  <div class="form-content">
                    <div class="search-area">
                      <div class="search-row">
                        <el-form-item label="名称" prop="docName" label-width="40px">
                          <el-input v-model="searchForm.docName" placeholder="输入名称" clearable size="small" style="width: 180px;">                            
                          </el-input>
                        </el-form-item>
                        <el-form-item label="编码" prop="docCode" label-width="40px">
                          <el-input v-model="searchForm.docCode" placeholder="输入编码" clearable size="small" style="width: 120px;"></el-input>
                        </el-form-item>
                        <el-form-item label="版本" prop="docVersion" label-width="40px">
                          <el-input v-model="searchForm.docVersion" placeholder="输入版本" clearable size="small" style="width: 80px;"></el-input>
                        </el-form-item>
                        <el-form-item label="状态" prop="docStatus" label-width="40px">
                          <el-select v-model="searchForm.docStatus" placeholder="选择" clearable size="small" style="width: 70px;">
                            <el-option label="有效" :value="1"></el-option>
                            <el-option label="无效" :value="0"></el-option>
                          </el-select>
                        </el-form-item>
                        <div class="action-buttons">
                          <el-button type="success" size="small" icon="el-icon-circle-plus-outline" @click="showDialog({})">新增</el-button>
                          <el-button slot="append" type="primary" icon="el-icon-search" @click="getSearchBtn()" size="small">查询</el-button>
                          <el-button size="small" icon="el-icon-refresh" @click="resetForm">重置</el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-form>
              </div>
            </div>
            <div class="root-main">
              <el-table class="mt-3"
                        :height="700"
                        border
                        v-loading="tableLoading"
                        :data="tableData"
                        style="width: 100%; border-radius: 4px;"
                        :empty-text="'暂无数据'">
                <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center">
                </el-table-column>
                <el-table-column v-for="(item) in tableName"
                                 :default-sort="{prop: item.value, order: 'descending'}"
                                 :key="item.value"
                                 :prop="item.value"
                                 :label="item.text"
                                 :width="item.width"
                                 :align="item.alignType || 'center'"
                                 sortable
                                 show-overflow-tooltip>
                  <template slot-scope="scope">
                    <template v-if="item.value === 'FileSize'">
                      {{ formatFileSize(scope.row[item.value]) }}
                    </template>
                    <template v-else-if="item.value === 'DocStatus'">
                      <el-tag :type="getStatusType(scope.row[item.value])" size="small">
                        {{ formatStatus(scope.row[item.value]) }}
                      </el-tag>
                    </template>
                    <template v-else>
                      {{ scope.row[item.value] }}
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="operation" :min-width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._CK') }}</el-button>
                    <el-button size="mini" type="text" @click="handleDownload(scope.row)">{{ $t('GLOBAL.Download') }}</el-button>
                    <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
                    <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._shenqing') }}</el-button>               
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </split-pane>
      <div class="root-footer">
        <el-pagination
            class="mt-3"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="searchForm.pageIndex"
            :page-sizes="[10,20, 50, 100,500]"
            :page-size="searchForm.pageSize"
            layout="->,total, sizes, prev, pager, next, jumper"
            :total="total"
            background
        ></el-pagination>
      </div>
      <form-dialog @saveForm="getSearchBtn" ref="formDialog" :treeData="treeData"></form-dialog>
      <sop-dir-form-dialog 
        :visible.sync="sopDirDialogVisible" 
        :form-data="sopDirDialogForm" 
        @saveForm="getDirTree" 
        ref="sopDirFormDialog">
      </sop-dir-form-dialog>
    </div>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss'
import FormDialog from './form-dialog'
import TreeSelect from '../components/tree-select'
import SplitPane from '../components/split-pane'
import {
    delSopDoc, getSopDocList, downloadSopDoc
} from "@/api/SOP/sopDoc";
import { getSopDirTree, saveSopDirForm, delSopDir } from "@/api/SOP/sopDir";
import SopDirFormDialog from "@/views/SOP/sopDir/form-dialog";
import { sopDocColumns } from '@/columns/SOP/sopDoc.js';

export default {
  name: 'index.vue',
  components: {
    FormDialog,
    SopDirFormDialog,
    SplitPane
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
        dirId: '',
        docName: '',
        docCode: '',
        docStatus: '',
        deleted: ''
      },
      total: 0,
      tableData: [],
      hansObj: this.$t('SOP文档主表.table'),
      tableName: [],
      tableLoading: false,
      treeLoading: false,
      initLoading: false,
      tableOption: [],
      mainH: 0,
      buttonOption: {
        name:'SOP文档主表',
        serveIp:'baseURL_DFM',
        uploadUrl:'/api/SopDoc/ImportData',
        exportUrl:'/api/SopDoc/ExportData',
        DownLoadUrl:'/api/SopDoc/DownLoadTemplate',
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label:  function (node){
          return node.data.DirName
        } 
      },
      docStatusOptions: [],
      deletedOptions: [],
      sopDirDialogVisible: false,
      sopDirDialogForm: {},
      sopDirFormLoading: false
    }
  },
  async mounted() {
    try {
      this.initLoading = true
      this.getZHHans()
      await Promise.all([
        this.getDictData('docStatus', 'docStatusOptions'),
        this.getDictData('deletedStatus', 'deletedOptions')
      ])
      await this.getDirTree()
      await this.getTableData()
      this.$nextTick(() => {
        this.mainH = this.$webHeight(
          document.getElementsByClassName('root')[0].clientHeight,
          document.getElementsByClassName('root')[0].clientHeight
        )
      })
    } catch (err) {
      console.error('页面初始化失败:', err)
      this.$message.error('页面初始化失败，请刷新重试')
    } finally {
      this.initLoading = false
    }
  },
  beforeDestroy() {
    window.onresize = null
  },
  methods: {
    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let index = 0
      let fileSize = parseFloat(size)
      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }
      return `${fileSize.toFixed(2)} ${units[index]}`
    },

    // 获取状态对应的类型
    getStatusType(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'warning'
        case 0: return 'info'
        default: return ''
      }
    },

    // 格式化状态
    formatStatus(status) {
      switch (status) {
        case 1: return '已生效'
        case 2: return '待审核'
        case 0: return '已禁用'
        default: return '未知'
      }
    },

    async getDictData(dictType, targetKey) {
      try {
        this[targetKey] = await this.$getNewDataDictionary(dictType)
      } catch (err) {
        console.error(`获取${dictType}字典数据失败:`, err)
        this.$message.error('获取字典数据失败')
        throw err
      }
    },

    getZHHans() {
      this.tableName = sopDocColumns
    },

    showDialog(row) {
      this.$refs.formDialog.show(row)
    },

    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },

    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },

    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    resetForm() {
      this.searchForm = {
        pageIndex: 1,
        pageSize: 20,
        dirId: '',
        docName: '',
        docCode: '',
        docStatus: '',
        deleted: ''
      }
      this.getTableData()
    },

    async delRow(row) {
      try {
        await this.$confirms({
          title: this.$t('GLOBAL._TS'),
          message: this.$t('GLOBAL._COMFIRM'),
          confirmText: this.$t('GLOBAL._QD'),
          cancelText: this.$t('GLOBAL._QX')
        })
        const res = await delSopDoc([row.ID])
        if (res.success) {
          this.$message.success(res.msg || '删除成功')
          this.getTableData()
        } else {
          this.$message.error(res.msg || '删除失败')
        }
      } catch (err) {
        if (err) {
          console.error('删除数据失败:', err)
          this.$message.error('删除失败')
        }
      }
    },

    async getTableData(data) {
      this.tableLoading = true
      try {
        const res = await getSopDocList(this.searchForm)
        if (res.success) {
          this.tableData = res.response.data || []
          this.total = res.response.dataCount || 0
        } else {
          this.$message.error(res.msg || '获取数据失败')
        }
      } catch (err) {
        console.error('获取表格数据失败:', err)
        this.$message.error('获取数据失败')
        throw err
      } finally {
        this.tableLoading = false
      }
    },
    
    async getDirTree() {
      this.treeLoading = true
      try {
        const res = await getSopDirTree()
        if (res.success) {
          this.treeData = res.response || []
        } else {
          this.$message.error(res.msg || '获取目录树失败')
        }
      } catch (err) {
        console.error('获取目录树失败:', err)
        this.$message.error('获取目录树失败')
        throw err
      } finally {
        this.treeLoading = false
      }
    },
    
    handleNodeClick(data) {
      // 保留分页配置,重置其他搜索条件
      const { pageIndex, pageSize } = this.searchForm
      this.searchForm = {
        pageIndex,
        pageSize,
        dirId: data.id,
        docName: '',
        docCode: '',
        docStatus: '',
        deleted: ''
      }
      this.getTableData()
    },
    
    showSopDirDialog(data) {
      this.sopDirDialogForm = { ...data }
      this.sopDirDialogVisible = true
      this.$nextTick(() => {
        this.$refs.sopDirFormDialog.show(data, 'show')
      })
    },
    
    addSopDirChild(data) {
      this.sopDirDialogForm = { parentId: data.id }
      this.sopDirDialogVisible = true
      this.$nextTick(() => {
        this.$refs.sopDirFormDialog.show(data, 'add')
      })
    },
    
    async deleteSopDir(data) {
      try {
        await this.$confirms({
          title: this.$t('GLOBAL._TS'),
          message: '确定删除该目录吗？',
          confirmText: this.$t('GLOBAL._QD'),
          cancelText: this.$t('GLOBAL._QX')
        })
        const res = await delSopDir([data.id])
        if (res.success) {
          this.$message.success(res.msg || '删除成功')
          await this.getDirTree()
        } else {
          this.$message.error(res.msg || '删除失败')
        }
      } catch (err) {
        if (err) {
          console.error('删除目录失败:', err)
          this.$message.error('删除失败')
        }
      }
    },

    // 展开所有节点
    expandAll() {
      this.expandNodes(this.$refs.tree.store.root, true)          
    },

    // 收起所有节点
    collapseAll() {
      this.expandNodes(this.$refs.tree.store.root, false)
    },

    // 处理文件下载
    async handleDownload(row) {
      try {
        const res = await downloadSopDoc(row.FileUuid)
        // 创建下载链接
        const blob = new Blob([res], { type: res.type })
        // const fileName = row.DocName.split('.').slice(0, -1).join('.') // 移除最后一个扩展名
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = row.DocName
        link.click()
        window.URL.revokeObjectURL(link.href)
      } catch (err) {
        console.error('文件下载失败:', err)
        this.$message.error('文件下载失败')
      }
    },
    //树节点展开关闭
    expandNodes(node, type){
      node.expanded = type;
      for(let i = 0; i<node.childNodes.length; i++){
        node.childNodes[i].expanded = type;
        if(node.childNodes[i].childNodes.length > 0){
          this.expandNodes(node.childNodes[i], type);
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.root-layout {
  height: calc(100% - 60px);
}

.root-left {
  height: 100%;
  padding: 10px;
  overflow: auto;
  background-color: #f5f7fa;
  border-radius: 4px;

  .tree-toolbar {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;
    .tree-title {
      font-weight: 500;
    }
  }

  .tree-node-actions {
    display: none;
  }

  .el-tree-node__content:hover {
    .tree-node-actions {
      display: inline-block;
    }
  }
}

.root-right {
  padding: 0 12px;
  height: 100%;
  overflow: auto;

  .InventorySearchBox {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;

    .search-form {
      :deep(.el-form) {
        .el-form-item--small.el-form-item {
          margin-bottom: 0;
        }
      }

      .form-content {
        padding: 4px;
        
        .search-area {
          .search-row {
            display: flex;
            align-items: center;
            gap: 4px;

            .el-form-item {
              margin: 0;
              flex: none;
              
              .el-form-item__label {
                padding-right: 4px;
                line-height: 26px;
                font-size: 12px;
              }

              .el-form-item__content {
                line-height: 26px;

                .el-input,
                .el-select {
                  :deep(.el-input__inner) {
                    height: 26px;
                    line-height: 26px;
                    padding: 0 8px;
                    font-size: 12px;
                  }

                  :deep(.el-input-group__append) {
                    padding: 0;
                    .el-button {
                      padding: 0 10px;
                      height: 26px;
                      border: none;
                    }
                  }
                }
              }
            }

            .el-button {
              height: 26px;
              padding: 0 10px;
              font-size: 12px;
              margin-left: 4px;
              
              [class^="el-icon-"] {
                margin-right: 3px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  .root-main {
    margin-top: 12px;

    .el-table {
      border-radius: 4px;
      overflow: hidden;
    }
  }
}
</style>
