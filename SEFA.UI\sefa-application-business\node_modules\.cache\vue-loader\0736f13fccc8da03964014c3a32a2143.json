{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=style&index=0&id=52aa7681&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236826787}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsbA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 调试信息 -->\n                            <div v-if=\"!tableLoading && tableData.length === 0\" style=\"text-align: center; padding: 20px; color: #999;\">\n                                暂无数据\n                            </div>\n                            <div v-if=\"tableData.length > 0\" style=\"margin-bottom: 8px; font-size: 12px; color: #666;\">\n                                共 {{ total }} 条数据\n                            </div>\n\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求权限数据\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}