{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=style&index=0&id=52aa7681&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750218254563}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb290IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBoZWlnaHQ6IDEwMCU7CgogICAgLnJvb3QtbGVmdCB7CiAgICAgICAgd2lkdGg6IDI4MHB4OwogICAgICAgIHBhZGRpbmc6IDEwcHg7CiAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2ViZWVmNTsKICAgICAgICBvdmVyZmxvdzogYXV0bzsKICAgIH0KCiAgICAucm9vdC1yaWdodCB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIH0KCiAgICAucm9vdC1oZWFkIHsKICAgICAgICBwYWRkaW5nOiAxMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICB9CgogICAgLnJvb3QtbWFpbiB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBwYWRkaW5nOiAwIDEwcHg7CiAgICAgICAgb3ZlcmZsb3c6IGF1dG87CiAgICB9CgogICAgLnJvb3QtZm9vdGVyIHsKICAgICAgICBwYWRkaW5nOiAxMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICB9Cn0KCi5lbC1mb3JtLWl0ZW0tLXNtYWxsLmVsLWZvcm0taXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAwcHg7Cn0KCi5tdC04cCB7CiAgICBtYXJnaW4tdG9wOiA4cHg7Cn0KCi5wZC1sZWZ0IHsKICAgIHBhZGRpbmctbGVmdDogNXB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqLA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n                    <el-form-item>\n                        <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n                            {{ $t('DFM_JSGL._FPQX') }}\n                        </el-button>\n                    </el-form-item>\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <el-table class=\"mt-3\" :height=\"mainH\" border :data=\"tableData\" style=\"width: 100%\">\n                    <el-table-column\n                        v-for=\"item in tableName\"\n                        :default-sort=\"{ prop: 'date', order: 'descending' }\"\n                        :key=\"item.ID\"\n                        :prop=\"item.field\"\n                        :label=\"item.label\"\n                        :width=\"item.width\"\n                        :align=\"item.alignType\"\n                        sortable\n                        show-overflow-tooltip\n                    >\n                        <template slot-scope=\"scope\">\n                            {{ scope.row[item.field] }}\n                        </template>\n                    </el-table-column>\n                    <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                        <template slot-scope=\"scope\">\n                            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n                            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                        </template>\n                    </el-table-column>\n                </el-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n            <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog';\nimport SopDir from '../sopDir/index.vue';\nimport { delSopPermission, getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getTableHead } from '@/util/dataDictionary.js';\n\nimport { sopPermissionColumn } from '@/api/SOP/sopPermission.js';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        FormDialog,\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [{}],\n            hansObj: this.$t('SopPermission.table'),\n            tableName: [],\n            loading: false,\n            tableOption: [],\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getZHHans();\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        getZHHans() {\n            for (let key in this.hansObj) {\n                this.tableName = getTableHead(this.hansObj, this.tableOption);\n            }\n        },\n        showDialog(row) {\n            this.$refs.formDialog.show(row);\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        delRow(row) {\n            this.$confirms({\n                title: this.$t('GLOBAL._TS'),\n                message: this.$t('GLOBAL._COMFIRM'),\n                confirmText: this.$t('GLOBAL._QD'),\n                cancelText: this.$t('GLOBAL._QX')\n            })\n                .then(async () => {\n                    delSopPermission([row.ID]).then(res => {\n                        this.$message.success(res.msg);\n                        this.getTableData();\n                    });\n                })\n                .catch(err => {\n                    console.log(err);\n                });\n        },\n\n        getTableData(data) {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data;\n                this.total = res.response.dataCount;\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}