{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=style&index=0&id=52aa7681&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750235480077}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5yb290IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBoZWlnaHQ6IDEwMCU7CgogICAgLnJvb3QtbGVmdCB7CiAgICAgICAgd2lkdGg6IDI4MHB4OwogICAgICAgIHBhZGRpbmc6IDEwcHg7CiAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2ViZWVmNTsKICAgICAgICBvdmVyZmxvdzogYXV0bzsKICAgIH0KCiAgICAucm9vdC1yaWdodCB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIH0KCiAgICAucm9vdC1oZWFkIHsKICAgICAgICBwYWRkaW5nOiAxMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICB9CgogICAgLnJvb3QtbWFpbiB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBwYWRkaW5nOiAwIDEwcHg7CiAgICAgICAgb3ZlcmZsb3c6IGF1dG87CiAgICB9CgogICAgLnJvb3QtZm9vdGVyIHsKICAgICAgICBwYWRkaW5nOiAxMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICB9Cn0KCi5lbC1mb3JtLWl0ZW0tLXNtYWxsLmVsLWZvcm0taXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAwcHg7Cn0KCi5tdC04cCB7CiAgICBtYXJnaW4tdG9wOiA4cHg7Cn0KCi5wZC1sZWZ0IHsKICAgIHBhZGRpbmctbGVmdDogNXB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <vxe-table\n                    class=\"mt-3\"\n                    :height=\"mainH\"\n                    border\n                    :data=\"tableData\"\n                    style=\"width: 100%\"\n                    :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                    ref=\"xTable\">\n\n                    <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                    <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                            <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                        </template>\n                    </vxe-column>\n                </vxe-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SopDir from '../sopDir/index.vue';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            loading: false,\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permLevel = 0;\n            if (row.Preview) permLevel |= 1;    // 预览 (1)\n            if (row.Download) permLevel |= 2;   // 下载 (2)\n            if (row.Search) permLevel |= 3;     // 检索 (3)\n            if (row.Upload) permLevel |= 4;     // 上传 (4)\n            if (row.Delete) permLevel |= 8;     // 删除 (8)\n\n            row.PermLevel = permLevel.toString();\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            const level = parseInt(permLevel) || 0;\n            row.Preview = (level & 1) > 0;      // 预览 (1)\n            row.Download = (level & 2) > 0;     // 下载 (2)\n            row.Search = (level & 3) > 0;       // 检索 (3)\n            row.Upload = (level & 4) > 0;       // 上传 (4)\n            row.Delete = (level & 8) > 0;       // 删除 (8)\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n\n\n        getTableData() {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data || [];\n                this.total = res.response.dataCount || 0;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            }).catch(err => {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '3', // 1+2 = 3 (预览+下载)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 1 (仅预览)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}