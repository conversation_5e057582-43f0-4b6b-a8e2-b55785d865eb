{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=style&index=0&id=52aa7681&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750237498523}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4cA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 数据统计信息 -->\n                            <div class=\"data-summary\" v-if=\"tableData.length > 0\">\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">总计角色：</span>\n                                    <span class=\"summary-value\">{{ total }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">已配置权限：</span>\n                                    <span class=\"summary-value\">{{ getConfiguredCount() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">未配置权限：</span>\n                                    <span class=\"summary-value\">{{ total - getConfiguredCount() }}</span>\n                                </div>\n                            </div>\n\n                            <el-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%; border-radius: 4px;\"\n                                v-loading=\"tableLoading\"\n                                :empty-text=\"'暂无数据'\"\n                                ref=\"permissionTable\">\n\n                                <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n                                <el-table-column prop=\"RoleName\" :label=\"$t('SopPermission.table.RoleName')\" min-width=\"150\" show-overflow-tooltip></el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Preview\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Download\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Search\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Upload\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Delete\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-button size=\"mini\" type=\"primary\" plain @click=\"selectAll(scope.row)\" style=\"margin-right: 4px;\">\n                                            {{ $t('SopPermission.table.SelectAll') }}\n                                        </el-button>\n                                        <el-button size=\"mini\" type=\"warning\" plain @click=\"cancelAll(scope.row)\">\n                                            {{ $t('SopPermission.table.CancelAll') }}\n                                        </el-button>\n                                    </template>\n                                </el-table-column>\n                            </el-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求根目录权限数据\n            this.searchForm.grantId = 'root'; // 设置默认查询根目录\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: data.id || data.dirCode || 'root', // 使用目录ID或编码作为grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: this.searchForm.grantId, // 保留当前的grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 获取已配置权限的角色数量\n        getConfiguredCount() {\n            return this.tableData.filter(row => {\n                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;\n            }).length;\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    padding: 16px;\n    border-radius: 8px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    margin-bottom: 16px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 8px;\n                line-height: 26px;\n                font-size: 13px;\n                color: white;\n                font-weight: 500;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 32px;\n                    line-height: 32px;\n                    padding: 0 12px;\n                    font-size: 13px;\n                    border-radius: 6px;\n                    border: 2px solid rgba(255, 255, 255, 0.3);\n                    background: rgba(255, 255, 255, 0.9);\n                    transition: all 0.3s ease;\n\n                    &:focus {\n                      border-color: rgba(255, 255, 255, 0.8);\n                      background: white;\n                      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);\n                    }\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 8px;\n              margin-left: 12px;\n\n              .el-button {\n                height: 32px;\n                padding: 0 16px;\n                font-size: 13px;\n                border-radius: 6px;\n                border: 2px solid rgba(255, 255, 255, 0.3);\n                background: rgba(255, 255, 255, 0.9);\n                color: #333;\n                font-weight: 500;\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: white;\n                  border-color: rgba(255, 255, 255, 0.8);\n                  transform: translateY(-1px);\n                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n                }\n\n                &.el-button--primary {\n                  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                  border: none;\n                  color: white;\n\n                  &:hover {\n                    background: linear-gradient(135deg, #66b1ff 0%, #5cdbd3 100%);\n                  }\n                }\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 4px;\n                  font-size: 13px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .data-summary {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 8px;\n      padding: 16px;\n      margin-bottom: 16px;\n      display: flex;\n      justify-content: space-around;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n\n      .summary-item {\n        text-align: center;\n        color: white;\n\n        .summary-label {\n          display: block;\n          font-size: 12px;\n          opacity: 0.9;\n          margin-bottom: 4px;\n        }\n\n        .summary-value {\n          display: block;\n          font-size: 24px;\n          font-weight: bold;\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n        }\n      }\n    }\n\n    .el-table {\n      border-radius: 8px;\n      overflow: hidden;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n\n      :deep(.el-table__header) {\n        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\n        th {\n          background: transparent !important;\n          color: #2c3e50;\n          font-weight: 600;\n          border-bottom: 2px solid #e4e7ed;\n        }\n      }\n\n      :deep(.el-table__body) {\n        tr {\n          transition: all 0.3s ease;\n\n          &:hover {\n            background-color: #f8f9ff !important;\n            transform: translateY(-1px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          }\n\n          td {\n            border-bottom: 1px solid #f0f0f0;\n            padding: 12px 0;\n\n            .el-checkbox {\n              :deep(.el-checkbox__input) {\n                .el-checkbox__inner {\n                  border-radius: 4px;\n                  transition: all 0.3s ease;\n\n                  &:hover {\n                    border-color: #409eff;\n                    transform: scale(1.1);\n                  }\n                }\n\n                &.is-checked .el-checkbox__inner {\n                  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                  border-color: #409eff;\n                }\n              }\n            }\n\n            .el-button {\n              border-radius: 6px;\n              font-size: 11px;\n              padding: 4px 8px;\n              transition: all 0.3s ease;\n\n              &:hover {\n                transform: translateY(-1px);\n                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n              }\n\n              &.el-button--primary.is-plain {\n                background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                border: none;\n                color: white;\n\n                &:hover {\n                  background: linear-gradient(135deg, #66b1ff 0%, #5cdbd3 100%);\n                }\n              }\n\n              &.el-button--warning.is-plain {\n                background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);\n                border: none;\n                color: white;\n\n                &:hover {\n                  background: linear-gradient(135deg, #ebb563 0%, #f78989 100%);\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-footer {\n    margin-top: 16px;\n    padding: 12px;\n    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n    border-radius: 8px;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n\n    :deep(.el-pagination) {\n      .el-pager li {\n        border-radius: 6px;\n        margin: 0 2px;\n        transition: all 0.3s ease;\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        }\n\n        &.active {\n          background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n          color: white;\n        }\n      }\n\n      .btn-prev, .btn-next {\n        border-radius: 6px;\n        transition: all 0.3s ease;\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        }\n      }\n\n      .el-select .el-input {\n        .el-input__inner {\n          border-radius: 6px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}