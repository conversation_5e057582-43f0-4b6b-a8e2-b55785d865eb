{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750237498523}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkKA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 数据统计信息 -->\n                            <div class=\"data-summary\" v-if=\"tableData.length > 0\">\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">总计角色：</span>\n                                    <span class=\"summary-value\">{{ total }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">已配置权限：</span>\n                                    <span class=\"summary-value\">{{ getConfiguredCount() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">未配置权限：</span>\n                                    <span class=\"summary-value\">{{ total - getConfiguredCount() }}</span>\n                                </div>\n                            </div>\n\n                            <el-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%; border-radius: 4px;\"\n                                v-loading=\"tableLoading\"\n                                :empty-text=\"'暂无数据'\"\n                                ref=\"permissionTable\">\n\n                                <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n                                <el-table-column prop=\"RoleName\" :label=\"$t('SopPermission.table.RoleName')\" min-width=\"150\" show-overflow-tooltip></el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Preview\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Download\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Search\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Upload\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox v-model=\"scope.row.Delete\" @change=\"updatePermLevel(scope.row)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-button size=\"mini\" type=\"primary\" plain @click=\"selectAll(scope.row)\" style=\"margin-right: 4px;\">\n                                            {{ $t('SopPermission.table.SelectAll') }}\n                                        </el-button>\n                                        <el-button size=\"mini\" type=\"warning\" plain @click=\"cancelAll(scope.row)\">\n                                            {{ $t('SopPermission.table.CancelAll') }}\n                                        </el-button>\n                                    </template>\n                                </el-table-column>\n                            </el-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求根目录权限数据\n            this.searchForm.grantId = 'root'; // 设置默认查询根目录\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: data.id || data.dirCode || 'root', // 使用目录ID或编码作为grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: this.searchForm.grantId, // 保留当前的grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 获取已配置权限的角色数量\n        getConfiguredCount() {\n            return this.tableData.filter(row => {\n                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;\n            }).length;\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    padding: 16px;\n    border-radius: 8px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    margin-bottom: 16px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 8px;\n                line-height: 26px;\n                font-size: 13px;\n                color: white;\n                font-weight: 500;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 32px;\n                    line-height: 32px;\n                    padding: 0 12px;\n                    font-size: 13px;\n                    border-radius: 6px;\n                    border: 2px solid rgba(255, 255, 255, 0.3);\n                    background: rgba(255, 255, 255, 0.9);\n                    transition: all 0.3s ease;\n\n                    &:focus {\n                      border-color: rgba(255, 255, 255, 0.8);\n                      background: white;\n                      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);\n                    }\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 8px;\n              margin-left: 12px;\n\n              .el-button {\n                height: 32px;\n                padding: 0 16px;\n                font-size: 13px;\n                border-radius: 6px;\n                border: 2px solid rgba(255, 255, 255, 0.3);\n                background: rgba(255, 255, 255, 0.9);\n                color: #333;\n                font-weight: 500;\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: white;\n                  border-color: rgba(255, 255, 255, 0.8);\n                  transform: translateY(-1px);\n                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n                }\n\n                &.el-button--primary {\n                  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                  border: none;\n                  color: white;\n\n                  &:hover {\n                    background: linear-gradient(135deg, #66b1ff 0%, #5cdbd3 100%);\n                  }\n                }\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 4px;\n                  font-size: 13px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .data-summary {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 8px;\n      padding: 16px;\n      margin-bottom: 16px;\n      display: flex;\n      justify-content: space-around;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n\n      .summary-item {\n        text-align: center;\n        color: white;\n\n        .summary-label {\n          display: block;\n          font-size: 12px;\n          opacity: 0.9;\n          margin-bottom: 4px;\n        }\n\n        .summary-value {\n          display: block;\n          font-size: 24px;\n          font-weight: bold;\n          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n        }\n      }\n    }\n\n    .el-table {\n      border-radius: 8px;\n      overflow: hidden;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n\n      :deep(.el-table__header) {\n        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\n        th {\n          background: transparent !important;\n          color: #2c3e50;\n          font-weight: 600;\n          border-bottom: 2px solid #e4e7ed;\n        }\n      }\n\n      :deep(.el-table__body) {\n        tr {\n          transition: all 0.3s ease;\n\n          &:hover {\n            background-color: #f8f9ff !important;\n            transform: translateY(-1px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          }\n\n          td {\n            border-bottom: 1px solid #f0f0f0;\n            padding: 12px 0;\n\n            .el-checkbox {\n              :deep(.el-checkbox__input) {\n                .el-checkbox__inner {\n                  border-radius: 4px;\n                  transition: all 0.3s ease;\n\n                  &:hover {\n                    border-color: #409eff;\n                    transform: scale(1.1);\n                  }\n                }\n\n                &.is-checked .el-checkbox__inner {\n                  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                  border-color: #409eff;\n                }\n              }\n            }\n\n            .el-button {\n              border-radius: 6px;\n              font-size: 11px;\n              padding: 4px 8px;\n              transition: all 0.3s ease;\n\n              &:hover {\n                transform: translateY(-1px);\n                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n              }\n\n              &.el-button--primary.is-plain {\n                background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n                border: none;\n                color: white;\n\n                &:hover {\n                  background: linear-gradient(135deg, #66b1ff 0%, #5cdbd3 100%);\n                }\n              }\n\n              &.el-button--warning.is-plain {\n                background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);\n                border: none;\n                color: white;\n\n                &:hover {\n                  background: linear-gradient(135deg, #ebb563 0%, #f78989 100%);\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-footer {\n    margin-top: 16px;\n    padding: 12px;\n    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n    border-radius: 8px;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n\n    :deep(.el-pagination) {\n      .el-pager li {\n        border-radius: 6px;\n        margin: 0 2px;\n        transition: all 0.3s ease;\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        }\n\n        &.active {\n          background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);\n          color: white;\n        }\n      }\n\n      .btn-prev, .btn-next {\n        border-radius: 6px;\n        transition: all 0.3s ease;\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        }\n      }\n\n      .el-select .el-input {\n        .el-input__inner {\n          border-radius: 6px;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}