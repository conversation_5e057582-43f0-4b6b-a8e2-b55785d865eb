{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236826787}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoJA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 调试信息 -->\n                            <div v-if=\"!tableLoading && tableData.length === 0\" style=\"text-align: center; padding: 20px; color: #999;\">\n                                暂无数据\n                            </div>\n                            <div v-if=\"tableData.length > 0\" style=\"margin-bottom: 8px; font-size: 12px; color: #666;\">\n                                共 {{ total }} 条数据\n                            </div>\n\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求权限数据\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}