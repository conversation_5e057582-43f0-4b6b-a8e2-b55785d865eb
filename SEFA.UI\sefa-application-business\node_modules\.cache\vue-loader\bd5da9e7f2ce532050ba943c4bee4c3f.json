{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236593164}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4IA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置和查询条件，更新目标目录\n            const { pageIndex, pageSize, dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 初始化所有权限为false\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n\n            if (!permLevel) return;\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p);\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        row.Preview = true;     // 预览\n                        break;\n                    case '2':\n                        row.Download = true;    // 下载\n                        break;\n                    case '3':\n                        row.Search = true;      // 检索\n                        break;\n                    case '4':\n                        row.Upload = true;      // 上传\n                        break;\n                    case '8':\n                        row.Delete = true;      // 删除\n                        break;\n                }\n            });\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            if (!this.searchForm.targetId) {\n                this.tableData = [];\n                this.total = 0;\n                return;\n            }\n\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                if (res.success) {\n                    this.tableData = res.response.data || [];\n                    this.total = res.response.dataCount || 0;\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    this.$message.error(res.msg || '获取权限数据失败');\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '1,2,3,4,8', // 所有权限：预览,下载,检索,上传,删除\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '1,2,3', // 预览,下载,检索\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 仅预览\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    },\n                    {\n                        ID: '4',\n                        RoleName: '编辑者',\n                        PermLevel: '1,2,3,4', // 预览,下载,检索,上传\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'editor-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}