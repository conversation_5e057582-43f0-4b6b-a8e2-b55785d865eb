{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750236171338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsHA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"root-head\">\n                            <div class=\"selected-dir-info\" v-if=\"selectedDir\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <vxe-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%\"\n                                :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                                v-loading=\"tableLoading\"\n                                ref=\"xTable\">\n\n                                <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                                <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                                    </template>\n                                </vxe-column>\n\n                                <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template #default=\"{ row }\">\n                                        <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                                        <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                                    </template>\n                                </vxe-column>\n                            </vxe-table>\n                        </div>\n                        <div class=\"root-footer\">\n                            <el-pagination\n                                class=\"mt-3\"\n                                @size-change=\"handleSizeChange\"\n                                @current-change=\"handleCurrentChange\"\n                                :current-page=\"searchForm.pageIndex\"\n                                :page-sizes=\"[10, 20, 50, 100, 500]\"\n                                :page-size=\"searchForm.pageSize\"\n                                layout=\"->,total, sizes, prev, pager, next, jumper\"\n                                :total=\"total\"\n                                background\n                            ></el-pagination>\n                        </div>\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留分页配置,重置其他搜索条件\n            const { pageIndex, pageSize } = this.searchForm;\n            this.searchForm = {\n                pageIndex,\n                pageSize,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permLevel = 0;\n            if (row.Preview) permLevel |= 1;    // 预览 (1)\n            if (row.Download) permLevel |= 2;   // 下载 (2)\n            if (row.Search) permLevel |= 3;     // 检索 (3)\n            if (row.Upload) permLevel |= 4;     // 上传 (4)\n            if (row.Delete) permLevel |= 8;     // 删除 (8)\n\n            row.PermLevel = permLevel.toString();\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            const level = parseInt(permLevel) || 0;\n            row.Preview = (level & 1) > 0;      // 预览 (1)\n            row.Download = (level & 2) > 0;     // 下载 (2)\n            row.Search = (level & 3) > 0;       // 检索 (3)\n            row.Upload = (level & 4) > 0;       // 上传 (4)\n            row.Delete = (level & 8) > 0;       // 删除 (8)\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n\n\n        async getTableData() {\n            if (!this.searchForm.targetId) {\n                this.tableData = [];\n                this.total = 0;\n                return;\n            }\n\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                if (res.success) {\n                    this.tableData = res.response.data || [];\n                    this.total = res.response.dataCount || 0;\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    this.$message.error(res.msg || '获取权限数据失败');\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '3', // 1+2 = 3 (预览+下载)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 1 (仅预览)\n                        TargetId: this.searchForm.targetId,\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .vxe-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}