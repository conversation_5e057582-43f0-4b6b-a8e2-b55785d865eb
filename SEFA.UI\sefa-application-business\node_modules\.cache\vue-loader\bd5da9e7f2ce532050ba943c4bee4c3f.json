{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750218254563}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n                    <el-form-item>\n                        <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n                            {{ $t('DFM_JSGL._FPQX') }}\n                        </el-button>\n                    </el-form-item>\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <el-table class=\"mt-3\" :height=\"mainH\" border :data=\"tableData\" style=\"width: 100%\">\n                    <el-table-column\n                        v-for=\"item in tableName\"\n                        :default-sort=\"{ prop: 'date', order: 'descending' }\"\n                        :key=\"item.ID\"\n                        :prop=\"item.field\"\n                        :label=\"item.label\"\n                        :width=\"item.width\"\n                        :align=\"item.alignType\"\n                        sortable\n                        show-overflow-tooltip\n                    >\n                        <template slot-scope=\"scope\">\n                            {{ scope.row[item.field] }}\n                        </template>\n                    </el-table-column>\n                    <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                        <template slot-scope=\"scope\">\n                            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n                            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                        </template>\n                    </el-table-column>\n                </el-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n            <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog';\nimport SopDir from '../sopDir/index.vue';\nimport { delSopPermission, getSopPermissionList } from '@/api/SOP/sopPermission';\nimport { getTableHead } from '@/util/dataDictionary.js';\n\nimport { sopPermissionColumn } from '@/api/SOP/sopPermission.js';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        FormDialog,\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [{}],\n            hansObj: this.$t('SopPermission.table'),\n            tableName: [],\n            loading: false,\n            tableOption: [],\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getZHHans();\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        getZHHans() {\n            for (let key in this.hansObj) {\n                this.tableName = getTableHead(this.hansObj, this.tableOption);\n            }\n        },\n        showDialog(row) {\n            this.$refs.formDialog.show(row);\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n        delRow(row) {\n            this.$confirms({\n                title: this.$t('GLOBAL._TS'),\n                message: this.$t('GLOBAL._COMFIRM'),\n                confirmText: this.$t('GLOBAL._QD'),\n                cancelText: this.$t('GLOBAL._QX')\n            })\n                .then(async () => {\n                    delSopPermission([row.ID]).then(res => {\n                        this.$message.success(res.msg);\n                        this.getTableData();\n                    });\n                })\n                .catch(err => {\n                    console.log(err);\n                });\n        },\n\n        getTableData(data) {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data;\n                this.total = res.response.dataCount;\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}