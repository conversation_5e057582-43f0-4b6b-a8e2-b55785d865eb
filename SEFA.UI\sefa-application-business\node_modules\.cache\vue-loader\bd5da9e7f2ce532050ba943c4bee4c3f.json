{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750235480077}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCAnQC92aWV3cy9JbnZlbnRvcnkvbXlzdHlsZS5zY3NzJzsKCmltcG9ydCBTb3BEaXIgZnJvbSAnLi4vc29wRGlyL2luZGV4LnZ1ZSc7CmltcG9ydCB7IGdldFNvcFBlcm1pc3Npb25MaXN0IH0gZnJvbSAnQC9hcGkvU09QL3NvcFBlcm1pc3Npb24nOwoKZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogJ2luZGV4LnZ1ZScsCiAgICBjb21wb25lbnRzOiB7CiAgICAgICAgU29wRGlyCiAgICB9LAogICAgZGF0YSgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICBzZWFyY2hGb3JtOiB7CiAgICAgICAgICAgICAgICBwYWdlSW5kZXg6IDEsCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgICAgICAgICBkaXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgICAgICAgICBkaXJDb2RlOiB1bmRlZmluZWQsCiAgICAgICAgICAgICAgICB0YXJnZXRJZDogdW5kZWZpbmVkLAogICAgICAgICAgICAgICAgdGFyZ2V0VHlwZTogdW5kZWZpbmVkLAogICAgICAgICAgICAgICAgZ3JhbnRUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgICAgICAgICBncmFudElkOiB1bmRlZmluZWQsCiAgICAgICAgICAgICAgICBwZXJtTGV2ZWw6IHVuZGVmaW5lZAogICAgICAgICAgICB9LAogICAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgICAgIG1haW5IOiAwLAogICAgICAgICAgICBidXR0b25PcHRpb246IHsKICAgICAgICAgICAgICAgIG5hbWU6ICdTb3BQZXJtaXNzaW9uJywKICAgICAgICAgICAgICAgIHNlcnZlSXA6ICdiYXNlVVJMX1NPUCcsCiAgICAgICAgICAgICAgICB1cGxvYWRVcmw6ICcvYXBpL1NvcFBlcm1pc3Npb24vSW1wb3J0RGF0YScsIC8v5a+85YWlCiAgICAgICAgICAgICAgICBleHBvcnRVcmw6ICcvYXBpL1NvcFBlcm1pc3Npb24vRXhwb3J0RGF0YScsIC8v5a+85Ye6CiAgICAgICAgICAgICAgICBEb3duTG9hZFVybDogJy9hcGkvU29wUGVybWlzc2lvbi9Eb3duTG9hZFRlbXBsYXRlJyAvL+S4i+i9veaooeadvwogICAgICAgICAgICB9CiAgICAgICAgfTsKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCk7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICB0aGlzLm1haW5IID0gdGhpcy4kd2ViSGVpZ2h0KGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoJ3Jvb3QtaGVhZCcpWzBdLmNsaWVudEhlaWdodCwgZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgncm9vdCcpWzBdLmNsaWVudEhlaWdodCk7CiAgICAgICAgfSk7CiAgICAgICAgd2luZG93Lm9ucmVzaXplID0gKCkgPT4gewogICAgICAgICAgICB0aGlzLm1haW5IID0gdGhpcy4kd2ViSGVpZ2h0KGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoJ3Jvb3QtaGVhZCcpWzBdLmNsaWVudEhlaWdodCwgZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgncm9vdCcpWzBdLmNsaWVudEhlaWdodCk7CiAgICAgICAgfTsKICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgICAgaGFuZGxlRGlyQ2xpY2sobm9kZSkgewogICAgICAgICAgICB0aGlzLnNlYXJjaEZvcm0udGFyZ2V0SWQgPSBub2RlLmlkOwogICAgICAgICAgICB0aGlzLnNlYXJjaEZvcm0udGFyZ2V0VHlwZSA9IDE7IC8vIDHooajnpLrnm67lvZUKICAgICAgICAgICAgdGhpcy5nZXRUYWJsZURhdGEoKTsKICAgICAgICB9LAoKICAgICAgICAvLyDmnYPpmZDnuqfliKvmm7TmlrDmlrnms5UKICAgICAgICB1cGRhdGVQZXJtTGV2ZWwocm93KSB7CiAgICAgICAgICAgIGxldCBwZXJtTGV2ZWwgPSAwOwogICAgICAgICAgICBpZiAocm93LlByZXZpZXcpIHBlcm1MZXZlbCB8PSAxOyAgICAvLyDpooTop4ggKDEpCiAgICAgICAgICAgIGlmIChyb3cuRG93bmxvYWQpIHBlcm1MZXZlbCB8PSAyOyAgIC8vIOS4i+i9vSAoMikKICAgICAgICAgICAgaWYgKHJvdy5TZWFyY2gpIHBlcm1MZXZlbCB8PSAzOyAgICAgLy8g5qOA57SiICgzKQogICAgICAgICAgICBpZiAocm93LlVwbG9hZCkgcGVybUxldmVsIHw9IDQ7ICAgICAvLyDkuIrkvKAgKDQpCiAgICAgICAgICAgIGlmIChyb3cuRGVsZXRlKSBwZXJtTGV2ZWwgfD0gODsgICAgIC8vIOWIoOmZpCAoOCkKCiAgICAgICAgICAgIHJvdy5QZXJtTGV2ZWwgPSBwZXJtTGV2ZWwudG9TdHJpbmcoKTsKICAgICAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55SoQVBJ5L+d5a2Y5p2D6ZmQ5Y+Y5pu0CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmnYPpmZDmm7TmlrA6Jywgcm93LlJvbGVOYW1lLCAn5p2D6ZmQ57qn5YirOicsIHJvdy5QZXJtTGV2ZWwpOwogICAgICAgIH0sCgogICAgICAgIC8vIOWFqOmAieadg+mZkAogICAgICAgIHNlbGVjdEFsbChyb3cpIHsKICAgICAgICAgICAgcm93LlByZXZpZXcgPSB0cnVlOwogICAgICAgICAgICByb3cuRG93bmxvYWQgPSB0cnVlOwogICAgICAgICAgICByb3cuU2VhcmNoID0gdHJ1ZTsKICAgICAgICAgICAgcm93LlVwbG9hZCA9IHRydWU7CiAgICAgICAgICAgIHJvdy5EZWxldGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnVwZGF0ZVBlcm1MZXZlbChyb3cpOwogICAgICAgIH0sCgogICAgICAgIC8vIOWPlua2iOWFqOmAiQogICAgICAgIGNhbmNlbEFsbChyb3cpIHsKICAgICAgICAgICAgcm93LlByZXZpZXcgPSBmYWxzZTsKICAgICAgICAgICAgcm93LkRvd25sb2FkID0gZmFsc2U7CiAgICAgICAgICAgIHJvdy5TZWFyY2ggPSBmYWxzZTsKICAgICAgICAgICAgcm93LlVwbG9hZCA9IGZhbHNlOwogICAgICAgICAgICByb3cuRGVsZXRlID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMudXBkYXRlUGVybUxldmVsKHJvdyk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g6Kej5p6Q5p2D6ZmQ57qn5Yir5a2X56ym5LiyCiAgICAgICAgcGFyc2VQZXJtTGV2ZWwocGVybUxldmVsLCByb3cpIHsKICAgICAgICAgICAgY29uc3QgbGV2ZWwgPSBwYXJzZUludChwZXJtTGV2ZWwpIHx8IDA7CiAgICAgICAgICAgIHJvdy5QcmV2aWV3ID0gKGxldmVsICYgMSkgPiAwOyAgICAgIC8vIOmihOiniCAoMSkKICAgICAgICAgICAgcm93LkRvd25sb2FkID0gKGxldmVsICYgMikgPiAwOyAgICAgLy8g5LiL6L29ICgyKQogICAgICAgICAgICByb3cuU2VhcmNoID0gKGxldmVsICYgMykgPiAwOyAgICAgICAvLyDmo4DntKIgKDMpCiAgICAgICAgICAgIHJvdy5VcGxvYWQgPSAobGV2ZWwgJiA0KSA+IDA7ICAgICAgIC8vIOS4iuS8oCAoNCkKICAgICAgICAgICAgcm93LkRlbGV0ZSA9IChsZXZlbCAmIDgpID4gMDsgICAgICAgLy8g5Yig6ZmkICg4KQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlKSB7CiAgICAgICAgICAgIHRoaXMuc2VhcmNoRm9ybS5wYWdlSW5kZXggPSBwYWdlOwogICAgICAgICAgICB0aGlzLmdldFRhYmxlRGF0YSgpOwogICAgICAgIH0sCiAgICAgICAgaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgICAgICAgIHRoaXMuc2VhcmNoRm9ybS5wYWdlU2l6ZSA9IHNpemU7CiAgICAgICAgICAgIHRoaXMuZ2V0VGFibGVEYXRhKCk7CiAgICAgICAgfSwKICAgICAgICBnZXRTZWFyY2hCdG4oKSB7CiAgICAgICAgICAgIHRoaXMuc2VhcmNoRm9ybS5wYWdlSW5kZXggPSAxOwogICAgICAgICAgICB0aGlzLmdldFRhYmxlRGF0YSgpOwogICAgICAgIH0sCgoKCiAgICAgICAgZ2V0VGFibGVEYXRhKCkgewogICAgICAgICAgICBnZXRTb3BQZXJtaXNzaW9uTGlzdCh0aGlzLnNlYXJjaEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzLnJlc3BvbnNlLmRhdGEgfHwgW107CiAgICAgICAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnJlc3BvbnNlLmRhdGFDb3VudCB8fCAwOwoKICAgICAgICAgICAgICAgIC8vIOino+aekOavj+ihjOeahOadg+mZkOe6p+WIqwogICAgICAgICAgICAgICAgdGhpcy50YWJsZURhdGEuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMucGFyc2VQZXJtTGV2ZWwocm93LlBlcm1MZXZlbCwgcm93KTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9KS5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5p2D6ZmQ5pWw5o2u5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICAvLyDmt7vliqDkuIDkupvmtYvor5XmlbDmja7nlKjkuo7mvJTnpLoKICAgICAgICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gWwogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgSUQ6ICcxJywKICAgICAgICAgICAgICAgICAgICAgICAgUm9sZU5hbWU6ICfnrqHnkIblkZgnLAogICAgICAgICAgICAgICAgICAgICAgICBQZXJtTGV2ZWw6ICcxNScsIC8vIDErMis0KzggPSAxNSAo5omA5pyJ5p2D6ZmQKQogICAgICAgICAgICAgICAgICAgICAgICBUYXJnZXRJZDogJ3Rlc3QtdGFyZ2V0LTEnLAogICAgICAgICAgICAgICAgICAgICAgICBHcmFudFR5cGU6IDEsCiAgICAgICAgICAgICAgICAgICAgICAgIEdyYW50SWQ6ICdhZG1pbi1yb2xlJwogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBJRDogJzInLAogICAgICAgICAgICAgICAgICAgICAgICBSb2xlTmFtZTogJ+aZrumAmueUqOaItycsCiAgICAgICAgICAgICAgICAgICAgICAgIFBlcm1MZXZlbDogJzMnLCAvLyAxKzIgPSAzICjpooTop4gr5LiL6L29KQogICAgICAgICAgICAgICAgICAgICAgICBUYXJnZXRJZDogJ3Rlc3QtdGFyZ2V0LTEnLAogICAgICAgICAgICAgICAgICAgICAgICBHcmFudFR5cGU6IDEsCiAgICAgICAgICAgICAgICAgICAgICAgIEdyYW50SWQ6ICd1c2VyLXJvbGUnCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIElEOiAnMycsCiAgICAgICAgICAgICAgICAgICAgICAgIFJvbGVOYW1lOiAn6K6/5a6iJywKICAgICAgICAgICAgICAgICAgICAgICAgUGVybUxldmVsOiAnMScsIC8vIDEgKOS7hemihOiniCkKICAgICAgICAgICAgICAgICAgICAgICAgVGFyZ2V0SWQ6ICd0ZXN0LXRhcmdldC0xJywKICAgICAgICAgICAgICAgICAgICAgICAgR3JhbnRUeXBlOiAxLAogICAgICAgICAgICAgICAgICAgICAgICBHcmFudElkOiAnZ3Vlc3Qtcm9sZScKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBdOwogICAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMudGFibGVEYXRhLmxlbmd0aDsKCiAgICAgICAgICAgICAgICAvLyDop6PmnpDmr4/ooYznmoTmnYPpmZDnuqfliKsKICAgICAgICAgICAgICAgIHRoaXMudGFibGVEYXRhLmZvckVhY2gocm93ID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnBhcnNlUGVybUxldmVsKHJvdy5QZXJtTGV2ZWwsIHJvdyk7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfQogICAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2FA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root\">\n        <div class=\"root-left\">\n            <sop-dir @node-click=\"handleDirClick\" />\n        </div>\n        <div class=\"root-right\">\n            <div class=\"root-head\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                    <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                        <el-input v-model=\"searchForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\" />\n                    </el-form-item>\n\n                    <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                        <el-input v-model=\"searchForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\" />\n                    </el-form-item>\n\n                    <el-form-item class=\"mb-2\">\n                        <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n                    </el-form-item>\n\n                </el-form>\n            </div>\n            <div class=\"root-main\">\n                <vxe-table\n                    class=\"mt-3\"\n                    :height=\"mainH\"\n                    border\n                    :data=\"tableData\"\n                    style=\"width: 100%\"\n                    :edit-config=\"{ trigger: 'click', mode: 'cell' }\"\n                    ref=\"xTable\">\n\n                    <vxe-column field=\"RoleName\" :title=\"$t('SopPermission.table.RoleName')\" width=\"150\"></vxe-column>\n\n                    <vxe-column field=\"Upload\" :title=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Upload\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Download\" :title=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Download\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Preview\" :title=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Preview\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Search\" :title=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Search\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column field=\"Delete\" :title=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-checkbox v-model=\"row.Delete\" @change=\"updatePermLevel(row)\"></vxe-checkbox>\n                        </template>\n                    </vxe-column>\n\n                    <vxe-column :title=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                        <template #default=\"{ row }\">\n                            <vxe-button type=\"text\" @click=\"selectAll(row)\">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>\n                            <vxe-button type=\"text\" @click=\"cancelAll(row)\">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>\n                        </template>\n                    </vxe-column>\n                </vxe-table>\n            </div>\n            <div class=\"root-footer\">\n                <el-pagination\n                    class=\"mt-3\"\n                    @size-change=\"handleSizeChange\"\n                    @current-change=\"handleCurrentChange\"\n                    :current-page=\"searchForm.pageIndex\"\n                    :page-sizes=\"[10, 20, 50, 100, 500]\"\n                    :page-size=\"searchForm.pageSize\"\n                    layout=\"->,total, sizes, prev, pager, next, jumper\"\n                    :total=\"total\"\n                    background\n                ></el-pagination>\n            </div>\n\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SopDir from '../sopDir/index.vue';\nimport { getSopPermissionList } from '@/api/SOP/sopPermission';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SopDir\n    },\n    data() {\n        return {\n            searchForm: {\n                pageIndex: 1,\n                pageSize: 20,\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            loading: false,\n            mainH: 0,\n            buttonOption: {\n                name: 'SopPermission',\n                serveIp: 'baseURL_SOP',\n                uploadUrl: '/api/SopPermission/ImportData', //导入\n                exportUrl: '/api/SopPermission/ExportData', //导出\n                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板\n            }\n        };\n    },\n    mounted() {\n        this.getTableData();\n        this.$nextTick(() => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        });\n        window.onresize = () => {\n            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);\n        };\n    },\n    methods: {\n        handleDirClick(node) {\n            this.searchForm.targetId = node.id;\n            this.searchForm.targetType = 1; // 1表示目录\n            this.getTableData();\n        },\n\n        // 权限级别更新方法\n        updatePermLevel(row) {\n            let permLevel = 0;\n            if (row.Preview) permLevel |= 1;    // 预览 (1)\n            if (row.Download) permLevel |= 2;   // 下载 (2)\n            if (row.Search) permLevel |= 3;     // 检索 (3)\n            if (row.Upload) permLevel |= 4;     // 上传 (4)\n            if (row.Delete) permLevel |= 8;     // 删除 (8)\n\n            row.PermLevel = permLevel.toString();\n            // 这里可以调用API保存权限变更\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n        },\n\n        // 全选权限\n        selectAll(row) {\n            row.Preview = true;\n            row.Download = true;\n            row.Search = true;\n            row.Upload = true;\n            row.Delete = true;\n            this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        cancelAll(row) {\n            row.Preview = false;\n            row.Download = false;\n            row.Search = false;\n            row.Upload = false;\n            row.Delete = false;\n            this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            const level = parseInt(permLevel) || 0;\n            row.Preview = (level & 1) > 0;      // 预览 (1)\n            row.Download = (level & 2) > 0;     // 下载 (2)\n            row.Search = (level & 3) > 0;       // 检索 (3)\n            row.Upload = (level & 4) > 0;       // 上传 (4)\n            row.Delete = (level & 8) > 0;       // 删除 (8)\n        },\n        handleCurrentChange(page) {\n            this.searchForm.pageIndex = page;\n            this.getTableData();\n        },\n        handleSizeChange(size) {\n            this.searchForm.pageSize = size;\n            this.getTableData();\n        },\n        getSearchBtn() {\n            this.searchForm.pageIndex = 1;\n            this.getTableData();\n        },\n\n\n\n        getTableData() {\n            getSopPermissionList(this.searchForm).then(res => {\n                this.tableData = res.response.data || [];\n                this.total = res.response.dataCount || 0;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            }).catch(err => {\n                console.error('获取权限数据失败:', err);\n                // 添加一些测试数据用于演示\n                this.tableData = [\n                    {\n                        ID: '1',\n                        RoleName: '管理员',\n                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'admin-role'\n                    },\n                    {\n                        ID: '2',\n                        RoleName: '普通用户',\n                        PermLevel: '3', // 1+2 = 3 (预览+下载)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'user-role'\n                    },\n                    {\n                        ID: '3',\n                        RoleName: '访客',\n                        PermLevel: '1', // 1 (仅预览)\n                        TargetId: 'test-target-1',\n                        GrantType: 1,\n                        GrantId: 'guest-role'\n                    }\n                ];\n                this.total = this.tableData.length;\n\n                // 解析每行的权限级别\n                this.tableData.forEach(row => {\n                    this.parsePermLevel(row.PermLevel, row);\n                });\n            });\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root {\n    display: flex;\n    height: 100%;\n\n    .root-left {\n        width: 280px;\n        padding: 10px;\n        border-right: 1px solid #ebeef5;\n        overflow: auto;\n    }\n\n    .root-right {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n    }\n\n    .root-head {\n        padding: 10px;\n        background: #fff;\n    }\n\n    .root-main {\n        flex: 1;\n        padding: 0 10px;\n        overflow: auto;\n    }\n\n    .root-footer {\n        padding: 10px;\n        background: #fff;\n    }\n}\n\n.el-form-item--small.el-form-item {\n    margin-bottom: 0px;\n}\n\n.mt-8p {\n    margin-top: 8px;\n}\n\n.pd-left {\n    padding-left: 5px;\n}\n</style>\n"]}]}