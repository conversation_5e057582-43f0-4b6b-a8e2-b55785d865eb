{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopPermission\\index.vue", "mtime": 1750239033097}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4JA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/SOP/sopPermission", "sourcesContent": ["<template>\n    <div class=\"root usemystyle\">\n        <div class=\"root-layout\" v-loading=\"initLoading\">\n            <split-pane\n                :min-percent=\"15\"\n                :max-percent=\"40\"\n                :default-percent=\"20\"\n                split=\"vertical\">\n                <template slot=\"pane1\">\n                    <div class=\"root-left\">\n                        <div class=\"tree-toolbar\">\n                            <el-button-group>\n                                <el-button\n                                    size=\"small\"\n                                    icon=\"el-icon-refresh\"\n                                    @click=\"getDirTree\">刷新</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"expandAll\">展开</el-button>\n                                <el-button\n                                    size=\"small\"\n                                    @click=\"collapseAll\">收起</el-button>\n                            </el-button-group>\n                        </div>\n                        <el-tree\n                            ref=\"tree\"\n                            :data=\"treeData\"\n                            :props=\"defaultProps\"\n                            highlight-current\n                            @node-click=\"handleNodeClick\"\n                            v-loading=\"treeLoading\">\n                            <span class=\"custom-tree-node\" slot-scope=\"{ node }\">\n                                <div style=\"line-height: 22px;\">\n                                    <div class=\"tree-title\">{{ node.data.name }}</div>\n                                </div>\n                            </span>\n                        </el-tree>\n                    </div>\n                </template>\n                <template slot=\"pane2\">\n                    <div class=\"root-right\">\n                        <div class=\"InventorySearchBox\">\n                            <div class=\"search-form\">\n                                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                                    <div class=\"form-content\">\n                                        <div class=\"search-area\">\n                                            <div class=\"search-row\">\n                                                <el-form-item label=\"目录名称\" prop=\"dirName\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirName\" placeholder=\"输入目录名称\" clearable size=\"small\" style=\"width: 200px;\"></el-input>\n                                                </el-form-item>\n                                                <el-form-item label=\"目录编码\" prop=\"dirCode\" label-width=\"100px\">\n                                                    <el-input v-model=\"searchForm.dirCode\" placeholder=\"输入目录编码\" clearable size=\"small\" style=\"width: 150px;\"></el-input>\n                                                </el-form-item>\n                                                <div class=\"action-buttons\">\n                                                    <el-button slot=\"append\" type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn()\" size=\"small\">查询</el-button>\n                                                    <el-button size=\"small\" icon=\"el-icon-refresh\" @click=\"resetForm\">重置</el-button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </el-form>\n                            </div>\n                        </div>\n                        <div class=\"root-head\" v-if=\"selectedDir\">\n                            <div class=\"selected-dir-info\">\n                                <span>当前目录：{{ selectedDir.name }}</span>\n                            </div>\n                        </div>\n                        <div class=\"root-main\">\n                            <!-- 数据统计信息 -->\n                            <div class=\"data-summary\" v-if=\"tableData.length > 0\">\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">当前目录：</span>\n                                    <span class=\"summary-value\">{{ getCurrentDirName() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">总计角色：</span>\n                                    <span class=\"summary-value\">{{ total }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">已配置权限：</span>\n                                    <span class=\"summary-value\">{{ getConfiguredCount() }}</span>\n                                </div>\n                                <div class=\"summary-item\">\n                                    <span class=\"summary-label\">未配置权限：</span>\n                                    <span class=\"summary-value\">{{ total - getConfiguredCount() }}</span>\n                                </div>\n                            </div>\n\n                            <el-table\n                                class=\"mt-3\"\n                                :height=\"mainH\"\n                                border\n                                :data=\"tableData\"\n                                style=\"width: 100%; border-radius: 4px;\"\n                                v-loading=\"tableLoading\"\n                                :empty-text=\"'暂无数据'\"\n                                ref=\"permissionTable\">\n\n                                <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\"></el-table-column>\n\n                                <el-table-column prop=\"RoleName\" :label=\"$t('SopPermission.table.RoleName')\" min-width=\"150\" show-overflow-tooltip></el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Preview')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Preview\" @input=\"handlePermissionChange(scope.row, 'Preview', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Download')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Download\" @input=\"handlePermissionChange(scope.row, 'Download', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Search')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Search\" @input=\"handlePermissionChange(scope.row, 'Search', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Upload')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Upload\" @input=\"handlePermissionChange(scope.row, 'Upload', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Delete')\" width=\"80\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <el-checkbox :value=\"scope.row.Delete\" @input=\"handlePermissionChange(scope.row, 'Delete', $event)\"></el-checkbox>\n                                    </template>\n                                </el-table-column>\n\n                                <el-table-column :label=\"$t('SopPermission.table.Operation')\" width=\"160\" align=\"center\">\n                                    <template slot-scope=\"scope\">\n                                        <div style=\"display: flex; justify-content: center; gap: 4px;\">\n                                            <el-button size=\"mini\" type=\"primary\" @click=\"selectAll(scope.row)\">\n                                                {{ $t('SopPermission.table.SelectAll') }}\n                                            </el-button>\n                                            <el-button size=\"mini\" type=\"info\" @click=\"cancelAll(scope.row)\">\n                                                {{ $t('SopPermission.table.CancelAll') }}\n                                            </el-button>\n                                        </div>\n                                    </template>\n                                </el-table-column>\n                            </el-table>\n                        </div>\n\n                    </div>\n                </template>\n            </split-pane>\n        </div>\n    </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport SplitPane from '../components/split-pane';\nimport { getSopPermissionList, saveSopPermissionForm } from '@/api/SOP/sopPermission';\nimport { getSopDirTree } from '@/api/SOP/sopDir';\n\nexport default {\n    name: 'index.vue',\n    components: {\n        SplitPane\n    },\n    data() {\n        return {\n            // 初始化加载状态\n            initLoading: false,\n            // 树相关数据\n            treeData: [],\n            treeLoading: false,\n            selectedDir: null,\n            defaultProps: {\n                children: 'children',\n                label: 'name'\n            },\n            // 表格相关数据\n            searchForm: {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: undefined,\n                targetType: undefined,\n                grantType: undefined,\n                grantId: undefined,\n                permLevel: undefined\n            },\n            total: 0,\n            tableData: [],\n            tableLoading: false,\n            mainH: 0\n        };\n    },\n    async mounted() {\n        try {\n            this.initLoading = true;\n            await this.getDirTree();\n            // 页面加载时默认请求根目录权限数据\n            this.searchForm.targetId = this.treeData[0].id; // 设置默认查询根目录\n            await this.getTableData();\n            this.$nextTick(() => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            });\n            window.onresize = () => {\n                this.mainH = this.$webHeight(\n                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,\n                    document.getElementsByClassName('root')[0]?.clientHeight || 0\n                );\n            };\n        } catch (err) {\n            console.error('页面初始化失败:', err);\n            this.$message.error('页面初始化失败，请刷新重试');\n        } finally {\n            this.initLoading = false;\n        }\n    },\n    methods: {\n        // 获取目录树\n        async getDirTree() {\n            this.treeLoading = true;\n            try {\n                const res = await getSopDirTree();\n                if (res.success) {\n                    this.treeData = res.response || [];\n                } else {\n                    this.$message.error(res.msg || '获取目录树失败');\n                }\n            } catch (err) {\n                console.error('获取目录树失败:', err);\n                this.$message.error('获取目录树失败');\n                throw err;\n            } finally {\n                this.treeLoading = false;\n            }\n        },\n\n        // 处理树节点点击\n        handleNodeClick(data) {\n            this.selectedDir = data;\n            // 保留查询条件，更新目标目录\n            const { dirName, dirCode } = this.searchForm;\n            this.searchForm = {\n                dirName,\n                dirCode,\n                targetId: data.id,\n                targetType: 1, // 1表示目录\n                grantType: undefined,\n                grantId: undefined, // 使用目录ID或编码作为grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 展开所有节点\n        expandAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, true);\n            });\n        },\n\n        // 收起所有节点\n        collapseAll() {\n            const nodes = this.$refs.tree.store._getAllNodes();\n            nodes.forEach(node => {\n                this.expandNodes(node, false);\n            });\n        },\n\n        // 树节点展开关闭\n        expandNodes(node, type) {\n            node.expanded = type;\n            for (let i = 0; i < node.childNodes.length; i++) {\n                node.childNodes[i].expanded = type;\n                if (node.childNodes[i].childNodes.length > 0) {\n                    this.expandNodes(node.childNodes[i], type);\n                }\n            }\n        },\n\n        // 处理权限变更\n        async handlePermissionChange(row, permission, value) {\n            console.log('权限变更:', permission, value);\n            this.$set(row, permission, value);\n            await this.updatePermLevel(row);\n        },\n\n        // 权限级别更新方法\n        async updatePermLevel(row) {\n            let permissions = [];\n            if (row.Preview) permissions.push('1');    // 预览 (1)\n            if (row.Download) permissions.push('2');   // 下载 (2)\n            if (row.Search) permissions.push('3');     // 检索 (3)\n            if (row.Upload) permissions.push('4');     // 上传 (4)\n            if (row.Delete) permissions.push('8');     // 删除 (8)\n\n            row.PermLevel = permissions.join(',');\n            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);\n\n            // 调用API保存权限变更\n            await this.savePermission(row);\n        },\n\n        // 保存权限到后端\n        async savePermission(row) {\n            try {\n                const saveData = {\n                    ID: row.ID,\n                    TargetId: row.TargetId || this.searchForm.targetId,\n                    TargetType: row.TargetType || this.searchForm.targetType || 1,\n                    GrantType: row.GrantType || this.searchForm.grantType || 1,\n                    GrantId: row.GrantId || this.searchForm.grantId,\n                    RoleName: row.RoleName,\n                    PermLevel: row.PermLevel,\n                    CreateDate: row.CreateDate,\n                    CreateUserId: row.CreateUserId,\n                    ModifyDate: new Date().toISOString(),\n                    ModifyUserId: this.$store.state.user.userInfo?.userId || 'system'\n                };\n\n                console.log('保存权限数据:', saveData);\n                const res = await saveSopPermissionForm(saveData);\n\n                if (res.success) {\n                    this.$message.success(`${row.RoleName} 权限保存成功`);\n                } else {\n                    this.$message.error(res.msg || '权限保存失败');\n                }\n            } catch (err) {\n                console.error('保存权限失败:', err);\n                this.$message.error('权限保存失败，请重试');\n            }\n        },\n\n        // 全选权限\n        async selectAll(row) {\n            console.log('全选权限:', row.RoleName);\n            this.$set(row, 'Preview', true);\n            this.$set(row, 'Download', true);\n            this.$set(row, 'Search', true);\n            this.$set(row, 'Upload', true);\n            this.$set(row, 'Delete', true);\n            await this.updatePermLevel(row);\n        },\n\n        // 取消全选\n        async cancelAll(row) {\n            console.log('取消全选:', row.RoleName);\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n            await this.updatePermLevel(row);\n        },\n\n        // 解析权限级别字符串\n        parsePermLevel(permLevel, row) {\n            // 使用$set确保响应式\n            this.$set(row, 'Preview', false);\n            this.$set(row, 'Download', false);\n            this.$set(row, 'Search', false);\n            this.$set(row, 'Upload', false);\n            this.$set(row, 'Delete', false);\n\n            // 处理 null、undefined 或空字符串的情况\n            if (!permLevel || permLevel === 'null' || permLevel === '') {\n                return;\n            }\n\n            // 将权限字符串按逗号分割成数组\n            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');\n\n            // 根据权限数组设置对应的权限状态\n            permissions.forEach(perm => {\n                switch (perm) {\n                    case '1':\n                        this.$set(row, 'Preview', true);     // 预览\n                        break;\n                    case '2':\n                        this.$set(row, 'Download', true);    // 下载\n                        break;\n                    case '3':\n                        this.$set(row, 'Search', true);      // 检索\n                        break;\n                    case '4':\n                        this.$set(row, 'Upload', true);      // 上传\n                        break;\n                    case '8':\n                        this.$set(row, 'Delete', true);      // 删除\n                        break;\n                }\n            });\n        },\n        getSearchBtn() {\n            this.getTableData();\n        },\n\n        // 重置查询表单\n        resetForm() {\n            this.searchForm = {\n                dirName: undefined,\n                dirCode: undefined,\n                targetId: this.searchForm.targetId, // 保留当前选中的目录\n                targetType: this.searchForm.targetType,\n                grantType: undefined,\n                grantId: this.searchForm.grantId, // 保留当前的grantId\n                permLevel: undefined\n            };\n            this.getTableData();\n        },\n\n        // 获取已配置权限的角色数量\n        getConfiguredCount() {\n            return this.tableData.filter(row => {\n                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;\n            }).length;\n        },\n\n        // 获取当前目录名称\n        getCurrentDirName() {\n            if (this.selectedDir) {\n                return this.selectedDir.name || this.selectedDir.dirName || '未知目录';\n            }\n            return '根目录';\n        },\n\n\n\n        async getTableData() {\n            this.tableLoading = true;\n            try {\n                const res = await getSopPermissionList(this.searchForm);\n                console.log('API返回数据:', res);\n\n                // 处理后端返回的数据结构\n                let data = [];\n\n                if (res && res.success !== false) {\n                    if (res.response && Array.isArray(res.response.data)) {\n                        // 如果数据在 response.data 中\n                        data = res.response.data;\n                        this.total = res.response.dataCount || data.length;\n                    } else if (res.response && Array.isArray(res.response)) {\n                        // 如果数据直接在 response 中\n                        data = res.response;\n                        this.total = data.length;\n                    } else if (Array.isArray(res)) {\n                        // 如果数据直接是数组\n                        data = res;\n                        this.total = data.length;\n                    } else if (res.success && res.response) {\n                        // 处理其他可能的数据结构\n                        data = Array.isArray(res.response) ? res.response : [];\n                        this.total = data.length;\n                    }\n\n                    this.tableData = data;\n                    console.log('处理后的表格数据:', this.tableData);\n\n                    // 解析每行的权限级别\n                    this.tableData.forEach(row => {\n                        this.parsePermLevel(row.PermLevel, row);\n                    });\n                } else {\n                    console.log('API调用失败或返回错误:', res);\n                    this.$message.error(res?.msg || '获取权限数据失败');\n                    this.tableData = [];\n                    this.total = 0;\n                }\n            } catch (err) {\n                console.error('获取权限数据失败:', err);\n                this.$message.error('获取权限数据失败，请检查网络连接');\n                this.tableData = [];\n                this.total = 0;\n            } finally {\n                this.tableLoading = false;\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n\n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n\n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .action-buttons {\n              display: flex;\n              gap: 4px;\n              margin-left: 8px;\n\n              .el-button {\n                height: 26px;\n                padding: 0 10px;\n                font-size: 12px;\n\n                [class^=\"el-icon-\"] {\n                  margin-right: 3px;\n                  font-size: 12px;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-head {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .selected-dir-info {\n      padding: 4px;\n\n      span {\n        font-size: 14px;\n        color: #606266;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .data-summary {\n      background: #fff;\n      border-radius: 4px;\n      padding: 12px;\n      margin-bottom: 12px;\n      display: flex;\n      justify-content: space-between;\n      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n      border: 1px solid #ebeef5;\n\n      .summary-item {\n        text-align: center;\n        flex: 1;\n\n        &:first-child {\n          .summary-value {\n            color: #409eff;\n          }\n        }\n\n        .summary-label {\n          display: block;\n          font-size: 12px;\n          color: #606266;\n          margin-bottom: 4px;\n        }\n\n        .summary-value {\n          display: block;\n          font-size: 16px;\n          font-weight: bold;\n          color: #303133;\n        }\n      }\n    }\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n\n  .root-footer {\n    margin-top: 12px;\n    padding: 8px;\n    background: #fff;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  }\n}\n</style>\n"]}]}