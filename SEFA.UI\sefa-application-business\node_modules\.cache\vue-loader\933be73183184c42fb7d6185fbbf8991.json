{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue?vue&type=style&index=0&id=552967d0&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDir\\form-dialog.vue", "mtime": 1750231625148}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zb3AtZGlyLWZvcm0gewogIDpkZWVwKC5lbC1kaWFsb2cpIHsKICAgIGJvcmRlci1yYWRpdXM6IDhweDsKCiAgICAuZWwtZGlhbG9nX19oZWFkZXIgewogICAgICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1OwogICAgICBtYXJnaW46IDA7CiAgICB9CiAgICAKICAgIC5lbC1kaWFsb2dfX2JvZHkgewogICAgICBwYWRkaW5nOiAyMHB4OwogICAgfQoKICAgIC5lbC1kaWFsb2dfX2Zvb3RlciB7CiAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsKICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlYmVlZjU7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7CiAgICB9CiAgfQoKICAuZm9ybS1ib2R5IHsKICAgIHBhZGRpbmc6IDEwcHggMDsKCiAgICAuZWwtZm9ybS1pdGVtIHsKICAgICAgbWFyZ2luLWJvdHRvbTogMThweDsKICAgIH0KICB9CgogIC5wYXJlbnQtZGlyIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogICAgcGFkZGluZzogNXB4IDEycHg7CiAgICBsaW5lLWhlaWdodDogMS41OwogICAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICBjb2xvcjogIzYwNjI2NjsKICB9CgogIC51c2VyLXNlbGVjdG9yIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiAxMHB4OwogICAgCiAgICAudXNlci10ZXh0IHsKICAgICAgZmxleDogMTsKICAgICAgcGFkZGluZzogNXB4IDEycHg7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgfQogICAgCiAgICAuc2VsZWN0LWJ0biB7CiAgICAgIGZsZXgtc2hyaW5rOiAwOwogICAgICB3aWR0aDogODBweDsKICAgICAgcGFkZGluZzogN3B4IDEycHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AAkPA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/SOP/sopDir", "sourcesContent": ["<template>\n  <div class=\"sop-dir-form\">\n    <el-dialog\n      :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      :modal=\"true\"\n      :append-to-body=\"true\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"handleClose\"\n    >\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" :rules=\"rules\" label-width=\"100px\">\n        <div class=\"form-body\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"父级目录\" prop=\"parentId\">\n                <span class=\"parent-dir\" :title=\"parentDir\">{{ parentDir || '未选择父级目录' }}</span>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirName')\" prop=\"dirName\">\n                <el-input v-model=\"dialogForm.dirName\" :placeholder=\"$t('SOP.EnterDirName')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item :label=\"$t('SOP.DirCode')\" prop=\"dirCode\">\n                <el-input v-model=\"dialogForm.dirCode\" :placeholder=\"$t('SOP.EnterDirCode')\"></el-input>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"负责人\" prop=\"ownerUserid\">\n                <div class=\"user-selector\">\n                  <span class=\"user-text\" :title=\"selectedUser\">{{ selectedUser || '未选择负责人' }}</span>\n                  <el-button \n                    type=\"primary\" \n                    size=\"small\" \n                    @click=\"showUserDialog = true\"\n                    class=\"select-btn\"\n                    icon=\"el-icon-user\">\n                    选择\n                  </el-button>\n                </div>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button-group>\n          <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n          <el-button \n            type=\"primary\" \n            size=\"small\"\n            v-loading=\"formLoading\" \n            :disabled=\"formLoading\" \n            element-loading-spinner=\"el-icon-loading\"\n            @click=\"submit()\">\n            确 定\n          </el-button>\n        </el-button-group>\n      </div>\n    </el-dialog>\n    <select-user \n      :visible.sync=\"showUserDialog\" \n      @select-user=\"handleSelectUser\"\n      v-if=\"dialogVisible\"\n    />\n\n  </div>\n</template>\n  \n<script>\nimport {\n  getSopDirDetail,\n  saveSopDirForm,\n  getSopDirTree\n} from \"@/api/SOP/sopDir\";\nimport Treeselect from '@riophae/vue-treeselect'\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\nimport SelectUser from './components/selectUser.vue'\n\nexport default {\n  name: 'SopDirFormDialog',\n  components: {\n    SelectUser\n  },\n  data() {\n    return {\n      dialogForm: {\n        parentId: 0,\n        dirName: '',\n        dirCode: '',\n        ownerUserid: ''\n      },\n      rules: {\n        dirName: [\n          { required: true, message: this.$t('SOP.DirNameRequired'), trigger: 'blur' },\n          { min: 2, max: 50, message: this.$t('SOP.DirNameLength'), trigger: 'blur' }\n        ],\n        dirCode: [\n          { required: true, message: this.$t('SOP.DirCodeRequired'), trigger: 'blur' },\n          { pattern: /^[A-Za-z0-9-_]+$/, message: this.$t('SOP.DirCodeFormat'), trigger: 'blur' }\n        ],\n        ownerUserid: [\n          { required: true, message: this.$t('SOP.DirOwnerRequired'), trigger: 'change' }\n        ]\n      },\n      dialogVisible: false,\n      formLoading: false,\n      currentRow: {},\n      isRootDir: false, // 是否新增根目录\n      dataList: [], // 目录树数据\n      showUserDialog: false,\n      selectedUser: '',\n      parentDir: '',\n      normalizer: node => ({\n        id: node.ID,\n        label: node.DirName,\n        children: node.Children\n      })\n    }\n  },\n  methods: {\n    async submit() {\n      try {\n        await this.$refs.dialogForm.validate()\n        this.formLoading = true\n        const res = await saveSopDirForm(this.dialogForm)\n        if (res.success) {\n          this.$message.success(res.msg || '保存成功')\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        } else {\n          this.$message.error(res.msg || '保存失败')\n        }\n      } catch (err) {\n        console.error('保存失败:', err)\n        this.$message.error('保存失败')\n      } finally {\n        this.formLoading = false\n      }\n    },\n    async loadTreeData() {\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.dataList = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n      }\n    },\n    async show(data, type) {\n      if(type === 'add'){\n        // 判断是否为新增根目录\n        this.isRootDir = !data.id && !data.parentId && !data.parentId != '0'\n\n        this.dialogForm = {\n          parentId: this.isRootDir ? 0 : data.id, // 根目录parentId为0，否则为选中节点的id\n          dirName: '',\n          dirCode: '',\n          ownerUserid: ''\n        }\n        this.selectedUser = ''\n        this.parentDir = this.isRootDir ? '' : data.value + '-' + data.name\n        this.currentRow = data\n      }\n\n      debugger;\n      if(type === 'show'){\n        this.dialogForm = {\n          id: data.id,\n          parentId: this.isRootDir ? 0 : data.parentId, // 根目录parentId为0，否则为选中节点的id\n          dirName: data.name,\n          dirCode: data.value,\n          ownerUserid: data.remark\n        }\n        this.selectedUser = data.extendField\n      }      \n      this.dialogVisible = true      \n    },\n    async getDialogDetail(id) {\n      try {\n        const res = await getSopDirDetail(id)\n        if (res.success) {\n          this.dialogForm = res.response || {}\n          // 如果有负责人信息，设置显示名称\n          if (this.dialogForm.ownerUserid && this.dialogForm.ownerUserName) {\n            this.selectedUser = this.dialogForm.ownerUserName\n          }\n        } else {\n          this.$message.error(res.msg || '获取详情失败')\n        }\n      } catch (err) {\n        console.error('获取详情失败:', err)\n        this.$message.error('获取详情失败')\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n      // 添加表单重置逻辑\n      this.$nextTick(() => {\n        this.$refs.dialogForm.clearValidate()\n      })\n    },\n    \n    // 处理用户选择\n    handleSelectUser(user) {\n      try {\n        console.log('Selected user:', user) // 调试日志\n        \n        if (!user || !user.ID) {\n          throw new Error('无效的用户选择')\n        }\n\n        this.dialogForm.ownerUserid = user.LoginName\n        this.selectedUser = `${user.DepartmentName || ''} - ${user.LoginName || ''} - ${user.UserName || ''}`.trim()\n        \n        // 关闭对话框并验证表单\n        this.$nextTick(() => {\n          this.showUserDialog = false\n          this.$refs.dialogForm.validateField('ownerUserid')\n        })\n        \n      } catch (error) {\n        console.error('处理用户选择失败:', error)\n        this.$message.error('选择负责人失败: ' + (error.message || '未知错误'))\n        this.showUserDialog = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sop-dir-form {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n\n    .el-dialog__header {\n      padding: 15px 20px;\n      border-bottom: 1px solid #ebeef5;\n      margin: 0;\n    }\n    \n    .el-dialog__body {\n      padding: 20px;\n    }\n\n    .el-dialog__footer {\n      padding: 10px 20px;\n      border-top: 1px solid #ebeef5;\n      background-color: #f9fafb;\n    }\n  }\n\n  .form-body {\n    padding: 10px 0;\n\n    .el-form-item {\n      margin-bottom: 18px;\n    }\n  }\n\n  .parent-dir {\n    display: block;\n    padding: 5px 12px;\n    line-height: 1.5;\n    border: 1px solid #dcdfe6;\n    border-radius: 4px;\n    background-color: #f5f7fa;\n    color: #606266;\n  }\n\n  .user-selector {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    \n    .user-text {\n      flex: 1;\n      padding: 5px 12px;\n      border: 1px solid #dcdfe6;\n      border-radius: 4px;\n      background-color: #f5f7fa;\n      color: #606266;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    \n    .select-btn {\n      flex-shrink: 0;\n      width: 80px;\n      padding: 7px 12px;\n    }\n  }\n}\n</style>\n"]}]}