{"GLOBAL": {"Summary": "Summary", "Lose": "Lose", "AllLose": "All Lose", "Unbind": "Unbind", "SAPTB": "SAP同步", "upgrade": "Upgrade", "_BCBDY": "Save", "_MB": "TemplateName", "_SBMC": "EquipmentName", "_DYJ": "PrinterName", "LText1": "BOM文本", "LText2": "工单文本", "LText3": "物料文本", "LText4": "采购订单文本", "LText5": "工艺路线文本", "LText6": "销售订单文本", "_COMFIRM_HB": "Please confirm whether to merge?", "_SCBQ": "Create Label", "Spotcheck": "Spotcheck", "_BZ": "Notes", "_WMSLABLE": "<PERSON><PERSON>", "Maintenance": "Maintenance", "Number": "Number", "CF": "Repeat", "NoFile": "File Empty", "NoData": "NoData", "PDFOnly": "Only PDF files can be uploaded", "CheckImg": "CheckImg", "DetialTitle": "Extended Information", "_YC": "Remove", "_CK": "View", "_TS": "Tips", "_TB": "Synchronization", "_WC": "Completed", "_GD": "More", "_KS": "Start", "_SQ": "Put away", "_CX": "Query", "_XG": "Modify", "_KSJY": "Start Detection", "_CZ": "Reset", "_FZ": "Copy", "_PD": "Disply", "_TG": "Pass", "_BH": "Reject", "_FZXZ": "Copy New", "_Shenqing": "Application", "_shengcheng": "Create", "_SSL": "Search bar", "_XZ": "New", "_KCXZ": "New Invent", "_QJ": "Add", "_QR": "Confirm", "_BJ": "Editor", "_TD": "Instead", "_SET": "Set", "_BC": "Save", "_XQ": "Details", "_SC": "Delete", "_QC": "Clear", "_SQBJ": "Apply for spare parts", "_PLXG": "Batch Modification", "_PLDY": "Bulk printing", "_PLSC": "Bulk Delete", "_ZKSQ": "Expand/Cond", "_QD": "OK", "_GB": "Close", "_ZC": "Temporary storage", "_QX": "Cancel", "_PJ": "EVALUATION", "_PG": "Spending workers", "_QY": "Enable", "_JY": "Disable", "_SX": "Filter", "_SX2": "Refresh", "_HQ": "Get", "_BTG": "No pass", "_TYPE": "type", "_BOM": "BOM", "_PCGDCF": "Split Order", "_PCGD": "Batch Order", "_PCGDXZ": "New Split Order", "_XFDCS": "Download DCS", "_QDBGBTC": "Confirm and close the pop-up window", "_MANDATORY": "This field is required", "_SELECT": "Please select the item you need to operate", "_COMFIRM": "Please confirm whether to delete it?", "_COMFIRM_QX": "Please confirm whether it is <PERSON><PERSON>?", "_COMFIRM_GB": "Please confirm whether it is closed?", "_COMFIRM_SF": "Please confirm whether it is released?", "_COMFIRM_Reopen": "Please confirm whether to Reopen PO?", "_COMFIRM_GJPC": "Please confirm whether to re-parse the build batch?", "_COMFIRM_BDPF": "Please confirm whether to <PERSON>d PoRecip<PERSON>?", "_COMFIRM_SSCC": "Please enter the sub-batch number", "_COMFIRM_WC": "Please confirm whether it is completed?", "_COMFIRM_TL": "Please confirm whether to start feeding?", "_COMFIRM_SC": "Please confirm Create <PERSON><PERSON>?", "_COMFIRM_BF": "Please confirm whether it is scrapped?", "_COMFIRM_JC": "Please confirm whether to Delivery the library?", "_COMFIRM_TK": "Please confirm whether to return the library?", "_COMFIRM_BC": "Please confirm whether to save?", "_COMFIRM_FC": "Please confirm whether it is a recoil?", "_COMFIRM_BH": "Please confirm whether to reject it?", "_COMFIRM_DJ": "Please confirm whether to <PERSON><PERSON><PERSON><PERSON>?", "_COMFIRM_OVER": "Please confirm whether to <PERSON>?", "_COMFIRM_XFDCS": "Please confirm whether to download to DCS?", "_UNBIND": "Please confirm whether to unbind?", "_QRYCXPB": "Are you sure you want to reschedule?", "_G": "total", "_TSJ": "Data", "_TZD": "Jump to", "_Y": "Page", "_BCCG": "Save successfully", "_SCCG": "Successful deletion", "_BHCG": "Successful rejection", "_GBCG": "Closed successfully", "_SCTIPS": "The data will not be recovered once it is deleted. Are you sure you want to delete this data?", "_GDJSTS": "After the work order is over, it will not be possible to reopen the work order. Will it be confirmed that it is over?", "_KEY": "keyword", "_RELEASE": "Release", "_APPROVAL": "Approval", "mainProcess": "Main Process", "Subprocesses": "subprocesses", "resourceInformation": "resource information", "_INDEX": "serial number", "_ACTIONS": "Operation", "StartTime": "Start Time", "EndTime": "End Time", "_DATE": "DATE", "_SAPIMPORT": "SAP Import", "_EXPORT": "Export", "_FILEINPORT": "Upload File", "_IMGINPORT": "Upload/Check Img", "_CheckFile": "Check File", "_EXPORTQR": "Export QR code", "_DR": "Import", "_DC": "Export", "_DCWLXQ": "ExportMaterialRequirement", "_MBXZ": "Template Download", "Download": "Download", "PrintBarcode": "Print Barcode", "CreateDate": "Create Time", "CreateUserId": "Creater", "ModifyDate": "Recent Modified Time", "ModifyUserId": "Recent Modifier", "noData": "No data available", "UnfoldFold": "Unfold/Fold", "_SM": "Scan the code", "_XM_BH": "Employee Name/Employee Number", "_CXGJZ": "Query keywords", "_YDL": "Domain Login", "_GROUP": "Group", "_REASON": "Reason", "_FS": "Send", "_LINE": "Product Production Line"}, "TITLE": {"_WL": "material", "_DRPF": "import", "_Device": "<PERSON><PERSON>"}, "SCZX": "Manufacturing Execution", "SCJX": {"GGQZ": "Group", "kpi": "kpi", "CXCLJS": "Output Budget-Based on size", "LBCLYS": "Output Budget-Based on Category", "YLHSNMB": "Annual target for raw material loss", "BCSHNMB": "Annual target for packaging material loss", "BCSHNMBSCX": "Annual target for packaging material loss_Based on Line", "CXTJLMBZ": "Production line shutdown rate target value"}, "SZGC": "Digital Factory", "SIM": {"Mang": "SIM Management", "SIM1": "SIM1", "_SJCG": "Update Successfully"}, "DFM_XTGL": "System Administration", "DFM_JHGL": "Plan Management", "DFM_KPIPZ": "KPI Setup", "DFM_ZLGL": "Quality Management", "KANBAN_GZTJ": {"_GZTJ": "Fault statistics"}, "DFM_DDGL": {"_XJ": "Add", "_BJ": "Edit", "_WL": "Material", "_ZWSJ": "No Data", "_DDGL": "Order Management", "_ADJL": "Andon record", "ActualStartTime": "Start time", "ActualEndTime": "End time", "Status": "Status", "ProductionCode": "Material Code", "_DDH": "Order number", "Line": "Production line", "_LX": "Type", "_KZZ": "Master", "_DDY": "Di<PERSON>atcher", "_ZDSCGD": "Automatically generate work orders", "_SDSCGD": "Manually generate work orders", "_QXGD": "Cancel work order", "SapWoCode": "SAP production order number", "ProductionName": "Material name", "Stage": "Phases", "LockStatus": "Locked", "PlanDate": "Planned date", "Type": "Order type", "PlanQty": "Planned quantity", "RoutingId": "Rework route", "_FSTZ": "Send notification", "_GBBJ": "Turn off alarm"}, "DFM_GDGL": {"_Tips": "The planned number cannot be less than {num}", "_GJZ": "Keywords", "_BC": "Shift", "_JHSLXG": "Planned quantity modification", "_SH": "Audit", "_HB": "<PERSON><PERSON>", "_CF": "Split", "_JHSL": "Planned quantity", "_CFSL": "Split quantity", "_QXZHBD": "Please select Me<PERSON> to", "SapWoCode": "SAP production order number", "WoCode": "Work order", "ProductionCode": "Material Code", "ProductionLineCode": "Production line", "LineName": "Section", "ProductionName": "Material name", "PlanDate": "Planned date", "Shift": "Shift", "Status": "Status", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "PlanQty": "Planned quantity", "ActualStartTime": "Actual start time", "ActualEndTime": "Actual end time", "QuantityQty": "Quantity of good products", "UnquantityQty": "Quantity of defective products"}, "DFM_SCGDGTT": {"_SCGDGTT": "Production work order Gantt chart", "_RQ": "Date", "_DDH": "Order number", "_BC": "Shift", "_SAPDDH": "SAP order", "_GDH": "Work order", "ProductionCode": "Material Code", "ProductionLineCode": "Production line", "PlanDate": "Schedule time"}, "DFM_SCJDKB": {"_SCJDKB": "Production progress board", "_DDH": "Order number", "_GD": "Work order", "_GDU": "Section", "_ZWSJ": "No data"}, "DFM_SCGD": {"_SCGD": "Production work order"}, "DFM_YWKB": {"_YWKB": "Operation and maintenance of Kanban", "_GDKB": "Work order Kanban", "_GGSBKB": "Public device Kanban"}, "DFM_JYLXTY": {"_SJXMWH": "First check item maintenance", "_XJXMWH": "Maintenance of inspection items", "_CJXMWH": "Random check project maintenance", "_TJXG": "Add/Edit", "_SCTP": "Upload pictures", "_ZWTP": "No picture yet", "_PZ": "Photograph", "_SBTJ": "Equipment tuning", "_QXZYY": "Please select the cause", "_SB": "Equipment", "InspectionSheet": "Inspection number", "TestItem": "Inspection item", "ProductionCode": "Material code", "ProductionName": "Material name", "Minvalue": "Minimum value", "MeasuredValue": "Measured value", "Maxvalue": "Maximum value", "Standardvalue": "Standard value", "SizingStandard": "Qualitative value", "InspectionText": "OK/NG", "PictureFile": "Photo management"}, "DFM_GDCX": {"_GDCX": "The repair order query", "_SJXZ": "Time selection", "_ZDYSJFW": "Customize the time range", "SapWoCode": "SAP production order number", "WoCode": "Work order", "ProductionCode": "Material Code", "ProductionLineCode": "Production line", "LineName": "Section", "ProductionName": "Material name", "PlanDate": "Planned date", "Shift": "Shift", "Status": "Status", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "PlanQty": "Planned quantity", "ActualStartTime": "Actual start time", "ActualEndTime": "Actual end time", "QuantityQty": "Quantity of good products", "UnquantityQty": "Quantity of defective products", "actions1": ""}, "DFM_RGDXQ": {"_RGDXQ": "Details of daily work order"}, "DFM_NJH": {"_NJH": "Year Plan", "_QHST": "Switch views", "_KSNF": "Start year", "_JSNF": "End year", "_ZZT": " histogram", "_SLG": "Quantity/piece", "_SJ": "Time", "ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Year": "Year", "Quantity": "Quantity", "actions": "Actions"}, "DFM_YJH": {"_YJH": "Month Plan", "_QHST": "Switch views", "_KSYF": "Start month", "_JSYF": "End month", "ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Floor": "Storey", "LineCoding": "Production line", "Month": "Month", "Quantity": "Quantity", "actions": "Actions"}, "DFM_RJH": {"_RJH": "Day Plan", "_QHST": "Switch views", "_KSRQ": "Start time", "_JSRQ": "End time", "ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Floor": "Storey", "LineCoding": "Production line", "Days": "Date", "Quantity": "Quantity", "actions": "Actions"}, "DFM_JYXM": {"_JYXM": "Inspection items", "_DRMBXZ": "Import template download", "_DR": "Import", "_JYJHSDSC": "Inspection plan generation manually", "_JYJHSC": "Inspection plan generation", "_PDBZ": "Judgement standard", "TestItem": "Inspection item", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "InspectionType": "Judgment mode", "InspectionCategory": "Inspection category", "Frequency": "Sampling frequency", "SampleSize": "Sample size", "Unit": "Unit", "StandardValue": "Standard value", "Minvalue": "Minimum value", "Maxvalue": "Maximum value", "Testtool": "Detection tools", "Toolmodel": "Gauge model", "Inputmode": "Data input mode", "IsAuto": "Inspection mode", "Executor": "Executor", "Status": "Whether or not to enable", "Remark": "Remark"}, "DFM_JYJH": {"_JYJH": "Inspection plan", "_ZDSCJYJH": "Automatically generate inspection plan", "TestType": "Type of inspection plan", "TestItem": "Inspection item", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Execution time", "Executor": "Executor", "Status": "Valid or not", "_KSSJ": "Execution start time", "_JSSJ": "Execution end time"}, "DFM_SJJL": {"_SJJL": "First inspection record", "_KSSJ": "Start time", "_JSSJ": "End time", "_GBBJ": "Turn off alarm", "_CSBJ": "Timeout alarm", "InspectionSheet": "First inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status"}, "DFM_XJJL": {"_XJJL": "Patrol inspection records", "_KSSJ": "Start time", "_JSSJ": "End time", "_GBBJ": "Turn off alarm", "_CSBJ": "Timeout alarm", "InspectionSheet": "Number of patrol inspection sheet", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status"}, "DFM_GCCJ": {"_GCCJ": "Process sampling inspection", "InspectionSheet": "Sampling inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "_KSSJ": "Start time", "_JSSJ": "End time", "_GBBJ": "Turn off alarm", "_CSBJ": "Timeout alarm", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status"}, "DFM_CPGL": {"PlanQty": "Planned quantity", "PlanDate": "Planned date", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "RoutingId": "Rework route", "SapWoCode": "SAP production order number", "Stage": "Phases", "_CPGL": "Product isolation", "_GLCZ": "Isolation and disposal", "_CZJG": "Disposal result", "_GLPSJ": "Isolation collection", "InspectionSheet": "Isolation order number", "ProductionCode": "Material code", "ProductionName": "Material name", "ProductionCodeModel": "Transfer material number", "Line": "Production line", "Segment": "Isolate the section", "BatchNo": "Batch number", "Status": "Status", "D1CreaterCode": "Quarantine person", "PlanIsolationQty": "Planned quarantine quantity", "ActualIsolationQty": "Actual number of quarantines", "DealReason": "Register", "_FG": "Rework"}, "DFM_YCLLFX": {"_YCLLFX": "Primary yield analysis", "_BJLLFX": "Edit yield analysis", "ProductLine": "Production line", "ShiftName": "Shift", "StartPlanDate": "Start time", "EndPlanDate": "End time"}, "DFM_YCSJTZ": {"_YCSJTZ": "Exception event notification", "_FSTZ": "Send QMS notification", "_YCSJXQ": "Exception event details"}, "DFM_CXZLJKKB": {"_CXZLJKKB": "Production line quality monitoring board"}, "DFM_ZLGLKB": {"_ZLGLKB": "Quality Management Kanban"}, "DFM_ZLJKDWPZ": {"_ZLJKDWPZ": "Configuration of quality monitoring points", "_XZDWPZ": "Added point configuration", "_BJDWPZ": "Edit the point configuration", "_SFSCS": "Whether test", "_JZWH": "Baseline maintenance", "_PYGZWH": "Maintenance of dissent rule", "_CYZB": "Sampling group", "_JYJG": "Inspection result", "actions": "Actions", "AreaName": "Production line", "Prodouctname": "Section", "Segmentname": "Work Station", "TargetPoint": "TAG point", "GlueWater": "Glue"}, "DFM_CPCJ": {"_CJ": "disassemble", "_SMTM": "Scan the barcode", "_CPCJ": "Disassembly of defective products", "Sn": "SN", "ProductionCode": "Material code", "Line": "Production line", "WoCode": "Production work order", "BatchNo": "Production batch", "ErrorDesc": "Cause of abnormality", "WorkTime": "Disassembly time", "Operator": "Dismantling man"}, "DFM_BQPZ": {"_BQPZ": "Tag Configuration", "_CIMJB": "SIM Level", "_BQMC": "Tag Name", "_XZBQ": "Add Tag", "_BQBM": "Tag Code", "_DW": "Unit", "_JSFS": "Formula Mode", "_MS": "Description", "_SFYX": "Is valid or not"}, "DFM_CZRZ": {"_CZRZ": "Operation log", "UserName": "Operator", "ServiceName": "Service name", "_KSSJ": "Start time", "_JSSJ": "End time"}, "DFM_ZNYWPZ": {"_ZNYWPZ": "Intelligent operation and maintenance configuration", "Factory": "Factory area", "ProductSeries": "Product line", "TypeName": "Type of monitoring", "Building": "Building", "Floor": "Storey", "Linename": "Production line", "SegmentName": "Section", "StationLinename": "Work station", "Device_Name": "Equipment", "DataTag": "Data label name", "DataFrom": "Source of data", "Position": "Location", "Duty_Person": "Head of operation and maintenance", "Recipient": "<PERSON><PERSON><PERSON><PERSON>", "GroupRecipient": "Group of responders", "State": "Status", "DeviceType": "Type"}, "DFM_SBTJJL": {"_SBTJJL": "Equipment shutdown record", "_RQ": "Date", "_CX": "Production line", "_GD": "Section", "_GZ": "Work station", "_BZ": "Shift", "_TJYY": "Cause of shutdown", "_TJMX": "Add details", "_CJJL": "Acquisition record", "_TZJL": "Adjustment entry"}, "DFM_SJDR": {"_SJDR": "Data import", "_SLXZ": "Sample download", "_DRMBXZ": "Import template download", "_DR": "Import", "_LX": "Type", "_KSRQ": "Start date", "_JSRQ": "End date", "_DRSJLX": "Import data type", "_SCSJWJ": "Upload data file"}, "DFM_BZZY": {"_BZZY": "Help guide", "_CDMC": "Menu name", "_QPZ": "Go config", "_TJZL": "Add subclass"}, "DFM_BZPZ": {"_BZPZ": "Help configuration", "_MC": "Name"}, "DFM_MXGL": {"Formula": "Formula", "_CIMJB": "SIM Level", "_MXGL": "Model Management", "_WLMX": "Physical Model", "_KPIName": "KPI Name", "_PLFZ": "Bulk Copy", "_KPIMX": "KPI Model", "_JCSX": "Basic Attribute", "_KPIGS": "KPI Formula", "_GSTS": "Use the keyboard above to edit the KPI formula", "_GSDY": "The formula of unit", "_MBZ": "Target value", "ProcessCode": "Process", "KPIName": "KPI Name", "KPICode": "KPI Code", "Period": "Granularity", "Unit": "Unit", "Expression": "Formula", "EnabledMark": "Enable", "Description": "Description", "_XZ": "Select", "_MC": "Name", "_LX": "Type", "_BQMC": "Tag Name", "_BQBM": "Tag Code", "_JSFS": "Formula Mode", "_SJBQ": "Data Label"}, "DFM_MXLR": {"_CIMJB": "SIM Level", "_MXLR": "Model Entering", "_WLMX": "Physical Model", "_KPIMC": "KPI Name", "_PLLR": "Batch Entry", "_SJGL": "Data Management", "_SJLR": "Data Entry", "_MBZLR": "Enter the target value", "SummaryDate": "Summary Date", "LineName": "Plant/Base/Production line", "KPIName": "KPI Name", "Period": "Granularity", "Team": "Team or group", "UnitName": "Formula Parameters", "TagName": "Tag Name", "Value": "Parameter values", "_JZMBZ": "KPI baseline target value", "AboveTarget": "KPI upper limit target value", "BelowTarget": "KPI lower limit"}, "DFM_SJZD": {"_SJZD": "Data Dictionary", "_ZDFL": "DictionaryClass", "_XZZD": "Add Dictionary", "_XGZD": "Edit Dictionary", "_XMM": "Item Name", "_XMZ": "Item Code", "_PX": "Sort", "_JC": "Short Name", "_YC": "Color", "_YX": "<PERSON><PERSON>", "_BZ": "Remark", "_FLGL": "Sort Management", "_TJFL": "Add Management", "_XGFL": "Edit Management", "_SJ": "superior", "_MC": "Name", "_BH": "Code", "_SX": "Tree"}, "DFM_CDGL": {"_CDGL": "Menus Management", "_XZCD": "<PERSON><PERSON>", "_XGCD": "<PERSON>", "_SJCD": "superior", "_CDTB": "Icon", "_CDMC": "Menus Name", "_CDBM": "Menus Code", "_ANMC": "Button Name", "_QXZF": "permission", "_CDPX": "sort", "_LYDZ": "Route", "_CDZT": "state"}, "DFM_XWLMX": {"_XWLMX": "Physical Model (New)"}, "DFM_OPC": {"_OPC": "OPC", "_TagAddress": "Tag Address", "_Server": "Server", "_Group": "Group", "_TagName": "Tag Name", "_NewServer": "New Server", "_NewGroup": "New Group", "_NewTag": "New Tag", "_Export": "Export", "_Import": "Import", "_Refresh": "Refresh", "_QuickSearch": "Quick Search"}, "DFM_TAGDATA": {"_TAGDATA": "Tag Data"}, "DFM_DWZH": {"_DWZH": "Unit Conversion", "MaterialCode": "Material", "FormUnitName": "Source unit name", "ConvertFormQty": "Source unit quantity", "ToUnitName": "Conversion unit name", "ConvertToQty": "Conversion unit quantity", "EffectiveBeginDate": "Effective start time", "EffectiveEndDate": "Effective end time", "Remark": "Remark"}, "DFM_WLMX": {"_WLMX": "Physical Model", "_XZMX": "Add Model", "_XGMX": "Edit Model", "_SJCD": "superior", "_MC": "Name", "_BM": "Code", "_LX": "Type", "_GB": "Close", "_QY": "Open", "_MS": "Remark", "_SXKZ": "Attribute Extensions", "_SXLB": "Attribute List", "_TJPZ": "Suite Configuration", "_TJSX": "Add Attribute", "_XGSX": "Edit Attribute", "_SXBM": "Code", "_SXZ": "Value", "_SXBZ": "Remark", "_KXTJ": "Optional Suite", "_YXTJ": "Selected Suite"}, "DFM_WLGXYS": {"_WLGXYS": "Material relationship mapping"}, "DFM_CXKPIMBSD": {"_CXKPIMBSD": "Production line KPI target setting"}, "DFM_SCKPIMBSD": {"_SCKPIMBSD": "KPI target setting for trial production"}, "DFM_TDLGL": {"_TDLGL": "Substitute material management", "Production": "Production", "Material": "Material"}, "DFM_BBJCMX": {"_BBJCMX": "Report base model configuration"}, "DFM_TJPZ": {"_TJPZ": "Suite configuration", "_XZTJ": "Add Suite", "_XGTJ": "Edit Suite", "_TJMC": "Suite Name", "_TJBM": "Suite Code", "_TJLX": "Suite Code", "_SXKZ": "Attribute Extensions", "_TJSX": "Add Attribute", "_XGSX": "Edit Attribute", "_SXBM": "Code", "_SXMC": "Name", "_SXZ": "Value", "_SXBZ": "Remark"}, "DFM_GYSGL": {"_GYSGL": "Supplier Management", "_XZGYS": "Add Supplier", "_BJGYS": "Edit Supplier", "_GYSDM": "Supplier Code", "_GYSMC": "Supplier Name", "_SJH": "Tel", "_DZ": "Address", "_WWSC": "Outsourced production", "_BZ": "Remark", "_MS": "Remark", "_SXKZ": "Attribute Extensions", "_SXLB": "Attribute List", "_TJPZ": "Suite Configuration", "_TJSX": "Add Attribute", "_XGSX": "Edit Attribute", "_SXBM": "Code", "_SXZ": "Value", "_SXBZ": "Remark", "_KXTJ": "Optional Suite", "_YXTJ": "Selected Suite"}, "DFM_ZZJM": {"_ZZJM": "Organization Modeling", "_ZZXX": "Organize Information", "_XZZZ": "Add Organization", "_XGZZ": "Edit Organization", "_SJZZ": "superior", "_ZZMC": "Organization Name", "_ZZBM": "Organization Code", "_ZZLX": "Organization Type", "_ZZJC": "Organization Shortname", "_DHH": "Tel", "_BZ": "Remark", "_SXKZ": "Attribute Extensions", "_SXLB": "Attribute List", "_TJPZ": "Suite Configuration", "_TJSX": "Add Attribute", "_XGSX": "Edit Attribute", "_SXBM": "Code", "_SXZ": "Value", "_SXBZ": "Remark", "_KXTJ": "Optional Suite", "_YXTJ": "Selected Suite"}, "DFM_GCJM": "ESP-SupPlant", "DFM_WLGL": {"WLGL": "Material Management", "WLFLGL": "Material classification management", "flxx": "Classified information", "_WLFL": "Material Classification", "Categorycode": "The material group", "UnitName": "Fundamental Unit", "Version": "Version Number", "addMaterial": "Add material", "editorMaterial": "Edit the material", "Description": "Material_Description", "VendorPn": "Material Classification", "copyOne": "<PERSON><PERSON> the new", "copy": "Copy", "_TZ": "<PERSON><PERSON><PERSON>", "_GJZ": "Keywords", "Code": "Code", "Name": "Name", "traitName": "<PERSON><PERSON><PERSON>", "Descriptions": "Description", "_BJWLFL": "Edit material classification"}, "DFM_WLBOMGL": {"_WLH": "Material code", "LHMC": "Number/name of finished product", "WLBOMGL": "Material BOM Management", "BomType": "BOM Type", "Version": "Version Number", "Factory": "Factory", "selectBom": "Please select a BOM", "addBOM": "The new BOM", "editBOM": "Edit the BOM"}, "DFM_WLBOMMX": {"WLBOMGL": "BOM Details List", "addBOMDetail": "New BOM Details", "editBOMDetail": "Editing BOM Details"}, "DFM_XBHJW": {"XBHJW": "Line side shelf position", "selecteRacking": "Please select a wire rack", "equipmentRacking": "The shelf corresponds to the equipment machine", "addRacking": "Added line shelf information", "editRacking": "Edit line side shelf information", "addPosition": "Add the line side shelf position information", "editPosition": "Edit the shelf position information along the line", "addEquipmentRacking": "Add the shelf corresponding machine equipment information", "editEquipmentRacking": "Edit shelf corresponding machine equipment information"}, "DFM_XBHJWL": {"XBHJWL": "Line side shelf position material", "selectePosition": "Please select a line-side shelf position (click list or tree structure)", "bindInfo": "Line side shelf position information", "addBind": "Add line side shelf position", "editBind": "Edit line side shelf positions"}, "DFM_GWGL": "Post Management", "DFM_GXGD": "Process section", "DFM_LCGL": {"_LCGL": "Floor management", "_LCXX": "Floor information", "_RYGL": "Personnel association"}, "DFM_YYSGL": {"_YYSGL": "Reasontree Management", "_XZYYS": "Add <PERSON>tree", "_XGYYS": "Edit Reasontree", "_YYSDM": "Reasontree Code", "_YYSMC": "Reasontree Name", "_YYSLX": "Reasontree Type"}, "DFM_YYXXGL": {"_YYXXGL": "Reason Management", "_XZYYXX": "Add Reason", "_XGYYXX": "Edit Reason", "_YYXXDM": "Reason Code", "_YYXXMC": "Reason Name", "_YYXXLX": "Reason Type", "_YYXXMS": "Remark"}, "DFM_YYMXGL": {"_YYDMANDMC": "Cause detail code/name", "_YYMXGL": "Reasondetail Management", "_YYSXX": "Reasontree list", "_XZYYMX": "Add Reason list", "_XGYYMX": "Edit Reason list", "_YYS": "ReasonTree", "_YYSPX": "Level", "_YYXX": "Reason Type", "_YYSJ": "superior", "_YYBZ": "Remark"}, "DFM_GYLX": {"_NEW": "Process route", "_LIST": "List of process routes", "_CHART": "The flow chart", "_GYLX": "Process Route", "_LXDYCP": "Products corresponding to routes", "_GYLXMX": "Process route details", "_TJGYLXXX": "Add process route information", "_XGGYLXXX": "Modify process route information", "sectionProcess": "Section process", "_GC": "Factory", "_LX": "Process type", "_LXDM": "Routing code", "_LXMC": "Routing name", "_LXBB": "Routing version", "EffectStart": "Effective start", "EffectEnd": "Effective until", "Status": "Status", "Description": "Description", "Notes": "Notes", "_TJGYLXCP": "Add the products corresponding to the process route", "MaterialCode": "Finished product material mumber", "MaterialName": "Product name", "MaterialVersion": "Finished product part number version", "Remark": "Notes", "_TJGYLXMX": "Add process route details", "ProcCode": "Process code", "ProcName": "Process name", "ProcessVersion": "Process version", "ProcType": "Process type", "Unit": "Operating unit", "Timb": "Work-hour standard", "Jbcd": "Type of work", "Runm": "Run the machine", "Runl": "Run labor", "Setl": "Set up labor", "Movd": "Moving hours", "Qued": "Queuing hours", "Version": "Version"}, "DFM_GXWLZY": {"_GXWLZY": "Physical Resource"}, "DFM_RL": {"_RL": "Calendar", "_DBBZM": "Package group code", "_GSXX": "Company information", "_RQ": "Date", "_SZRL": "Set the calendar", "_QX": "Select All", "_SCRL": "Delete", "_GD": "More", "_BCGL": "Classes management", "_BZGL": "Team management", "_YZMS": "Operating mode", "_DQY": "The current month", "_BCSJ": "Classes Time", "_MC": "Name", "_BM": "Deparment", "_JC": "Short Name", "_XH": "Index", "_BZ": "Remark", "_CZ": "Operating", "_BCS": "Class number", "_BZS": "Team number", "_JCXX": "Basic information", "_BCSZ": "Shifts setting", "_SLBZ": "First shift team", "_XLBZ": "Next shift team", "_TJBC": "Add shift", "_TJBZ": "Add team", "_TJYZMS": "Add operating mode", "_BC": "Classes", "_BZU": "Team", "_YF": "Month", "_XZBZ": "Choose shifts", "_KSRQ": "Start date", "_JSRQ": "Ending date", "_DBZQ": "Shift work cycle", "_SYB": "Last step", "_XYB": "Next step", "_WC": "Confirm", "_GB": "Close", "_XZBC": "Select shift", "_XZBZU": "Select team", "_ZWSJ": "No data", "_KSSJ": "Start time", "_JSSJ": "End time", "_BJRL": "Edit calendar", "_XXKSSJ": "Break start time", "_XXJSSJ": "End of break time", "_XXSJ": "Half time", "_BZMC": "Team name", "_BZJC": "Team abbreviation", "_BCMC": "Shift name", "_BCJC": "Short for shift", "_XZSYTBDGD": "Select the segment you want to synchronize", "_XZGDKBX": "Select section (optional)"}, "DFM_GZRL": {"_GZRL": "Work Calendar", "_WLMX": "Physical Model"}, "DFM_PRINT": {"title": "Printing", "country": {"table": {"Description": "Description", "CultureCode": "CultureCode"}, "monthOverride": {"Month": "Month", "Month Override": "Month Override"}}, "labelFomart": {"table": {"Description": "Description", "Country": "Country", "LotPrefix": "LotPrefix", "LotSuffix": "LotSuffix", "DayFormatPrefix": "DayFormatPrefix", "DayFormat": "DayFormat", "DayFormatSuffix": "DayFormatSuffix", "MonthFormatPrefix": "MonthFormatPrefix", "MonthFormat": "MonthFormat", "MonthFormatSuffix": "MonthFormatSuffix", "YearFormatPrefix": "YearFormatPrefix", "YearFormat": "YearFormat", "YearFormatSuffix": "YearFormatSuffix", "IsUpperCase": "IsUpperCase", "DayFormatPrefix2": "DayFormatPrefix2", "DayFormat2": "DayFormat2", "DayFormatSuffix2": "DayFormatSuffix2", "MonthFormatPrefix2": "MonthFormatPrefix2", "MonthFormat2": "MonthFormat2", "MonthFormatSuffix2": "MonthFormatSuffix2", "YearFormatPrefix2": "YearFormatPrefix2", "YearFormat2": "YearFormat2", "YearFormatSuffix2": "YearFormatSuffix2", "MonthFormatOffset": "MonthFormatOffset", "DayFormatOffset": "DayFormatOffset"}, "form": {"Description": "Description", "Country": "Country", "LotPrefix": "LotPrefix", "LotSuffix": "LotSuffix", "DayFormatPrefix": "DayFormatPrefix", "DayFormat": "DayFormat", "DayFormatSuffix": "DayFormatSuffix", "MonthFormatPrefix": "MonthFormatPrefix", "MonthFormat": "MonthFormat", "MonthFormatSuffix": "MonthFormatSuffix", "YearFormatPrefix": "YearFormatPrefix", "YearFormat": "YearFormat", "YearFormatSuffix": "YearFormatSuffix", "IsUpperCase": "IsUpperCase", "DayFormatPrefix2": "DayFormatPrefix2", "DayFormat2": "DayFormat2", "DayFormatSuffix2": "DayFormatSuffix2", "MonthFormatPrefix2": "MonthFormatPrefix2", "MonthFormat2": "MonthFormat2", "MonthFormatSuffix2": "MonthFormatSuffix2", "YearFormatPrefix2": "YearFormatPrefix2", "YearFormat2": "YearFormat2", "YearFormatSuffix2": "YearFormatSuffix2", "MonthFormatOffset": "MonthFormatOffset", "DayFormatOffset": "DayFormatOffset"}}, "labelSize": {"table": {"Size": "Size"}}, "labelTemplate": {"table": {"Code": "Code", "Description": "Description", "Data": "Data", "PrinterClassName": "PrinterClassName", "TemplateClassName": "TemplateClassName", "Variant": "<PERSON><PERSON><PERSON>", "IsEditable": "IsEditable", "PreviewConfig": "PreviewConfig", "IsDefault": "<PERSON><PERSON><PERSON><PERSON>", "LabelSizeName": "LabelSizeName", "PreviewCommand": "PreviewCommand", "FileExtension": "FileExtension", "FileNamingType": "FileNamingType"}, "form": {"Code": "Code", "Description": "Description", "Data": "Data", "PrinterClass": "PrinterClass", "TemplateClassId": "TemplateClassId", "Variant": "<PERSON><PERSON><PERSON>", "IsEditable": "IsEditable", "PreviewConfig": "PreviewConfig", "IsDefault": "<PERSON><PERSON><PERSON><PERSON>", "LabelSize": "LabelSize", "PreviewCommand": "PreviewCommand", "FileExtension": "FileExtension", "FileNamingType": "FileNamingType"}}, "LabelPrinter": {"table": {"Code": "Code", "EquipmentName": "Equipment Name", "PrinterClassName": "Printer Class Name", "Description": "Description", "Status": "Status", "AllowManualPrint": "AllowManualPrint", "Type": "Type", "SequenceType": "SequenceType", "LabelSizeName": "LabelSizeName"}, "form": {"Code": "Code", "Equipment": "Equipment", "PrinterClass": "PrinterClass", "Description": "Description", "Status": "Status", "DefaultLabelCount": "DefaultLabelCount", "AllowManualPrint": "AllowManualPrint", "Type": "Type", "SequenceType": "SequenceType", "LabelSize": "LabelSize"}}, "LabelPrinterMapping": {"table": {"EquipmentName": "EquipmentName", "TemplateName": "TemplateName", "PrinterName": "PrinterName", "IsDefault": "<PERSON><PERSON><PERSON>"}, "form": {"Equipment": "Equipment", "Printer": "Printer", "IsDefault": "<PERSON><PERSON><PERSON><PERSON>", "DefaultLabelCount": "DefaultLabelCount", "Template": "Template"}}, "LabelPrintHistory": {"table": {"LotName": "LotName", "SublotName": "SublotName", "Data": "Data", "PrinterName": "PrinterName", "TemplateName": "TemplateName", "SourceEquipmentName": "SourceEquipmentName", "BatchPalletName": "BatchPalletName", "LogType": "LogType", "DownloadStatus": "DownloadStatus", "ProductionOrderName": "ProductionOrderName", "Board": "Board"}}}, "DFM_YHGL": {"HRUSER": "HR User", "_YHGL": "User Management", "_YHXM": "User Name", "_GSBM": "Departments of company", "_TJYH": "Add users", "_XZZZ": "Select group", "LoginName": "Login Name", "UserNo": "Employee Number", "Email": "Email", "UserName": "User Name", "Age": "Age", "Sex": "Sex", "Birth": "Birthday", "Tel": "Phone", "Remark": "Remark", "_MM": "Password", "_ZT": "Status", "_NAN": "Man", "_NV": "Woman", "_QY": "Enable", "_JY": "Disabled"}, "DFM_JSGL": {"_JSGL": "Role Management", "_QXFP": "Permission to operate", "_GJZ": "Keywords", "_JSANDYH": "Role name/User name", "_BJJS": "Edit Role", "_QY": "Enable", "_JY": "Disabled", "Name": "Role Name", "Enable": "Status", "Remark": "Description", "_DQJS": "The current role", "_YHLB": "Users List", "_FPYH": "Allot user", "_FPQX": "Allot permission", "_XZQX": "Select permissions", "_QYCG": "To enable successful", "_JYCG": "Disable the success", "_TJJSBDYH": "The user bound to the role is added", "_XZTJYH": "Please select the user you want to add", "_FPSBZ": "Assign device groups"}, "DFM_MBGL": {"_MBGL": "Template management", "_XZMB": "New template", "_MBSJ": "Template design", "_QY": "enable", "_JY": "disable"}, "DFM_DATAAPI": {"_DATAAPI": "Data API"}, "DFM_TMGL": {"_TMGL": "Bar code management", "ckm": "Warehouse code", "bzm": "Group code"}, "DFM_SYFZ": {"_SYFZ": "Use aid"}, "DFM_SBLX": {"_SBLX": "Device Type", "_BJSBLX": "Edit Device type", "_PLACEHOLDER": "Please select the superior menu", "_NODATA": "No data", "CATEGORY_NAME": "Device type", "DESCRIBE": "Description", "REMARK": "Notes"}, "DFM_GYGL": {"_GYJCXX": "Basic process Information", "addProcess": "Add basic process information", "editProcess": "Edit basic process information"}, "DFM_MJJL": {"_MJJL": "Final inspection record", "_KSSJ": "Start time", "_JSSJ": "End time", "_MJJLSC": "Generate final inspection", "InspectionSheet": "Final inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status", "IsDuty": "Arrive at the scene", "IsClose": "Andon status"}, "DFM_GXZY": {"_GXZY": "Process Resources", "_CKSJSYGYLX": "View all processes involved", "_SZCPHGX": "Set up the product number process", "_DJWH": "Tool maintenance", "_JJWH": "Fixture maintenance", "_FLWH": "Auxiliary maintenance", "_SBWH": "Device maintenance", "_CXWH": "Program maintenance", "_TJGZJJXX": "Add fixture information", "FixturesType": "Type", "MaterialCode": "Material code", "Quantity": "Required quantity", "DeviceTypeName": "Device type", "Remark": "Remark", "_TJFLXX": "Adding Auxiliary Materials", "Unit": "Unit"}, "DFM_DWGL": {"_BJDW": "Edit Unit", "_DWGL": "Unit Management", "Name": "UnitName", "Index": "Index", "Shortname": "Unit ShortName", "TYPENAME": "Type", "Enable": "Enable", "Description": "Description"}, "DFM_WLGYJM": {"BomRatio": "Semi-finished/finished products ratio", "_GLSB": "Associated apparatus", "_WLGYJM": "Material process modeling", "add": "Added process resource routes", "update": "Edit the process resource route", "MaterialId": "The product name", "RoutingCode": "Process route", "Remark": "Remark", "Version": "Version", "VersionProp": "The version number cannot be greater than 7 characters", "device": "equipment", "fixtures": "tooling", "material": "material", "file": "Process documentation", "param": "process parameters", "envParam": "Process environment parameter", "staffing": "staffing", "attributes": "Extended attributes", "addDevice": "The new equipment", "updateDevice": "Edit equipment", "addFixtures": "New tooling fixture", "updateFixtures": "Editing fixture and fixture", "addMaterial": "The new material", "updateMaterial": "Edit the material", "MaterialBom": "The material BOM", "materialBOMDetail": "Material BOM details list", "addFile": "Add process documents", "updateFile": "Edit process file", "addParam": "New process parameters", "updateParam": "Edit process parameters", "addEnvParam": "Add process environment parameters", "updateEnvParam": "Edit process environment parameters", "addStaffing": "Adding Personnel Configuration", "updateStaffing": "Edit Staff Configuration", "updateBasicInfo": "Editing Basic Information", "addBasicInfo": "Adding Basic Information", "basicInfo": "Basic information"}, "DFM_CXGYLX": {"_CXGYLX": "Production line process route", "add": "New production line process route", "edit": "Edit the production line process route"}, "DFM_GYWJGL": {"_GYWJGL": "Process document management", "_XZGYWJ": "Add process documents", "_BJGYWJ": "Modification of process documents"}, "DFM_HTRW": {"_HTRW": "Background Task", "_XJRW": "New Task", "_XGRW": "Edit Task", "_ZTRW": "Suspended Task", "_KQRW": "Enabling Task", "_LJZX": "Immediate Execution", "_ZYMC": "Task Name", "_FZ": "Task Group", "_JG": "Interval(Cron)", "_URL": "ApiUrl", "_HEADERKEY": "Header(key)", "_HEADERVALUE": "Header(value)", "_QQFS": "Request Method", "_MS": "Description", "_RZ": "Log", "_RZLB": "Log List", "_KS": "Start date", "_JS": "End date", "_XX": "Message"}, "DFM_GDSPZCJDPZ": {"_GDSPZCJDPZ": "Collection point configuration", "ProductlineName": "Production line", "SegmentName": "Section", "Materialname": "Material name", "Intag": "Output collection point", "Oktag": "Acceptable output point", "Islastsegment": "Whether it is an assembly shop", "PlcOktag": "PLC produces TAG points", "Enable": "Enable or not"}, "DFM_SLDPZ": {"_SLDPZ": "Loading point configuration", "ProductlineName": "Production line", "SegmentName": "Section", "ProcessName": "Work Station", "MaterialName": "Material name", "MaterialType": "Material type", "TagId": "Collection point", "Uom": "Unit", "Tagert": "Target utilization", "BomQty": "Dosage per unit", "Price": "Price", "Seq": "Sort", "ComponentClass": "Component class", "ComponentName": "Component material", "Enable": "Enable or not"}, "TRACE_SCGL": "Production Management", "TRACE_KGJC": {"_KGJC": "Commencement inspection", "_KGQTJC": "Work order completeness inspection", "_RYYDG": "Personnel on duty", "_SBYDJ": "The equipment has been checked", "_GDWLCZ": "work material is sufficient", "_GZXHJSLCZ": "Adequate tooling model and quantity", "_SOPWJYXF": "SOP document has been issued", "_CLYQYXZ": "Measuring instrument calibrated", "_CT": "Production line", "_GD": "Section", "_GDH": "Work order", "_CPLH": "Product No", "_BC": "Shift", "_KSSJ": "Planned start time", "_JSSJ": "Planned end time"}, "TRACE_SlGL": {"_JSXLCZTS": "You are running a blanking operation!", "_JSXL": "blanking", "_SL": "Load material", "_PC": "<PERSON><PERSON>", "_WLH": "Material code", "_SLSL": "Loading quantity", "_SlGL": "Feeding Management", "_QSGZEWM": "Please scan equipment QR code", "_QSPCH": "Please scan batch QR code", "_QSYGH": "Please scan the employee number", "_QSMEWM": "Scan the QR code, please", "_DJSM": "Click on the sweep code", "_CX": "Production Line", "_GD": "Section", "_GZ": "Workstation", "_SBH": "Equipment Number"}, "TRACE_JSSXL": {"_JSSXL": "Glue loading and unloading management"}, "TRACE_XNRK": {"_XNRK": "Virtual entry"}, "TRACE_ZJDTBD": {"_ZJDTBD": "Single vehicle binding"}, "TRACE_ZJCPBD": {"_ZJCPBD": "Vehicle product binding"}, "TRACE_ZJLB": {"_ZJLB": "Box product code binding"}, "TRACE_SNJXPZ": {"_SNJXPZ": "SN resolution configuration"}, "TRACE_CZSCZS": {"_CZSCZS": "Vehicle production traceability"}, "TRACE_JTRYGL": {"_JTRYGL": "Machine personnel management"}, "TRACE_SFCCPYS": {"_SFCCPYS": "SFC product mapping", "FullLineName": "Production line", "SectionCode": "Section", "ProductName": "Material name", "SfcProductName": "SFC product model", "SfcDbName": "SFC DB name", "SfcTableName": "SFC Table name", "SfcSnFormat": "SFC SN format", "SfcIpAddress": "SFC IP address", "Remark": "Remark"}, "TRACE_CCZY": {"_CCZY": "Magnetizing operation", "_QSGZEWM": "Please scan equipment QR code", "_QSPCH": "Please scan batch QR code", "_DJSM": "Click on the sweep code", "_CX": "Production Line", "_GD": "Section", "_GZ": "Workstation", "_SBH": "Equipment Number"}, "TRACE_HXZY": {"_QXXNRK": "Cancel the virtual library", "_HXZY": "Oven operation", "_QSGZEWM": "Please scan equipment QR code", "_QSPCH": "Please scan batch QR code", "_DJSM": "Click on the sweep code", "_CX": "Production Line", "_GD": "Section", "_GZ": "Workstation", "_SBH": "Equipment Number"}, "TRACE_XNCS": {"_XNCS": "Performance testing", "_KSJC": "Begin testing", "_CSFD": "Test filing", "_ADC1": "File A batch", "_ADC2": "File A, second batch", "_ADC3": "A file three batches", "_BDC1": "Batch B", "_BDC2": "File B, Batch two", "_BDC3": "B file three batches", "_CDC": "C-file number", "_QRWC": "Confirm completion", "_CSLR": "Test entry", "_QSYLBEWM": "Please scan batch QR code", "_GDH": "Work order", "_PCH": "Batch No", "_PCSL": "Number of batches", "_CPX": "Production line", "_CPLH": "Product No", "_BC": "Shift", "_KSSJ": "Start time", "_JSSJ": "End time"}, "TRACE_CYCS": {"_CYCS": "Pure tone test", "_KSJC": "Start checking", "_JCLR": "Test entry", "_QSYLBEWM": "Please scan batch QR code", "_CPX": "Production line", "_GDH": "Work order", "_PCH": "Batch No", "_CPLH": "Product No", "_PCSL": "Number of batches", "_CPLR": "Defective product entry", "_PCSYSL": "Batch residual quantity", "_BLSL": "Inspection Qty", "_JCWC": "Check complete", "_KSSJ": "Start time", "_JSSJ": "End time"}, "TRACE_BZRK": {"_XZRKD": "Download the receipt", "_BZRK": "Packing and storage", "_CPWL": "Finished material", "_RKRQ": "Warehousing date", "_SCRKD": "Generate an incoming receipt"}, "TRACE_WGJC": {"CHECK_COMFIRM": "Is the check complete operation performed?", "_WGJC": "Visual inspection", "_KSJC": "Start checking", "_JCLR": "Test entry", "_QSYLBEWM": "Please scan batch QR code", "_CPX": "Production line", "_GDH": "Work order", "_PCH": "Batch No", "_CPLH": "Product No", "_PCSL": "Number of batches", "_CPLR": "Defective product entry", "_PCSYSL": "Batch residual quantity", "_BLSL": "Inspection Qty", "_JCWC": "Check complete", "_KSSJ": "Start time", "_JSSJ": "End time", "_DYSJLCK": "Print the process card for inspection"}, "TRACE_PCHB": {"_PCHB": "Batch consolidation", "_HBCS": "Number of mergers", "_HBZSL": "Total number of consolidations", "_QSYLBEWM": "Please scan batch QR code", "_CPX": "Production line", "_GDH": "Work order", "_PCH": "Batch No", "_CPLH": "Product No", "_KSSJ": "Start time", "_JSSJ": "End time"}, "TRACE_GDGL": {"_GDGL": "Workorder Management", "_XZCX": "Production Line", "_XZGD": "Section", "_KGJC": "Start checking", "_GDKS": "Order Began", "_CJPC": "Create Batch", "_GDZT": "Order suspend", "_GDZZ": "Order Terminate", "_GDJS": "Order End", "_GDJD": "Order Over", "_BLLR": "Bad entry", "_FGCPLR": "Rework product entry", "FullLineName": "Production line", "TeamName": "Team", "WoCode": "Work order", "MaterialCode": "Product No", "Status": "Status", "Shift": "Shift", "_KSSJ": "Start time", "_JSSJ": "End time", "_CJSJ": "Creation time", "MaterialDescription": "Semi-finished product description", "SapWoProductionCode": "Material name", "pcNumbers": "Number of batches", "PrintTime": "Print time", "Reason": "Reason", "WoSumBatchQuantity": "<PERSON><PERSON> (PCS)", "WoConfirmBatchQuantity": "Confirmed Quantity (PCS)", "_SFGDKS": "Is work order started?", "_SFZTGD": "Is work order suspended?", "_SFZZGD": " Is work order terminate?", "_PCXQ": "Batch details", "_DY": "Print all", "_DYLCK": "Print process list", "_QXZGD": "Please select a work order"}, "TRACE_ABDSNCX": {"_ABDSNCX": "SN Query test records"}, "TRACE_GDSNCX": {"_GDSNCX": "Query work order SN"}, "TRACE_CXPZ": {"_CXPZ": "Production line configuration"}, "TRACE_DLZZY": {"_DLZZY": "Plasma work"}, "TRACE_BZ": {"_BZ": "packing", "_DYXH": "Print case number", "_CPX": "Production line", "_QSMEWM": "Please scan Case number QR code"}, "TRACE_CZBZ": {"_CZBZ": "Vehicle packaging"}, "TRACE_WLZS": {"_WLZS": "Material pegging", "_PC_SN": "BatchNo/SN"}, "TRACE_SCZS": {"_YCLPCM": "Raw material lot code", "_PCM": "Batch code", "_CPZS": "Product tracing", "_SCZS": "Production traceability", "_GDXX": "Work order information", "_PCXX": "Batch information", "_WLXX": "Material information", "_RYXX": "Personnel information", "_TRXX": "Input information", "_JSZS": "Glue tracing", "_CCXX": "Output information", "_JTXX": "Machine information", "_CPGZZS": "Product stopover traceability", "_WLZS": "Material traceability", "_ZLGL": "Quality information", "_GDSNCX": "Query work order SN", "_GDH": "Work order", "_PCHSN": "Batch number /SN", "_PCH": "Batch number", "FullLineName": "Product line", "BatchNo": "Batch number", "SapWoCode": "Order number", "WoCode": "Work order", "Sn": "SN"}, "TRACE_GXYS": {"_GXYS": "Process mapping"}, "TRACE_GDBG": {"_GDBG": "Report Work"}, "TRACE_ZPGZ": {"startTime": "Start time", "endTime": "End time", "_ZPGZ": "overassembly", "_CZCY": "Vehicle appearance", "_CZWG": "Car pure tone", "_CPGZZS": "Traceability of products over the station", "gzlb": "Stop list", "czcy": "Car pure tone list", "czwg": "Vehicle appearance list", "qsrt": "Please enter barcode", "_BLYY": "Undesirable reason", "gxbn": "The procedure cannot be empty"}, "TRACE_OQC": {"_OQC": "OQC", "pcmx": "Batch details", "smewm": "Scanning two-dimensional code", "tj": "add", "jcjg": "Detection result"}, "TRACE_SNZXCX": {"_SNZXCX": "SN packing query", "SapWoCode": "Order number", "WoCode": "Work order number", "Sn": "SN", "BoxNo": "Case number"}, "TRACE_CPDTBD": {"_CPDTBD": "Product monomer binding", "_CX": "Production Line", "Sn": "Monomer SN", "BoxSn": "Box SN"}, "TRACE_DYMBBD": {"_DYMBBD": "Print template binding", "ProductionName": "Material name", "PtlName": "Template name", "Status": "Enable or not"}, "TRACE_SCSJBD": {"_SCSJBD": "1525 Production data query", "Code1": "Vehicle code 1/SN", "Code2": "Vehicle code 2/SN", "project": "Project", "line": "Production line", "equipment": "Equipment", "section": "Section", "station": "Workstation", "startTime": "Start time", "endTime": "End time", "_SNZJM": "Vehicle code/SN", "CollectTime": "Collect time"}, "TRACE_EHYCPBD": {"_EHYCPBD": "Two in one product binding"}, "Andon_ADGL": "Andon management", "ANDON_BJLXGL": {"_BJLXGL": "Alarm type management"}, "ANDON_BJTZJL": {"_BJTZJL": "Alarm Notification Record Query"}, "ANDON_BJYYFX": {"MainAlarmType": "MainAlarmType", "SubAlarmType": "SubAlarmType", "Frequency": "Frequency", "CumulativePercentage": "CumulativePercentage（%）", "SingleItemPercentage": "SingleItemPercentage（%）", "ResponseTime": "ResponseTime（分钟）", "ProcessTime": "ProcessTime（分钟）"}, "ANDON_BJJL": {"isMain": "Querying All Alarms", "_BJJL": "Alarm record query", "_BJJLCZ": "Alarm record handling", "details": "Alarm details", "close": "Close the alarm", "maintenance": "Machine maintenance", "upgrade": "Upgrade the route", "upgradeList": "Upgrade the route list", "alarmList": "List of alarm details"}, "ANDON_DCTJBJ": {"_DCTJBJ": "Multiple Shutdown Alarm Rule"}, "ANDON_BJCFGZ": {"_BJCFGZ": "Alarm Trigger Rule Management"}, "ANDON_BJSJGZ": {"_BJSJGZ": "Alarm escalation rule management"}, "ANDON_BJZY": {"JYK": "Experience Library", "_BJZY": "Andon Alarm home page", "xzbj": "New alarm", "SubAlarmType": "Secondary classification", "ProblemLevel": "Problem level", "areaid": "Production line", "ProductLine": "section", "EquipmentCode": "equipment", "AlarmContent": "Alarm content", "jjcz": "Emergency response", "ProductLineName": "section", "SubAlarmName": "Secondary classification", "problemLevelName": "Problem level", "EquipmentName": "equipment", "Comment": "Police content", "shxx": "Loss information", "ReasonCode": "cause", "Unit": "unit", "ManQuantity": "Labor quantity", "DEVICE_Quantity": "Number of equipment", "tishi": "Please select Alarm record", "jjqr": "Call confirmation", "predicttime": "Estimated completion time (min)", "DealTypeName": "Alarm type", "PostInfo": "Notice Info", "PostType": "Post Type", "PostDept": "Post Dept", "PostRole": "Post Role", "PostUser": "Post User", "CheckPostUser": "Check PostUser!", "DesignatedHandler": "Designated Handler", "AlarmPic": "Alarm Pic", "AlarmVideo": "Alarm Video"}, "MATERIAL_WLGL": "Material management", "MATERIAL_MESCKJM": {"_MESCKJM": "MES warehouse modeling"}, "MATERIAL_CKWLGZ": {"_CKWLGZ": "Warehouse material rules", "kcdy": "The inventory has to be greater than 0", "aqxyyj": "Safety stock must be smaller than warning stock", "aqxyzg": "Safety stock must be less than maximum stock", "yjdyaq": "Warning stock must be larger than safety stock", "yjxyzg": "Warning stock must be smaller than maximum stock", "zgdyaq": "Maximum stock must be greater than safety stock", "zgdyyj": "Maximum stock must be greater than warning stock"}, "MATERIAL_AGVCK": {"_AGVCK": "AGV warehouse modeling", "kqwh": "Reservoir maintenance", "kqsxwh": "Reservoir property maintenance", "kqsx": "Reservoir property"}, "MATERIAL_CKGXJM": {"_CKGXJM": "AGV& Warehouse relationship modeling"}, "MATERIAL_CXCKJM": {"_CXCKJM": "Production line and warehouse relationship modeling"}, "MATERIAL_CXSLD": {"_CXSLD": "Production line material point management"}, "MATERIAL_LLZCD": {"_LLZCD": "Materials dump order management"}, "MATERIAL_YLKCGL": {"cxtl": "Production line returned material", "_YLKCGL": "Raw material inventory management", "wlkc": "Material inventory", "rkjl": "Warehousing record", "ckjl": "Outgoing record", "xzjl": "Correction record", "cxjy": "Production line balance inventory"}, "MATERIAL_YLFXDDY": {"_YLFXDDY": "Raw material separate line printing", "bnwk": "The warehouse code cannot be empty", "ckbm": "Warehouse coding"}, "MATERIAL_WIPKCGL": {"_WIPKCGL": "WIP inventory management", "kclb": "Inventory list", "kctj": "Inventory statistics", "wlyk": "Material transfer", "gxlb": "Check list", "yk": "Warehouse transfer", "ykxx": "Transfer information"}, "MATERIAL_JSGL": {"_JSGL": "Glue management", "jslb": "Glue list", "jszj": "gluing", "fljl": "Dispatch record", "wzjjb": "List of unfilled hose", "cjjb": "Set up hose", "wlbms": "Material code (scan code)", "wlbm": "Material number", "wlpcs": "Material number (scan code)", "wlpc": "Material lot number", "zsbm": "Trace information", "bzqx": "Shelf life (days)", "jbtmsm": "Rubber hose bar code (scan code)", "jbtm": "Rubber hose bar code", "jbzl": "Hose weight (g)", "zj": "gluing", "sdcj": "Manual creation", "lsxq": "Historical demand", "bmmc": "Material"}, "SHIFT_RYGL": "Personnel management", "SHIFT_RYSJ": "Personnel Data", "SHIFT_RYSJ_EYZSJ": {"_RYZSJ": "Personnel master data", "_MBXZ": "Template download", "_SJDR": "Data import", "_XZTX": "Select profile picture", "_FD": "Zoom in", "_SX": "Zoom out", "_YXZ": "Right rotation", "_ZXZ": "Left rotation"}, "SHIFT_RYSJ_RYJJ": {"_RYJJ": "Personnel piecework(Pending)"}, "SHIFT_RYSJ_RYCQ": {"_RYCQ": "Personnel attendance", "_CJCX": "Production line", "_SB": "Be on duty", "_XB": "off duty", "ConfirmTimes": "Working hours", "_QRGS": "Confirmed working hours", "_UWBKQGSXQ": "UWB attendance details", "_XH": "Index", "_QYMC": "Region name", "_KSSJ": "Start time", "_JSSJ": "End time", "_SC": "Duration", "_ZSC": "Total time"}, "SHIFT_RYSJ_RYPB": {"_CXPB": "Rearrange", "_RYCQ": "Personnel scheduling", "_RYLB": "Person list", "_CJCX": "Production line", "_FPBZ": "Assignment team", "_JD": "Execute a secondment", "_XB": "Return"}, "SHIFT_RYSJ_JJBZ": {"Material": "Material", "_JJBZ": "Piecework standard(Pending)", "_CCBZ": "Overproduction standard", "_GWBMXX": "Department position information", "_JJJZXX": "Piece base information", "_DJJZXX": "Unit price basis information"}, "SHIFT_RYSJ_RYCXPZ": {"_RYCXPZ": "Personnel production line configuration", "_MBXZ": "Template download", "_SJDR": "Data import", "_DC": "Data export"}, "TPM_SBGL": "Equipment management", "TPM_SBGL_SBGLZY": {"_SBGLZY": "Device Management Home Page", "_SBZT": "Device status", "_XXLX": "Information entry", "_SBWH": "Equipment maintenance", "_SBBY": "Equipment maintenance", "_DB": "To be done", "_JXZ": "In progress", "_YB": "Done", "_WH": "Maintenance", "_XQ": "Details"}, "TPM_SBGL_SBTZGL": {"_SXPZ": "Equipment Prop", "_SBBH": "DeviceCode", "_SBMC": "DeviceName", "_ZCBH": "AssetsName", "_ZT": "Status", "_SBTZGL": "Equipment account management", "_SBFL": "Equipment Category", "_SBCX": "Equipment production line", "_SBXX": "Equipment Information", "_SBSM": "Equipment description", "_SBWH": "Equipment maintenance", "_SBPJ": "Equipment accessories", "_BPBJQD": "List of spare parts", "_SBZB": "Equipment indicators", "_SBBOM": "Equipment BOM", "_SBLL": "Equipment Record", "_DYBQ": "Print the device label", "_WJ": "Equipment File"}, "KANBAN_KBGL": "Kanban management", "WLKCGL_WLKCGL": "Inventory management", "WLKCGL_WLKCGLMENU": {"Inspection": "Inspection Management", "YLBQ": "Material Label", "LISTING": "Inventory Listing", "ProductionSummary": "Production Summary", "ProductionHistory": "Production History", "ConsumptionHistory": "Consumption History", "TransferHistory": "Transfer History", "MaterialPreparation": "Material Preparation", "MaterialPreparationBulid": "Material Preparation Bulid", "BatchPallets": "<PERSON><PERSON>", "ContainerManagement": "Container Management", "PalletList": "<PERSON><PERSON>t List", "OperatorConsoleListing": "Operator Console Listing", "overview": "PO Management", "TLYJC": "Pre check Feeding", "YJCPZ": "Pre check configuration", "Feeding": "Feeding Management", "PoList": "PO List", "MaterialPreparationByBatch": "Material Preparation By Batch", "Repeatedweighing": "Repeatedweighing", "BLHHD": "Check After Material Preparation"}, "PrecheckFeeding": {"ScanContainerCode": "Scan Container Code", "BatchPalletId": "Batch Pallet Id", "BatchContainerId": "Batch Container Id", "ShowCompleted": "Show Completed", "LineName": "Line Name", "Machine": "Machine", "ProcessOrder": "Process Order"}, "Feeding": {"tips": "Please enter the material name or code", "NeedQuantity": "NeedQuantity", "InventQty": "InventQty", "ActualQuantity": "ActualQuantity", "ShQuantity": "Receivedinventory", "batch": "batch", "zzgd": "Product Order", "fzzgd": "Non-Production Order", "add": "add", "Type": "Type", "TraceCode": "TraceCode", "Area": "Area", "Status": "Status", "Quantity": "Quantity", "FeedingAdd": "Feeding Add", "Material": "Material", "MaterialType": "Material Type", "AddByMaterial": "Add By Material", "AddByDate": "Add By Date", "Delete": "Delete", "Detail": "Detail", "ChooseDate": "ChooseDate"}, "FeedingFULL": {"batch": "batch", "zzgd": "Product Order", "fzzgd": "Non-Production Order", "add": "add", "Type": "Type", "TraceCode": "TraceCode", "Area": "Area", "Status": "Status", "Quantity": "Quantity", "FeedingAdd": "Feeding Add", "Material": "Material", "MaterialType": "Material Type", "AddByMaterial": "Add By Material", "AddByDate": "Add By Date", "Delete": "Delete", "Detail": "Detail", "ChooseDate": "ChooseDate"}, "Producting": {"OperatorConsoleListing": "Operator Console Listing"}, "KANBAN_SBKB": {"_SBKB": "<PERSON><PERSON> kanban"}, "KANBAN_GCKB": {"_GCKB": "Factory board"}, "TPM_SBGL_SBBJGL": {"_DR": "Import", "_DC": "Export", "_MBXZ": "Template download", "_SBBJGL": "Equipment spare parts management", "_BJKCX": "Spare parts inquiry", "_PLDY": "Bulk print", "_DYBJBQ": "Print label", "_DYLLD": "Print Order", "_MXJL": "Detail record", "_RK": "Warehousing", "_RKLB": "Inbound list", "_SMRK": "Scan the code for storage", "_CK": "Out of the warehouse", "_CKQD": "Delivery list", "_CRJL": "Access record", "_RKJL": "Warehousing record", "_CKJL": "Outgoing record", "_ZSL": "Rows", "_RQ": "Date", "_BJCKSZ": "Warehouse setup", "_BJSL": "Spare parts quantity", "TBKC": "Sync Inventory"}, "DFM_SBGZDY": {"_SBGZDY": "Equipment fault definition"}, "DFM_MATERIAL_GROUP": {"MaterialGroupName": "Material Group Name", "Materials": "Materials", "GroupTypeText": "Group Type Text", "Remark": "Reamrk"}, "DFM_MATERIAL_DETAIL": {"MaterialCode": "Material Code", "MaterialName": "Material Name", "Unit": "Unit"}, "TPM_SBGL_SBWXGD": {"_ZPR": "Assign<PERSON><PERSON>", "_SJH": "Event number", "_ZCB": "Total cost", "_ZGS": "Total productive hours", "_SBWXGD": "Equipment repair work order", "_WWX": "Unmaintained", "_WXGDH": "Maintenance work order number", "_KSSJ": "Start time", "_JSSJ": "End time", "_WXZ": "In maintenance", "_YWX": "Repaired", "_QB": "All", "_CX": "Production line", "_GD": "Section", "_LY": "Source", "_BOM": "BOM", "_BJWLH": "Spare parts material number", "_FZR": "Owner", "_GZYY": "Fault cause", "_FZ": "Minutes", "_WXSC": "Maintenance time"}, "TPM_SBGL_SBWXJL": {"_SBWXJL": "Equipment maintenance records", "_WX": "maintenance", "_SJH": "Event number", "_WXGDH": "Maintenance work order number", "_KSSJ": "Alarm start time", "_JSSJ": "Alarm end time", "_TS": "debugging", "_WXMX": "Maintenance details", "_TSMX": "Debugging details", "_WXCB": "Maintenance cost", "_SFCCWZSK": "Is it a case?(Stored as a knowledge base)", "_TZBYGZ": "Adjust maintenance rules", "_JQBMMC": "Machine name/code", "_LRLX": "Input type", "_WXZT": "Maintenance condition", "_ZXRY": "executive", "_BJBM": "Spare parts code", "_YYFX": "Reason analysis", "_KSGS": "Minimum maintenance duration", "_JSGS": "Maximum maintenance duration", "_DQKC": "Current inventory", "_GZMC": "Rule name", "_GZZ": "Regular value", "_XGYY": "Reason for modification"}, "TPM_SBGL_SBDBSX": {"_SBDBSX": "Equipment to-do list"}, "TPM_SBGL_SBYXZT": {"_SBYXZT": "Operating status of equipment"}, "TPM_SBGL_SBBYXM": {"_SBBYXM": "Equipment maintenance items"}, "TPM_SBGL_SBBYJH": {"_SCBYRW": "Generate Maintenance Task", "_CJRW": "Create Maintenance Task", "_XMMX": "<PERSON><PERSON>", "_SBQD": "Equipment List", "_BYXMQD": "Maintenance Task List", "_BJMX": "Spare Parts Detail", "_SBBYJH": "Equipment maintenance plan", "_SCBYJH": "Generate maintenance plan", "_RWMX": "Task detail", "_PLBY": "Batch maintenance"}, "TPM_SBGL_SBBYGZ": {"_SBBYGZ": "Equipment maintenance rules", "Isenable": "Status", "_JQMCBM": "Machine name/code", "MaintainCycle": "Maintenance interval"}, "TPM_SBGL_SBDJXM": {"_SBDJXM": "Equipment spot inspection items"}, "TPM_SBGL_SBDJJH": {"_BYXMJHKSSJ": "Maintenance Task Plan Time", "_DJXMJHKSSJ": "Check Task Plan Time", "_SBDJJH": "Equipment spot inspection plan", "_SCDJJH": "Generate a point check Task", "_RWMX": "Task detail", "_PLDJ": "Batch inspection", "_PLFJ": "Batch reinspection", "_DJ": "Spot check", "_FJ": "recheck"}, "TPM_SBGL_SBDJGZ": {"_SBDJGZ": "Equipment inspection rules", "McProject": "Spot check items", "MaintainCycle": "Spot check cycle"}, "TPM_SBGL_BJFFGL": {"_CKDH": "Delivery Note Number", "_BJBM": "Spare Parts Code", "_BJMC": "Spare Parts Name", "_ZT": "Satus", "_GLDH": "Related Number", "_CKSJKS": "Shipment Time Start", "_CKSJJS": "Shipment Time End", "_FF": "Issue", "_TH": "Return", "_QX": "Cancel", "_CM": "Purchase", "_DC": "Export"}, "TPM_SBGL_BJYJCX": {"_BJBM": "Spare Parts Code", "_BJMC": "Spare Parts Name", "_GGXH": "Spec", "_YJKSSJ": "<PERSON><PERSON>", "_YJJSSJ": "<PERSON><PERSON>", "_DC": "Export"}, "TPM_SBGL_WDBX": {"GDH": "Work order", "ConfirmResult": "ConfirmResult", "ConfirmComment": "ConfirmComment", "_GDLY": "Work Order Source", "_ZT": "Status", "_GDLX": "Type", "_SBMC": "Equipment Name", "_SBBH": "Equipment ID", "_BXKSRQ": "Repair Start Date", "_BXJSRQ": "Repair End Date", "_DC": "Export"}, "TPM_SBGL_WDGD": {"_GDLY": "Work Order Source", "wxzgss": "Repair Duration（min）", "_ZT": "Status", "_GDLX": "Type", "_SBMC": "Equipment Name", "_SBBH": "Equipment ID", "_JDR": "Receiver", "_JD": "Receive", "_TH": "Return", "_WXJL": "Repair Record", "_CJFWD": "Create Service Order", "_BXKSRQ": "Repair Start Date", "_BXJSRQ": "Repair End Date", "_DC": "Export", "xqyy": "RequestReason", "fwnr": "RequestContent", "xykxc": "IsLookSite", "csr": "RequestBy", "bz": "Remark", "kscl": "StartDate", "ksjs": "FinishDate", "wxsc": "RepairDuration", "wxgcms": "RepairRecordDesc", "yyfx": "Reason", "yyfxjl": "ReasonResult", "wxxz": "RepairNature", "gzxz": "FaultNature", "gzbw": "FaultCategory", "wxsyby": "IsMaintained", "zwwxjy": "IsExperience"}, "TPM_SBGL_JLRWGL": {"MeasureNo": "MeasureNo", "AccountAssetNo": "AccountAssetNo", "yxqks": "Validity Start", "yxqjs": "Validity End", "jdff": "Calibration Method", "abcfl": "ABC Sort", "bhgqd": "Unqualified List", "zzwcrq": "Final Finish ", "zrr": "Responsible Person", "lb": "Category"}, "TPM_SBGL_JLQJGL": {"bh": "Number", "mc": "Name", "zt": "Status", "lb": "Category", "jyzt": "Insepection Status", "jdff": "Calibration Method", "abcfl": "ABC Sort", "yxqfw": "Validity Range", "jyrqks": "Inspection Date Start", "jyrqjs": "Inspection Date End", "zzwcrq": "Final Finish Date", "zrr": "Responsible Person", "yxqks": "Validity Start", "yxqjs": "Validity End", "jyrw": "Add Inspection"}, "TPM_SBGL_SBDXJH": {"_SCX": "Line", "_ND": "Year", "_YF": "M<PERSON>nt<PERSON>", "rwnr": "Task content", "zzr": "TaskBy", "dxnr": "Overhaul content", "jhksrq": "Plan Start Date", "jhjsrq": "Planned End Date", "zb": "Week", "zt": "Status", "comment": "Reamrk", "_ZT": "Status", "scdxrw": "Generate Overhaul Tasks", "cjwxgd": "Generate Repair Order", "bxsb": "Repair Equipment", "gdnx": "Type", "gdly": "Work Order Source", "gzxx": "Fault phenomenon", "bxsj": "Repair Time", "qwkssj": "Expected Start Time", "qwwcsj": "Expected Finish Time", "jjcd": "Emergency Level", "dzzg": "Duty Supervisor", "wxzg": "Repair Supervisor", "load": "Upload Picture/Video"}, "TPM_SBGL_FWDGL": {"_CG": "Purchase", "_CD": "Issue Order", "_YS": "Acceptance ", "_DC": "Export", "_LDYS": "Acceptance", "_yjqr": "Validation", "AcceptanceResult": "Acceptance Result", "UserConfirmResult": "UserConfirm Result", "_bmqr": "Department Confirmation", "_gcqr": "Factory Confirmation", "wxdh": "Repair Order Number", "fwdh": "Service Order Number", "sqr": "Applicator", "poh": "PO Number", "sbh": "Equipment ID", "sbmc": "Equipment Name", "bxlr": "Repair content", "zt": "Status", "jjd": "Emergency Level", "sqkssj": "Application Start Time", "sqjssj": "Application End Time", "wxzt": "Repair Status", "sftj": "Shutdown or Not", "action": "action"}, "TPM_SBGL_GZZSK": {"_GZZSK": "Fault knowledge base", "_GZYYS": "Fault cause tree"}, "DFM_SPCZST": {"_SPCZST": "SPC statement", "_DZT": "Monodromy graph", "_XNQXT": "Performance chart", "_CX": "Production line", "_GD": "Section", "_GZ": "Work station", "_SB": "Equipment", "_DW": "Point location", "_KSSJ": "Start time", "_JSSJ": "End time", "_JYXM": "Inspection items"}, "DFM_CZSPCZST": {"_CZSPCZST": "Vehicle SPC report"}, "en": "English", "user": "user|users", "login": "<PERSON><PERSON>", "register": "Register", "username": "Username", "password": "Password", "rule": {"requiredUsername": "Username is required ", "requiredPassword": "Password is required"}, "login_account": "Login account", "sponsor": "Sponsor", "home": "Home", "media": "Media", "dashboard": "Home Page", "$vuetify": {"badge": "Badge", "close": "Close", "dataIterator": {"noResultsText": "No matching records found", "loadingText": "Loading items..."}, "dataTable": {"Summary": {"order": "order", "MaterialCode": "MaterialCode", "MaterialName": "MaterialName", "MaterialBatchNo": "<PERSON>", "BatchNeed": "<PERSON><PERSON>", "ActualConsume": "Actual Consume", "ActualProduced": "Actual Produced", "Difference": "Difference", "SentQty": "<PERSON><PERSON>", "SendResult": "Send Result", "NotSentQty": "NotSend Qty", "FailQty": "FailSend Qty", "Unit": "Unit", "Qty": "Qty", "sscc": "Sscc", "batch": "<PERSON><PERSON>"}, "TPM_SBGL_SBFLGL": {"classname": "Class Name", "action": "action", "comment": "Reamrk", "classlevel": "Class Level", "classcode": "Class Code"}, "SHIFT_RYSJ_RYPB": {"SHIFT_RYSJ_RYCQ": ""}, "TPM_SBGL_BJFFGL": {"_ckdh": "Delivery Note Number", "_gldh": "Related Number", "_zt": "Status", "_bjbm": "Spare Parts Code", "_bjmc": "Spare Parts Name", "_ggxh": "Spec", "_bjlx": "spare parts type", "_pch": "Batch Code", "_ckck": "Outgoing Warehouse", "_ckkw": "Outgoing Warehouse Location", "_cksl": "Applications Quantity", "_cklx": "Outbound Type", "_slr": "<PERSON><PERSON><PERSON><PERSON>", "_slsj": "<PERSON>laim <PERSON>", "_glry": "Supervisor", "_cksj": "Outbound Time", "_cgsksj": "Purchase Start Time", "_cgjssj": "Purchase End Time", "action": "action"}, "TPM_SBGL_BJYJCX": {"ChoosePart": "<PERSON>ose <PERSON>", "QueryInventory": "Query Inventory", "Filtermaterials": "Filter Parts", "Remark": "Remark", "_bjbm": "Spare Parts Code", "_bjmc": "Spare Parts Name", "_bjlx": "Spare Parts Type", "_pp": "Brand", "_ggxh": "Spec", "_kc": "Inventory", "_dw": "Unit", "_kcaqsx": "Inventory Safety Upper Limit", "_kcaqxx": "Inventory Safety Lower Limit", "_cgzq": "Purchase Cycle (Days)", "_zdcgsl": "Minimum Purchase Quantity", "_yjsj": "Warning Time"}, "TPM_SBGL_WDGD": {"LineCode": "LineCode", "RecodeStatus": "Record Status", "gzxx": "Fault phenomenon", "wxdh": "Service Order Number", "sbh": "Equipment Number", "sbmc": "Equipment Name", "gzxz": "Fault Nature", "bxlr": "Repair content", "zt": "Workorder Status", "jjd": "Emergency Level", "jhkssj": "Planned Start Time", "jhwcsj": "Planned End Time", "wxzt": "RepairStatus", "sftj": "Shutdown or Not", "action": "action", "gdlx": "Type", "gdly": "Source", "gldh": "ReferOrderNo", "zdwcrq": "FinishDate", "dbzg": "DutyManager", "bxr": "ReportBy", "bxsj": "ReportDate", "zpr": "Assign<PERSON><PERSON>", "jdr": "ReceiveBy", "kscl": "StartDate", "ksjs": "FinishDate", "wxsc": "RepairDuration", "xtfl": "FaultCategory", "yyfl": "ReasonCategory", "wxgcms": "RepairProcess", "yyfx": "ReasonResult", "yyfxjl": "ReasonAnalysisConclusion", "wxxz": "RepairNature", "gzbw": "FaultLocation", "qrr": "ConfirmBy"}, "TPM_SBGL_JZTXCX": {"AccountAssetNo": "AccountAssetNo", "bmnbbh": "Internal Department Number", "jlqjmc": "Meauring Instrument Name", "gg": "Full Bag Weight", "jdff": "Calibration Method", "tjrq": "Reset Date", "yxq": "Validity", "jdbm": "Verification Department", "sybm": "User Department", "fbm": "By Department", "fzdd": "Place Location", "abcfl": "ABC Sort"}, "TPM_SBGL_JLRWGL": {"cjr": "Creator", "AccountAssetNo": "AccountAssetNo", "jyjg": "Validity Result", "jdff": "Calibration Method", "abcfl": "ABC Sort", "lb": "Category", "yxqks": "Validity Start", "yxqjs": "Validity End", "zzwcrq": "Final Finish Date", "jyzrr": "InspectResponsible Person", "zt": "Status", "actions": "action", "bh": "ID", "sbmc": "Equipment Name", "sl": "Qty", "ksrq": "StartDate", "jsrq": "FinishDate", "gg": "Full Bag Weight", "jyzt": "InspectStatus", "jyrq": "InspectDate", "yxq": "Validity", "hgzh": "Certificate Number", "file": "File", "jyzq": "Inspect Cycle (Months)", "jdbm": "Verification Department", "sybm": "User Department", "fbm": "By Department", "fzdd": "Place Location"}, "TPM_SBGL_JLQJGL": {"MeasureNo": "MeasureNo", "AccountAssetNo": "AccountAssetNo", "bh": "ID", "sbmc": "Equipment Name", "xh": "Model", "gg": "Full Bag Weight", "zt": "Status", "jyzt": "InspectStatus", "abcfl": "ABC Sort", "lb": "Category", "yxq": "Validity", "clfw": "Measure Range", "dj": "Level", "jd": "Percision", "dsjfdz": "d Actual Indexing Value", "ejdfdz": "e Testing Index Value", "kxs": "K Coefficient", "yc": "Tolerance", "zzcjmc": "Manufacturer Name", "ccrq": "Leave Factory Date", "ccbh": "Leave Factory ID", "jbh": "Old ID", "sqjdrq": "Apply Inspection Date", "jyzq": "Inspect Cycle (Months)", "jdff": "Calibration Method", "jyrq": "InspectDate", "jdbm": "Verification Department", "sybm": "User Department", "fbm": "By Department", "fzdd": "Place Location", "qjglry": "Utensil Supervisor", "yzzcm": "Account Asset Code", "wzzcm": "No Account Asset Code", "cxm": "Magnetic Code", "bc": "Reamrk", "actions": "action", "jyr": "Inspector", "jysj": "Inspect Time", "hgzh": "Certificate Number", "file": "File"}, "TPM_SBGL_SBDXJH": {"dxgdh": "Overhaul Work Order Number", "jhks": "Plan Start Date", "jhjs": "Planned End Date", "sjks": "Actual StartDate", "sjjs": "Actual End Date", "wxgdbh": "Repair Order ID", "sbh": "Equipment Number", "sbmc": "Equipment Name", "bxnr": "Repair content", "jjd": "Emergency Level", "qwks": "Expected Start Time", "qwwc": "Expected End Time", "dzzg": "Duty supervisor", "bxr": "Repairman", "bxsj": "Repair Time", "wxzg": "Repair Supervisor", "jdsj": "Accept Time", "fzr": "Responsible Person", "nd": "Year", "yf": "Months", "cx": "Line", "dxnr": "Overhaul content", "rwms": "Task Description", "rwnr": "Task content", "jhksrq": "Plan Start Date", "jhjsrq": "Planned End Date", "zb": "Week", "zt": "Status", "comment": "Reamrk", "ys": "Duration", "pj": "Evaluation", "pjr": "EvaluationBy", "pjsj": "EvaluateDate", "pjbz": "EvaluateRemark", "action": "action"}, "TPM_SBGL_FWDGL": {"Detail": "Datail", "Requestcontent": "Request Content", "PurchasePoItem": "Purchase PoItem", "PurchasePoLine": "Purchase PoLine", "ysyj": "Acceptance Comments", "ysjg": "Acceptance Result", "yssj": "Acceptance Time", "shsj": "Delivery Time", "sfjs": "Machinery/Construction is accepted", "yjqs": "User Confirm", "bmsp": "Department leader sign and approve", "prh": "PR Number", "poh": "PO Number", "gys": "Supplier", "reqh": "REQ NUmber", "spyj": "Examine Comments", "spr": "Examine Person", "spsj": "Examine Time", "spjg": "Examine Results", "sbh": "Equipment Number", "wxdh": "Repair Order Number", "fwdh": "Service Order Number", "fwdzt": "Service OrderStatus", "sqr": "Applicator", "sqsj": "Apply Time", "xqyy": "Demand reason", "fwnr": "Service Content", "xykxc": "Need to see the scene", "action": "action"}, "TPM_SBGL_WDBX": {"LineCode": "LineCode", "ConfirmResult": "ConfirmResult", "ConfirmComment": "ConfirmComment", "jdsj": "ReceiveDate", "qrsj": "ConfirmDate", "gdbh": "Work Order ID", "sbh": "Equipment Number", "sbmc": "Equipment Name", "wjmc": "FileName", "gdly": "Work Order Source", "gdlx": "Type", "bxlr": "Repair content", "zt": "Status", "sftj": "Shutdown Or Not", "jjd": "Emergency Level", "bxsb": "Repair Equipment", "gzxx": "Fault phenomenon", "bxsj": "Repair Time", "qwkssj": "Expected Start Time", "qwwcsj": "Expected End Time", "dbzg": "Duty Supervisor", "wxzg": "Repair Supervisor", "sctp/sp": "Upload picture/video", "action": "action", "gldh": "ReferOrderNo", "Remark": "Remark", "bxr": "ReportBy"}, "DFM_MBGL": {"Index": "Index", "Name": "Template name", "Value": "Template Value", "Type": "Template type", "attribute": "attribute", "TplJson": "Template content", "Status": "Status", "Remark": "Remark", "actions": "actions"}, "INV_FC": {"Batch": "<PERSON><PERSON>", "BatchPallet": "BatchPallet", "LineCode": "LineCode", "Sapformula": "Formula", "PlanTime": "PlanDate", "Sequences": "Sequence", "Date/User": "Date/User", "ProcessOrder(Batch)": "ProcessOrder(Batch)", "ShiftName": "Shift Name", "PROMaterial": "PRO Material", "BatchPallets": "BatchPallets", "MaterialPF": "M_FormulaNo", "TraceCode": "TraceCode", "Material": "Material", "Batches": "Batches", "Quantity": "Quantity", "Repeatedweight": "Repeatedweight", "result": "result", "Type": "Type", "Room": "room", "Location": "Location", "Expiration": "Expiration"}, "INV_LQJL": {"ModifyDate": "CreateDate", "Batchno": "Batch NO", "LotNo": "<PERSON><PERSON>", "detail": "detail", "BagWeight": "Bag Weight", "EquipmentName": "EquipmentName", "Material": "Material", "Type": "Type", "Index": "Index", "Quantity": "Quantity", "InventoryQuantity": "InventoryQuantity", "NeedQuantity": "NeedQuantity", "Completion": "Completion", "Way": "Way", "Status": "Status", "StartDate": "StartDate", "BagSize": "BagSize", "Comment": "Comment", "ComingDate": "ComingDate", "TrayNumber": "TrayNumber", "UserNumber": "UserNumber", "ActualQuantity": "ActualQuantity", "Inventqty": "Inventqty", "Complete": "% Complete", "Requesttype": "Requesttype", "CreateDate": "CreateDate", "PlannedTime": "PlannedTime", "ArraveTime": "ArraveTime", "Remark": "Remark"}, "INV_YCLGD": {"Sequence": "Sequence", "PlanStartDate": "PlanDate", "FormulaNo": "Formula No", "detail": "detail", "Label": "Label", "Line": "Line", "Po": "Po", "Machine": "Machine", "Batch": "<PERSON><PERSON>", "Material": "Material", "IsOver": "IsOver", "Confirmed": "Confirmed", "ConfirmedDate": "ConfirmedDate"}, "INV_YCLGDBL": {"Sequence": "Sequence", "PlanStartTime": "PlanStartTime", "FormulaNo": "Formula No", "detail": "detail", "Label": "Label", "ShiftName": "Shift Name", "Line": "Line", "Po": "Po", "Machine": "Machine", "Batch": "<PERSON><PERSON>", "Material": "Material", "IsOver": "IsOver", "Confirmed": "Confirmed", "ConfirmedDate": "ConfirmedDate"}, "DFM_NJH": {"_NJH": "Year Plan", "_QHST": "Switch views", "_KSNF": "Start year", "_JSNF": "End year", "ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Year": "Year", "Quantity": "Quantity", "actions": "Actions", "actions1": ""}, "DFM_YJH": {"ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Floor": "Storey", "LineCoding": "Production line", "Month": "Month", "Quantity": "Quantity", "actions": "Actions", "actions1": ""}, "DFM_RJH": {"ProductionCode": "Material code", "ProductionName": "Material name", "Factory": "Factory", "Floor": "Storey", "LineCoding": "Production line", "Days": "Date", "Quantity": "Quantity", "actions": "Actions", "actions1": ""}, "DFM_JYXM": {"TestItem": "Inspection item", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "InspectionType": "Judgment mode", "InspectionCategory": "Inspection category", "Frequency": "Sampling frequency", "SampleSize": "Sample size", "Unit": "Unit", "StandardValue": "Standard value", "Minvalue": "Minimum value", "Maxvalue": "Maximum value", "Testtool": "Detection tools", "Toolmodel": "Gauge model", "Inputmode": "Data input mode", "IsAuto": "Inspection mode", "Executor": "Executor", "Status": "Whether or not to enable", "Remark": "Remark"}, "DFM_JYJH": {"TestType": "Type of inspection plan", "TestItem": "Inspection item", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Execution time", "Executor": "Executor", "Status": "Valid or not"}, "DFM_SJJL": {"InspectionSheet": "First inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status", "IsDuty": "Arrive at the scene", "IsClose": "Andon status"}, "DFM_MJJL": {"InspectionSheet": "Final inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status", "IsDuty": "Arrive at the scene", "IsClose": "Andon status"}, "DFM_XJJL": {"InspectionSheet": "Number of patrol inspection sheet", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status", "IsDuty": "Arrive at the scene", "IsClose": "Andon status"}, "DFM_GCCJ": {"InspectionSheet": "Sampling inspection sheet No", "ProductionCode": "Material code", "ProductionName": "Material name", "Line": "Production line", "Segment": "Section", "Units": "Work Station", "ExecutionTime": "Time", "Executor": "Executor", "Status": "Status", "IsDuty": "Arrive at the scene", "IsClose": "Andon status"}, "DFM_CPGL": {"InspectionSheet": "Isolation order number", "ProductionCode": "Material code", "ProductionName": "Material name", "ProductionCodeModel": "Transfer material number", "Line": "Production line", "Segment": "Isolate the section", "BatchNo": "Batch number", "Status": "Status", "PlanIsolationQty": "Planned quarantine quantity", "ActualIsolationQty": "Actual number of quarantines", "DealReason": "Register", "FailureReason": "Cause of quarantine failure", "D1CreaterName": "Quarantine person", "D1CreaterCode": "Quarantined person account"}, "DFM_YCSJXQ": {"Result": "Punishment method", "DealQty": "Quantity", "UserCode": "Handler", "DealTime": "Date processed"}, "DFM_YCLLFX": {"PlanDate": "Date", "ShiftName": "Shift", "ProductLine": "Production line", "ErrorMsg": "Yield analysis", "Multiterm": "Multi term"}, "DFM_YCSJTZ": {"CreateBy": "Promoter", "EventTime": "Occurrence time", "Eventcontent": "Description of the event", "UeSource": "Source", "UeType": "Event type", "UePlace": "Place of occurrence", "UeLevel": "Event level", "ProductionCode": "Material code", "ProductionName": "Material name", "Stage": "Product phase", "Line": "Production line", "Section": "Section", "Result": "Product isolation", "Status": "Whether to send", "D1CreaterName": "Quarantine person", "D1CreaterCode": "Quarantined person account"}, "DFM_CPCJ": {"Sn": "SN", "ProductionCode": "Material code", "Line": "Production line", "WoCode": "Production work order", "BatchNo": "Production batch", "ErrorDesc": "Cause of abnormality", "WorkTime": "Disassembly time", "Operator": "Dismantling man"}, "DFM_ZLJKDWPZ": {"AreaName": "Production line", "Prodouctname": "Section", "Segmentname": "Work Station", "TargetPoint": "TAG point", "GlueWater": "Glue"}, "DFM_ZLJKDWPZ_B": {"PointName": "Point location name", "Parameter": "Point location name", "TestItem": "Inspection items", "EnableMonitoring": "Whether to open or not", "MonitoringRule": "Control rules", "GoValue": "Goal value", "actions": "Actions"}, "DFM_GDGL": {"SapWoCode": "SAP production order number", "WoCode": "Work order", "ProductionCode": "Material Code", "ProductionLineCode": "Production line", "LineName": "Section", "ProductionName": "Material name", "PlanDate": "Planned date", "Shift": "Shift", "Status": "Status", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "PlanQty": "Planned quantity", "ActualStartTime": "Actual start time", "ActualEndTime": "Actual end time", "QuantityQty": "Quantity of good products", "UnquantityQty": "Quantity of defective products", "actions": "Actions"}, "DFM_DDANDON": {"Status": "Andon state", "Type": "Exception type", "ErrorMessage": "Alarm information"}, "DFM_DDGL": {"SapWoCode": "SAP production order number", "ProductionCode": "Material code", "ProductionName": "Material name", "Stage": "Phases", "Line": "Production line", "LockStatus": "Locked", "PlanDate": "Planned date", "ActualStartTime": "Start time", "ActualEndTime": "End time", "Type": "Order type", "PlanQty": "Planned quantity", "Status": "Status"}, "DFM_GDCX": {"_GDCX": "The repair order query", "SapWoCode": "SAP production order number", "WoCode": "Work order", "ProductionCode": "Material Code", "ProductionLineCode": "Production line", "LineName": "Section", "ProductionName": "Material name", "PlanDate": "Planned date", "Shift": "Shift", "Status": "Status", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "PlanQty": "Planned quantity", "ActualStartTime": "Actual start time", "ActualEndTime": "Actual end time", "QuantityQty": "Quantity of good products", "UnquantityQty": "Quantity of defective products", "actions1": ""}, "DFM_CDGL": {"Category": "Category", "CnName": "Menus Name", "CnCode": "Menus Code", "Icon": "Icon", "Seq": "sort", "Code": "permission flag", "Route": "Route", "Enable": "Enable", "isHide": "Show", "IsKeepAlive": "<PERSON><PERSON>", "actions": "actions"}, "DFM_WLGL": {"Index": "Index", "Plant": "Plant", "Code": "Material Code", "NAME": "Material Name", "Version": "Version", "Description": "Material number/material description", "Type": "Material Classification", "Categorycode": "Material Group", "UnitName": "Fundamental Unit", "attribute": "attribute", "IsSpecial": "Plasma operation", "Deleted": "Enable or not"}, "DFM_WLFL": {"Code": "Code", "Name": "Name", "traitName": "<PERSON><PERSON><PERSON>", "Description": "Description", "actions": "actions"}, "DFM_WLBOMGL": {"Index": "Index", "MaterialCode": "Finished Product Material Number", "MaterialName": "Finished product name", "Version": "Version Number", "BomUsage": "BOM Usage Flag", "AltBom": "Alternative BOM", "Quantity": "Quantity", "Uom": "Unit", "Status": "Status", "EffectStart": "Commencement Of Validity", "EffectEnd": "Expiration Of Validity", "ModifyDate": "ModifyDate", "actions": "actions"}, "DFM_WLBOMMX": {"Index": "Index", "ParentName": "Superior Material Number", "CompoentName": "BOM Component", "ProcCode": "Processes", "Quantity": "Quantity", "ParentUom": "Parent Unit", "CompoentUom": "Component Unit", "Conversionrate": "The yield", "ParentQuantity": "Parent Quantity", "CompoentQuantity": "Component Quantity", "CompoentCode": "Component Code", "ParentCode": "Parent Code", "Level": "Level", "EffectStart": "Commencement Of Validity", "EffectEnd": "Expiration Of Validity", "AltGroup": "Alternative Material Group", "AltItem": "Alternative Material", "AltItemOrder": "Alternative Order (Priority)", "AltUsageRate": "Possible Alternative Material Usage Rates", "MaterialGroup": "Material Group", "MaterialDescription": "Material_Description", "Remark": "Remark", "actions": "actions"}, "DFM_XBHJW": {"Index": "Index", "RackingCode": "<PERSON><PERSON>", "RackingName": "<PERSON><PERSON>", "Status": "Valid Identification", "Description": "Description_of_The_Shelf_Location", "Remark": "Remark", "ModifyUserId": "ModifyUserId", "ModifyDate": "ModifyDate", "BinCode": "Bin Code", "BinName": "Bin Name", "BinDescription": "Bin Description", "BinType": "Bin Type", "DeviceCode": "Device Code", "actions": "actions"}, "DFM_XBHJWL": {"Index": "Index", "MaterialCode": "Material Code", "Status": "Valid Identification", "Remark": "Remark", "ModifyUserId": "ModifyUserId", "ModifyDate": "ModifyDate", "actions": "actions"}, "DFM_SJZD": {"Index": "Index", "ItemName": "ItemName", "ItemValue": "ItemValue", "Address": "Address", "SortCode": "SortCode", "Enable": "Enable", "Description": "Description", "actions": "actions"}, "DFM_SJZD_FXLB": {"Index": "Index", "name": "name", "value": "code", "SortCode": "SortCode", "SortTree": "SortTree", "Enable": "Enable", "Remark": "Remark", "actions": "actions"}, "DFM_GYSGL": {"Index": "Index", "SupplierCode": "Supplier Code", "SupplierName": "Supplier Name", "Tel": "Tel", "Address": "Address", "IsOutsourcing": "IsOutsourcing", "Remark": "Remark", "actions": "actions"}, "DFM_GWGL": {"Index": "Index", "Name": "work title", "Type": "work type", "WorkHours": "Shift system", "Description": "Description", "PostPrice": "Repair unit price /h", "ModifyDate": "ModifyDate", "ModifyUserId": "ModifyUserId", "CreateDate": "CreateDate", "CreateUserId": "CreateUserId", "actions": "actions"}, "DFM_GXGD": {"Index": "Index", "Processname": "Process name", "Segmentname": "Segment name", "ModifyDate": "ModifyDate", "ModifyUserId": "ModifyUserId", "CreateDate": "CreateDate", "CreateUserId": "CreateUserId", "actions": "actions"}, "DFM_LCGL": {"Index": "Index", "Code": "Floor number", "Name": "Floor name", "actions": "actions"}, "DFM_LCGL_XQ": {"code": "Code", "name": "Name", "sort": "sort", "actions": "actions"}, "DFM_WLMX": {"Index": "Index", "EquipmentCode": "Equipment Code", "EquipmentName": "Equipment Name", "Level": "LevelName", "Enabled": "Enabled", "Remark": "Remark", "attribute": "attribute", "actions": "actions", "noActions": ""}, "DFM_WLMX_SXKZ": {"Index": "Index", "PropertyCode": "Property Code", "Remark": "Remark", "PropertyName": "Remark", "PropertyValue": "Property Value", "Enable": "Enable", "actions": "actions"}, "DFM_WLGXYS": {"Type": "Material section", "Categorycode": "Material classification"}, "DFM_CXKPIMBSD": {"Linename": "Production line", "Segmentname": "Section", "Linecode": "Production line number", "LineIndex": "index", "Value": "Index value", "Description": "description"}, "DFM_SCKPIMBSD": {"Productline": "Production line", "Kpitype": "Kpi type", "Maxvalue": "Max value", "Minvalue": "Min value", "Enable": "Enable"}, "DFM_TDLGL": {"Enabled": "state", "ProductionCode": "Product part number", "ProductionName": "Product name", "GroupCode": "Alternate group number", "MaterialCode": "Material number", "MaterialName": "Material description"}, "DFM_BBJCMX": {"Projectname": "production line", "Productline": "production coding", "Segmentcode": "Block coding", "Segmentname": "Station", "Processname": "Process", "Devicecode": "Device coding", "Inmaterialcode": "Input material code", "Inmaterialname": "Input material", "Outmaterialcode": "Output material code", "Outmaterialname": "Output material", "Intag": "Put into the collection point", "Faulttag": "Fault collection point", "Oktag": "Qualified output collection point", "Ngtag": "NG product collection point", "Isfeed": "Whether to load the worker station", "Ct": "CT", "Ischokepoint": "Whether the bottleneck work station", "Issegmenttag": "Whether the section closing station", "Isproductlinetag": "Whether the whole line closing station", "OrderNum": "Station sequencing", "SegmentSeq": "Block sorting", "CtTag": "CT collection point", "Stopstatustag": "Device status collection point", "Sfcmachineid": "ID of the SFC test machine", "Sfcdbname": "Name of the SFC database", "Istestsegment": "Whether it is a test section", "Issegmentmaterial": "Section yield calculation material loading point", "Orgct": "Original CT", "Isenabled": "Whether the workshop is hidden", "Enable": "Enable or not", "ProductCode": "Product model", "ProductName": "Product model name", "ProductType": "Product type"}, "DFM_TJPZ": {"Index": "Index", "ClassName": "Suite Name", "ClassCode": "Suite Code", "ClassType": "Suite Type", "attribute": "attribute", "actions": "actions"}, "DFM_TJPZ_SX": {"Index": "Index", "PropertyCode": "Property Code", "DefaultValue": "Default Value", "Remark": "Remark", "Enable": "Enable", "actions": "actions"}, "DFM_ZZJM": {"Index": "Index", "Fullname": "Organization Name", "Encode": "Organization Code", "LEVEL": "Organization Type", "Shortname": "Organization Shortname", "Outerphone": "Tel", "Description": "Remark", "attribute": "attribute", "actions": "actions"}, "DFM_ZZJM_SXKZ": {"Index": "Index", "PropertyCode": "Property Code", "DefaultValue": "Default Value", "Remark": "Remark", "Enable": "Enable", "actions": "actions"}, "itemsPerPageText": "Rows per page:", "DFM_GYLX": {"Index": "Index", "RoutingCode": "Routing code", "RoutingName": "Routing name", "Version": "Version", "RoutingType": "Routing type", "EffectStart": "Effective start", "EffectEnd": "Effective until", "Status": "Status", "Description": "Description", "Notes": "Notes", "actions": "Operating", "MaterialCode": "Finished product material mumber", "MaterialName": "Product name", "MaterialVersion": "Finished product part number version", "Remark": "Notes", "ProcCode": "Process code", "ProcName": "Process name", "ProcessVersion": "Process version", "ProcType": "Process type", "Unit": "Operating unit", "Timb": "Work-hour standard", "Runm": "Run the machine", "Runl": "Run labor", "Setl": "Set up labor", "Movd": "Moving hours", "Qued": "Queuing hours"}, "DFM_SBLX": {"Index": "Index", "CATEGORY_NAME": "Device type", "PARENT_NAME": "Superior", "PARENT_TEST": "On the superior", "DESCRIBE": "Description", "REMARK": "Notes", "CREATEDATE": "Created time", "CREATEDATE_NAME": "Created user", "MODIFYDATE": "ModifyDate", "MODIFYUSERID": "ModifyUser", "actions": "Operating"}, "DFM_GYGL": {"ParentId": "Process group", "ParentCode": "Parent Code", "ProcName": "Process Name", "ProcCode": "Process Code", "ProcType": "Process type", "IsCreateOrder": "Whether to generate work order", "StandardTime": "Standard processing time", "StandardUom": "Standard processing unit", "PrepareTime": "Time to prepare", "TransTime": "Transportation time", "StandardPeronNum": "Standard number of employees", "UnitCapacity": "Design capacity", "Description": "Description", "CreateDate": "CreateDate", "ModifyDate": "ModifyDate", "ModifyUserId": "ModifyUser", "actions": "actions"}, "DFM_GXZY": {"Index": "Index", "RoutingName": "Process route", "MaterialCode": "Product number", "ProcName": "Process", "ProcType": "Routing type"}, "DFM_GXZY_JV": {"Index": "Index", "FixturesType": "Type", "MaterialCode": "Material code", "Quantity": "Required quantity", "DeviceTypeName": "Device type", "Remark": "Remark", "ModifyUserId": "Update user", "ModifyDate": "Update time", "actions": "Operating"}, "DFM_GXZY_FL": {"Index": "Index", "MaterialCode": "Auxiliary material number", "Quantity": "Required quantity", "Unit": "Unit", "Remark": "Remark", "ModifyUserId": "Update user", "ModifyDate": "Update time", "actions": "Operating"}, "DFM_CXGYLX": {"Factory": "Factory", "ProdctlineName": "Workshop section", "ProduceType": "Type", "YieldRate": "yield", "DelayTime": "Offset time", "RoutingName": "Routing name", "Remark": "Remark", "ModifyUserId": "Update user", "ModifyDate": "Update time", "actions": "Operating"}, "DFM_GYWJGL": {"FileType": "Type of process document", "FileNo": "Process document number", "FileVersion": "Version", "FileStatus": "Status", "ProdcutName": "Product Name (Class A product)", "FileName": "Name of process document", "Material": "material", "EffectStart": "Commencement Of Validity", "EffectEnd": "Expiration Of Validity", "FileSpec": "Document specifications", "Remark": "Remark", "ModifyUserId": "Update user", "ModifyDate": "Update time", "actions": "Operating"}, "DFM_YHGL": {"Index": "Index", "CompanyName": "Company", "DepartmentName": "Department", "PostName": "Post", "LoginName": "Login Name", "UserNo": "Employee Number", "UserName": "User Name", "Age": "Age", "Sex": "Sex", "Birth": "Birthday", "Tel": "Phone", "EMAIL": "Email", "Status": "State", "Remark": "Remark", "actions": "Operating"}, "DFM_YYSGL": {"Index": "Index", "ReasontreeCode": "ReasonTree Code", "ReasontreeName": "ReasonTree Name", "ReasontreeType": "ReasonTree Type", "actions": "actions"}, "DFM_YYXXGL": {"Index": "Index", "ReasontreeCode": "Reason Code", "ReasontreeName": "Reason Name", "ReasontreeType": "Reason Type", "Remark": "Remark", "actions": "actions"}, "DFM_YYMXGL": {"Index": "Index", "ReasontreeCode": "Reasontree Code", "ReasontreeName": "Reasontree Name", "DetailCode": "Detail Code", "DetailDescription": "Detail Description", "PartentName": "superior", "DetailLevel": "Level", "Remark": "Remark", "actions": "actions"}, "DFM_DWGL": {"Index": "Index", "Name": "UnitName", "Shortname": "Unit ShortName", "TYPENAME": "Type", "Enable": "Whether to enable", "Description": "Description", "actions": "actions"}, "DFM_WLGYJM": {"MaterialName": "Material Name", "ParentQuantity": "Parent Quantity", "ParentUnit": "Parent Unit", "StandardTime": "Standard processing time", "StandardUom": "Standard processing unit", "PrepareTime": "Time to prepare", "TransTime": "Transportation time", "StandardPersonNum": "Number of workers", "UnitCapacity": "Design capacity", "YieldRate": "The yield", "EquipName": "Equipment name", "EquipCode": "Equipment coding", "MaterialCode": "Material code", "FixturesType": "type", "DeviceType": "Device type", "Quantity": "Quantity", "CreateDate": "Creation time", "ProcCode": "Process Code", "Inout": "Input and output", "Unit": "unit", "Factory": "Factory", "FileNo": "Process file number", "FileVersion": "Process file version", "FileName": "Process file name", "Docid": "Current page file number", "DocVersion": "Current page specific version", "DocCode": "Current page number", "Name": "Name", "Code": "Code", "Standard": "Standard values", "UpperLimit": "ceiling", "LowerLimit": "Lower limit", "UpperUpperLimit": "Upper limit", "LowerLowerLimit": "Lower limit", "Uom": "U<PERSON>", "SORT": "The sorting", "Enabled": "Enabled", "DutyCode": "Code responsibilities", "DutyName": "Name of duty", "DutyType": "Staff skills", "TimeBase": "Base working hours", "RunNum": "Run number", "SetNum": "Set the number of", "Description": "Description", "ModifyUserId": "Update user", "ModifyDate": "Update time", "Remark": "Remark", "EquipmentCode": "Code", "EquipmentName": "Name", "Level": "Type", "Productlinename": "Production line", "Segmentname": "Section", "Processname": "Work station", "actions": "actions"}, "DFM_JSGL": {"Name": "Role Name", "Enable": "Status", "Remark": "Description", "actions": "actions"}, "DFM_DATAAPI": {"ModelName": "Model Name", "ModelCode": "Model Code", "HandleSql": "Sql", "Description": "remarks"}, "DFM_TMGL": {"Wmname": "Warehouse coding", "Line": "Production line", "Shift": "team"}, "DFM_SYFZ": {"ItemName": "Project name", "ItemValue": "Item value", "Description": "remarks"}, "DFM_CZRZ": {"UserName": "Operator", "ServiceName": "Service name", "UserCode": "Operator ID", "MethodName": "Method name", "BrowserInfo": "Browser information", "ClientName": "Client information", "ClientIpAddress": "Client IP", "ReturnValue": "Operating result", "MethodRemark": "Operation instruction"}, "DFM_BQPZ": {"TagName": "TagName", "TagCode": "TagCode", "Unit": "Unit", "Automatic": "Formula Mode", "EnabledMark": "Effective", "Description": "Description", "actions": "actions"}, "DFM_ZNYWPZ": {"Factory": "Factory area", "ProductSeries": "Product line", "TypeName": "Type of monitoring", "Building": "Building", "Floor": "Storey", "Linename": "Production line", "SegmentName": "Section", "StationLinename": "Work station", "Device_Name": "Equipment", "DataTag": "Data label name", "DataFrom": "Source of data", "Position": "Location", "Duty_Person": "Head of operation and maintenance", "Recipient": "<PERSON><PERSON><PERSON><PERSON>", "GroupRecipient": "Group of responders", "State": "Status", "DeviceType": "Type"}, "DFM_SJDR": {"FileName": "File name", "CreateUserId": "Introducer", "Remark": "Type", "CreateDate": "Import time"}, "DFM_MXGL": {"SIMName": "SIM Level", "LineName": "Plant/Base/Production line", "ProcessCode": "Process", "KPIName": "KPI Name", "KPICode": "KPI Code", "Period": "Granularity", "Unit": "Unit", "Expression": "Formula", "EnabledMark": "Enable", "Description": "Description", "actions": "actions"}, "DFM_MXLR_FIRST": {"SIMName": "SIM Level", "LineName": "Plant/Base/Production line", "ProcessCode": "Process", "KPIName": "KPI Name", "KPICode": "KPI Code", "PeriodName": "Granularity", "Unit": "Unit", "Expression": "Formula", "Description": "Description", "actions1": ""}, "DFM_MXLR_SECOND": {"UnitName": "Formula Parameters", "TagName": "Tag Name", "UnitCode": "Tag Code", "Automatic": "Acquisition Methods", "Description": "Description", "Type": "Type", "actions1": ""}, "DFM_HTRW": {"TaskName": "Task Name", "GroupName": "Group Name", "LastRunTime": "Last RunTime", "Interval": "Interval", "Status": "Status", "Describe": "Describe", "ApiUrl": "ApiUrl", "RequestType": "Request Type", "actions": "Actions"}, "DFM_GDSPZCJDPZ": {"ProductlineName": "Production line", "SegmentName": "Section", "Materialname": "Material name", "Intag": "Output collection point", "Oktag": "Acceptable output point", "Islastsegment": "Whether it is an assembly shop", "PlcOktag": "PLC produces TAG points", "Enable": "Enable or not"}, "DFM_SLDPZ": {"ProductlineName": "Production line", "SegmentName": "Section", "ProcessName": "Work Station", "MaterialName": "Material name", "MaterialType": "Material type", "TagId": "Collection point", "Uom": "Unit", "Tagert": "Target utilization", "BomQty": "Dosage per unit", "Price": "Price", "Seq": "Sort", "ComponentClass": "Component class", "ComponentName": "Component material", "ComponentCode": "Component part number", "Enable": "Enable or not"}, "TRACE_KGJC": {"WoStatus": "Work order status", "SapWoCode": "SAP order", "WoCode": "Work order", "ShiftName": "Shift", "TeamName": "Team", "FullLineName": "Production line", "CompanyName": "Section", "MaterialCode": "Product No", "MaterialDescription": "Product description", "WoQuantity": "Planned quantity (PCS)", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "actions": "Actions"}, "TRACE_SlGL": {"PersonnelCode": "Employee number", "PersonnelName": "Employee name", "Logindt": "Time on duty", "Logoutdt": "Leave time", "ProductlineName": "Production line", "ProductlineCode": "Production line coding", "SegmentName": "Work station", "SegmentCode": "Station code", "UnitName": "equipment", "UnitCode": "Device number", "FullLineName": "Product line", "WoCode": "Work order", "CompanyName": "Section", "TeamName": "Team or group", "ShiftName": "Shift", "ProcName": "Workstation", "MaterialCode": "Material Number", "MaterialDescription": "Material Description", "SapWoProductionCode": "Material name", "BatchQuantity": "Loading quantity", "ModifyUserId": "Staff", "CreateDate": "Time", "MaterialProductBatchNo": "Product Batch", "MaterialBatchNo": "Retroactive lot number", "FeedUnit": "units"}, "TRACE_GDGL": {"SapWoProductionCode": "Material name", "SendSapFlag": "SAP Status", "SapWoCode": "SAP order", "ShiftName": "Shift", "TeamName": "Team", "CompanyName": "Section", "FullLineName": "Production line", "SectionName": "Section", "WoCode": "Job number", "MaterialCode": "Part number of semi-finished product", "MaterialDescription": "Semi-finished product description", "WoQuantity": "Planned quantity (PCS)", "WoSumBatchQuantity": "<PERSON><PERSON> (PCS)", "WoConfirmBatchQuantity": "Confirmed Quantity (PCS)", "WoCompleteQuantity": "Completed Quantity (PCS)", "Reason": "Reason", "actions": "Actions", "WoStatus": "Status", "WoType": "Type", "PlanStartTime": "Plan StartTime", "PlanEndTime": "Plan EndTime", "ActualStartTime": "Actual StartTime", "ActualEndTime": "Actual EndTime"}, "TRACE_GDGL_XQ": {"BatchNo": "Batch No", "ShiftName": "Shift", "TeamName": "Team", "MaterialCode": "Product No", "BatchQuantity": "Quantity (PCS)", "BatchStartTime": "Start time", "BatchEndTime": "End time", "actions": "Actions"}, "TRACE_CCZY": {"FullLineName": "Product line", "ProcName": "Process", "CompanyName": "section", "TeamName": "Team", "ShiftName": "Shift", "WoCode": "Work order", "BatchNo": "Batch No", "BatchQuantity": "Number of batches (PCS)", "EquipmentName": "Equipment", "Status": "Status", "BatchStartTime": "Start time", "BatchEndTime": "End time"}, "TRACE_HXZY": {"ProcName": "Process", "FullLineName": "Production line", "CompanyName": "section", "TeamName": "Team", "ShiftName": "Shift", "WoCode": "Work order", "BatchNo": "Batch No", "BatchQuantity": "Number of batches (PCS)", "EquipmentName": "Equipment", "Status": "Status", "BatchStartTime": "Start time", "BatchEndTime": "End time"}, "TRACE_XNCS": {"CurrentProcName": "Process", "FullLineName": "Production line", "CompanyName": "section", "WoCode": "Work order", "TeamName": "Team", "ShiftName": "Shift", "MaterialCode": "Product No", "MaterialDescription": "Product description", "BatchNo": "Production batch", "BatchQuantity": "Number of batches (PCS)", "CurrentProcStatus": "Status", "ModifyDate": "Accept the time", "actions": "actions"}, "TRACE_CYCS": {"FullLineName": "Production line", "CompanyName": "section", "WoCode": "Work order", "MaterialCode": "Product No", "TeamName": "Team", "ShiftName": "Shift", "MaterialDescription": "Product description", "BatchNo": "Test lot No", "BatchQuantity": "Batch Quantity (PCS)", "BatchBadQuantity": "Bad Quantity (PCS)", "TestGrade": "Type", "Status": "Status", "CreateDate": "Operation time"}, "TRACE_BZRK": {"FullLineName": "Product line", "MaterialCode": "Materia Code", "MaterialDescription": "Material Description", "SheetNo": "Warehouse entry number", "SumQuantity": "Total sum", "BoxCount": "number of units", "BookDate": "Warehousing date"}, "TRACE_WGJC": {"FullLineName": "Production line", "CompanyName": "section", "WoCode": "Work order", "MaterialCode": "Product No", "MaterialDescription": "Product description", "TeamName": "Team", "ShiftName": "Shift", "BatchNo": "Test lot No", "BatchQuantity": "Batch Quantity (PCS)", "BatchBadQuantity": " Quantity (PCS)", "TestGrade": "Type", "Status": "Status", "CreateDate": "Operation time"}, "TRACE_PCHB": {"FullLineName": "Production line", "CompanyName": "section", "TeamName": "Team", "ShiftName": "Shift", "MaterialCode": "Product No", "MaterialDescription": "Product description", "BatchNo": "Test lot No", "BatchQuantity": "Number of batches (PCS)", "TestGrade": "Type", "CreateDate": "Combined batch time"}, "TRACE_PCHB_XQ": {"CompanyName": "Production line", "MaterialCode": "Product No", "TeamName": "Team", "ShiftName": "Shift", "BatchNo": "Test lot No", "RemainQuantity": "Number of batches (PCS)", "WoQuantity": "Consolidated Quantity (PCS)", "TestGrade": "Type", "actions": "actions"}, "TRACE_ABDSNCX": {"ProductLine": "Line body", "ProductModel": "Model number", "WoCode": "Work order number", "BatchNo": "Batch Number", "Sn": "SN", "SnProductionDate": "Product Date", "SnProductionShiftName": "Product Shift", "TestLevel": "Test grade", "TestResult": "Test result", "ProcessName": "Testing machine stand", "TestTime": "Test time", "TestFileName": "Test file name", "Segment": "Test section", "Remark": "Note information"}, "TRACE_GDSNCX": {"WoCode": "Work order number", "BatchNo": "Batch number", "Sn": "SN", "ShiftName": "shift", "TeamName": "team", "FactoryName": "factory", "FullLineName": "Production line", "MaterialCode": "Material code"}, "TRACE_GXYS": {"ProductCode": "Product part number", "SapProcessCode": "Sap process coding", "SapProcessName": "Sap process name", "MesProcess": "Mes process", "SpecialSetting": "Report special configuration", "MesProcessCode": "Mes process coding", "MesProcessName": "Mes process name"}, "TRACE_CXPZ": {"FullLineName": "Product line", "CompanyName": "Section", "MaterialCode": "Material code", "MaterialDescription": "Description of the material", "DefaultBatchQty": "Number of batches (PCS)", "AutoStartStopWo": "Automatic start and stop of work order", "actions": "actions", "AllowUpdateLastBatchQty": "Whether the last batch quantity can be changed", "AllowLastBatchQtyOverMax": "Whether to allow the number of last batches to exceed the maximum number of batches", "AllowCreateBatchType": "Allows manual generation of batch types", "PrintSectionName": "Print workshop alias", "MinBatchQty": "Minimum batch quantity(PCS)"}, "TRACE_DLZZY": {"Index": "Index", "EquipmentCode": "Equipment Code", "EquipmentName": "Equipment Name", "MaterialCode": "Materia Code", "MaterialName": "Material Name", "MaterialBatchNo": "Material Batch No", "MaterialZsBatchNo": "Material Zs Batch No", "MaterialBarcode": "Material Barcode", "StartTime": "Start Time", "EndTime": "End Time"}, "TRACE_BZ": {"FullLineName": "Production line", "MaterialCode": "Production Code", "MaterialDescription": "Production Name", "BoxNumber": "Case number", "BoxNo": "Case number", "InQty": "Quantity", "InStorageTime": "In Storage Time", "Quantity": "Quantity (PCS)", "ShiftName": "Shift", "TeamName": "Team", "SendSapFlag": "Whether to send SAP", "CreateDate": "Scan time"}, "TRACE_WLZS": {"BatchNo": "Material lot number", "WoCode": "Work order", "Type": "Item lot type", "TestGrade": "Test level", "MaterialCode": "Material code", "MaterialDescription": "Description of the material", "BatchQuantity": "Number of batches", "BindQuantity": "Binding of batches", "Unit": "Unit", "FeedingTime": "Loading Time", "CreateDate": "Lot Create Time"}, "TRACE_SCZS_GDXX": {"SapWoCode": "SAP order", "WoCode": "Work order", "ShiftName": "Shift", "FullLineName": "Production line", "CompanyName": "Section", "MaterialCode": "Product No", "MaterialDescription": "Product description", "WoQuantity": "Planned quantity (PCS)", "WoSumBatchQuantity": "<PERSON><PERSON> (PCS)", "WoConfirmBatchQuantity": "Confirmed Quantity (PCS)", "WoCompleteQuantity": "Completed Quantity (PCS)", "PlanStartTime": "Planned start time", "PlanEndTime": "Planned end time", "ActualStartTime": "Actual start time ", "ActualEndTime": "Actual end time", "Reason": "Reason"}, "TRACE_SCZS_PCXX": {"CompanyName": "Section", "BatchNo": "Batch number", "MaterialCode": "Product No", "MaterialDescription": "Product description", "BatchQuantity": "Quantity (PCS)", "BatchStartTime": "Start time", "BatchEndTime": "End time"}, "TRACE_SCZS_WLXX": {"WoCode": "Work order ", "MaterialCode": "Material Code", "MaterialDescription": "Description ", "ScadaTagQty": "Quantity of SCADA", "BomQty": "BOM amount", "ScadaConsumptionQty": "SCADA consumption", "ConfirmConsumptionQty": "Quantity consumed", "MaterialUom": "Unit"}, "TRACE_SCZS_ZLGL": {"TestItem": "Inspection item", "ProductionCode": "Product code", "ProductionName": "Product name", "Line": "Production line", "Segment": "Workshop section", "EquipmentName": "Work station", "Maxvalue": "Maximum value", "Minvalue": "Minimum value", "Standardvalue": "Standard value", "MeasuredValue": "Measured value"}, "TRACE_SCZS_GDSNCX": {"FullLineName": "Product line", "BatchNo": "Batch number", "SapWoCode": "Order number", "WoCode": "Work order", "Sn": "SN"}, "TRACE_SCZS_RYXX": {"PersonCode": "operator", "EquipmentCode": "Device coding", "EquipmentName": "Device name", "Level": "type", "UWB": "UWB", "Code": "Employee number", "Name": "Employee name", "JobContent": "Post", "Duration": "Duration", "Shift": "Shift"}, "TRACE_ZPGZ": {"CreateDate": "Crossing time", "FullLineName": "Product line", "WoCode": "Work order number", "MaterialCode": "Material code", "Sn": "SN", "Sns": "SN(scan code)", "ProcName": "Working procedure", "Result": "result", "Remark": "remarks"}, "TRACE_OQC": {"WoCode": "Work order number", "MaterialCode": "Material number", "BatchNo": "Batch number", "RemainQuantity": "quantity", "name": "Test item", "cjs": "Sampling number", "bls": "Bad number", "blyy": "Bad cause", "CheckItemName": "Test item", "Quantity": "quantity", "Reason": "Bad cause", "MaterialDescription": "Material description", "BatchQuantity": "quantity", "FullLineName": "Product line", "OqcBatchNo": "OQC batch number", "CheckQty": "Sampling number", "Result": "result", "Remark": "remarks", "BadQty": "Bad number"}, "TRACE_SFCCPYS": {"FullLineName": "Production line", "SectionCode": "Section", "ProductName": "Material name", "SfcProductName": "SFC product model", "SfcDbName": "SFC DB name", "SfcTableName": "SFC Table name", "SfcSnFormat": "SFC SN format", "SfcIpAddress": "SFC IP address", "Remark": "Remark"}, "TRACE_SNZXCX": {"FullLineName": "Product line", "CurrentProcName": "Work station", "SapWoCode": "Order number", "WoCode": "Work order number", "Sn": "SN", "BoxNo": "Case number", "MaterialCode": "Material name", "BatchNo": "Assembly lot number", "Status": "Status", "BoxTime": "Packing time"}, "TRACE_CPDTBD": {"Sn": "Monomer SN", "BoxSn": "Box SN", "BindTime": "Binding time"}, "TRACE_DYMBBD": {"ProductionName": "Material name", "PtlName": "Template name", "Status": "Enable or not"}, "ANDON_SET": {"PropertyCode": "Property Code", "Remark": "Remark", "PropertyValue": "Property Value", "actions": "Actions"}, "ANDON_BJLXGL": {"Enable": "Enable", "MessagePostTag": "Send an alarm", "OverMessagePostTag": "Send off alarm", "AlarmCode": "Coding", "AlarmName": "Name", "ProblemLevel": "Problem level", "DealType": "Type of disposal", "MessageTemplate": "Message template", "OverMessageTemplate": "The message closes the template", "UwbMessageTemplate": "Uwb message template", "ParentId": "Primary classification", "Icon": "icon", "Sort": "sort", "Description": "description", "actions": "operation"}, "ANDON_BJTZJL": {"AreaCode": "Production line", "areacode": "Production line", "ProductLine": "Section", "UniteCode": "Work Station", "EquipmentCode": "Equipment", "MainAlarmType": "First-level classification", "SubAlarmType": "Secondary classification", "PushType": "Push type", "Reciver": "Receiver", "NoticeTitle": "Title", "NoticeContent": "Content", "Status": "Send status"}, "ANDON_BJJL": {"IsMain": "Major or not", "Currentman": "responder", "EventNo": "Event number", "AreaName": "Production line", "areacode": "Production line", "ProductLineName": "Section", "UnitName": "Work Station", "EquipmentName": "Equipment", "MainAlarmType": "First-level classification", "SubAlarmType": "Secondary classification", "ProblemLevel": "Problem level", "EventLevel": "Alarm level", "RecordStatus": "Current status", "AlarmContent": "Alarm content", "Comment": "Instructions for closing the police", "Predicttime": "Estimated completion time(min)", "guid": "Current resolver", "Callman": "responder", "Calldate": "Alarm receiving time", "Overdate": "Completion time", "Closeman": "Close the police", "Closedate": "Police off time", "Wo": "Work order", "Process": "stage", "ActionType": "description", "Receiver": "Scada statistics", "StartTimeDiff": "Start with jet lag", "PreTimeDiff": "Time difference of previous step", "Dutydetail": "andon post", "Duty": "Alarm receiving team", "OutTime": "Upgrade time(min)", "EquipmentCode": "Material name", "MainAlarm": "First-level classification", "SubAlarm": "Secondary classification", "times": "sort", "state": "state", "Currentduty": "Responsible person", "Total": "Number of occurrences", "con": "duration", "repair_man": "Maintenance personnel", "ReasonCode": "Fault code", "CreateDate": "Occurrence time"}, "ANDON_DCTJBJ": {"ProductionLineCode": "Section", "Times": "Number of shutdowns", "Duration": "Cumulative duration of downtime"}, "ANDON_BJCFGZ": {"Maintype": "First-level classification", "Subtype": "Secondary classification", "Productlinecode": "Section", "Equipmentcode": "Equipment", "Variable": "Indicator name", "Syboml": "Comparator", "Standardvalue": "Standard value", "Deviationpercent": "Percentage of deviation(%)"}, "ANDON_BJSJGZ": {"UpgradeType": "Upgrade Type", "MainAlarmType": "First-level classification", "SubAlarmType": "Secondary classification", "EventLevel": "Event level", "OutTime": "Upgrade time(min)", "Dutydetail": "Responsible person", "NoticeType": "Notification method", "DealMode": "Deal Mode", "IsDownAllSend": "Send down in groups or no", "ResponseLevel": "Response level"}, "MATERIAL_MESCKJM": {"WarehouseCode": "Warehouse number", "WarehouseName": "Warehouse name", "WarehouseType": "Warehouse type", "ProductLine": "Product line", "Abbreviation": "abbreviation", "Floor": "floor", "Description": "Warehouse description", "SapPositionName": "SAP library location", "SapPosition": "SAP library bit coding"}, "MATERIAL_CKWLGZ": {"WarehouseCode": "Warehouse number", "Linename": "Production line", "MaterialName": "Material name", "WarehouseId": "Feed warehouse", "WarehouseType": "Warehouse type", "MaterialCode": "Material code", "Unit": "Unit", "Safevalue": "Safety stock", "Warningvalue": "Early warning warehouse", "Maxvalue": "Maximum inventory", "Floor": "floor"}, "MATERIAL_AGVCK": {"WarehouseCode": "Warehouse number", "WarehouseName": "Warehouse name", "BelongAreaCode": "Reservoir number", "BelongAreaName": "Name of reservoir area", "MaterialAreaTypeName": "Reservoir type", "PositionCode": "Warehouse number", "PositionName": "Repository name", "PositionType": "Library location type", "WarehouseLevel": "attribute", "Description": "description"}, "MATERIAL_CKGXJM": {"Floor": "floor", "Linename": "Production line", "WarehouseCode": "MES warehouse number", "WarehouseName": "MES warehouse name", "AGVWarehouseCode": "AGV Warehouse number", "AGVWarehouseName": "AGV Warehouse name", "BelongAreaCode": "AGV Warehouse area number", "BelongAreaName": "AGV Warehouse area name"}, "DFM_DWZH": {"MaterialCode": "Material", "FormUnitName": "Source unit name", "ConvertFormQty": "Source unit quantity", "ToUnitName": "Conversion unit name", "ConvertToQty": "Conversion unit quantity", "EffectiveBeginDate": "Effective start time", "EffectiveEndDate": "Effective end time", "Remark": "Remark"}, "MATERIAL_CXCKJM": {"LineCode": "section", "AreaCode": "Production line", "ProductCode": "Target material number", "ProductWarehouseCode": "Raw material bin", "StagingWarehouseCode": "Raw material temporary storage bin", "MesWarehouseCode": "MES produces virtual warehouse coding", "MesWarehouse": "MES produces virtual warehouses", "AGVBelongAreaName": "AGV output blanking area", "AGVBelongAreaCode": "AGV blanking area coding"}, "MATERIAL_CXSLD": {"LineCode": "Production line", "EquipmentCode": "Work station", "StagingWarehouseCode": "Raw material temporary storage bin", "MesWarehouseCode": "MES produces virtual warehouse coding", "AgvAreaName": "AGV feeding area", "AGVBelongAreaCode": "AGV blanking area coding"}, "MATERIAL_LLZCD": {"ReceivedWarehouseName": "WMS supply warehouse", "WarehousePositionName": "WMS materials warehouse", "WarehousePositionCode": "WMS warehouse code", "Orderid": "WMS Order number", "SN": "Dump flow", "DemandDate": "WMS demand date", "ReceivedDate": "WMS material delivery date", "OrderType": "WMS Order type", "WavesId": "WMS Batch number", "ReserveId": "WMS Reserved number", "ReceivedWarehouseCode": "WMS warehouse code", "MaterialCode": "material", "MaterialDescribe": "Material description", "Batchcode": "Requested batch", "Plan_Num": "Delivery quantity", "details": "View details", "Unit": "unit"}, "MATERIAL_YLKCGL": {"StockBack": "quantity", "ReturnNum": "quantity", "ReturnReason": "Reason", "Warehousename": "warehouse", "SapStore": "SAP library location", "Materialcode": "Material number", "Materialname": "Material name", "Num": "quantity", "Unit": "unit", "Batchcode": "batch", "Backbatchcode": "Traceable batch", "Productionline": "Production line", "PrepareTeam": "team", "IsNomarlV": "Abnormal or not", "Productiontime": "Date of production", "Sapcode": "SAP library location", "OldNum": "Original quantity", "NewNum": "Available quantity", "Description": "cause", "WareHouseName": "warehouse", "SapHouse": "SAP library location", "sapcode": "SAP library location", "ReceivedDate": "date", "MaterialCode": "Material number", "MaterialName": "Material name", "FullLineName": "Product line", "CompanyName": "section", "CompanyCode": "Section Code", "MaterialDescription": "Material description", "Quantity": "quantity", "Material": "material", "Uom": "unit"}, "MATERIAL_YLFXDDY": {"LineID": "Product line", "ProductionName": "Finished product description", "MaterialCode": "Part material number", "MaterialName": "Parts material description", "Unit": "unit", "PlanDate": "Demand date", "Shift": "Day shift", "Team": "Day team", "DemandNum": "Demand quantity", "ActualNum": "Actual quantity issued", "PlanDateB": "Demand date", "ShiftB": "Night shift", "TeamB": "Night team", "DemandNumB": "Demand quantity", "ActualNumB": "Actual quantity issued"}, "MATERIAL_WIPKCGL": {"AreaCode": "Product line", "WarehouseName": "MES virtual library", "AgvwarehouseName": "AGV warehouse", "AgvareawarehouseName": "AGV reservoir area", "AgvplacewarehouseName": "AGV library location", "Materialcode": "Material code", "Materialname": "Material name", "Outputdate": "Output time", "Batchnum": "Batch quantity", "Anum": "Current quantity (A)", "Bnum": "Number of defects (B)", "Cnum": "Quantity of waste (C)", "Status": "state", "Batchcode": "Batch number"}, "MATERIAL_JSGL": {"QualityGuaranteePeriod": "Shelf life (hours)", "QualityConfirmTime": "Large bottle glue reminder time (hours)", "QualityNoticeTime": "Vial glue warning time (minutes)", "GlueDate": "Gluing time", "IsLoading": "state", "LoadingDate": "Feeding time", "UnLoadingDate": "Blanking time", "EquipmentCode": "Device coding", "SingleTubeWeight": "Single tube capacity (g)", "Materialname": "Glue type", "Serialno": "Rubber hose bar code", "Expdate": "Failure time", "WarehouseCode": "Warehouse number", "WarehouseName": "Warehouse name", "Materialbatchno": "Material lot", "Materialcode": "Material number", "Indate": "Incoming time", "Shelflife": "Warranty period", "glueCode": "Glue type", "warehouse": "warehouse", "num": "Number of hose"}, "SHIFT_RYSJ_EYZSJ": {"Index": "Index", "Code": "Code", "Name": "Name", "UserAvatar": "head portrait", "Region": "Personnel sub-scope", "StaffGroup": "Staff sub-groups", "Factory": "factory", "BusinessDesc": "Personnel sub-scope", "BusinessDesc1": "Business type description", "WorkshopDesc1": "Workshop description", "WorkshopDesc": "factory", "VirtualOrganization": "Virtual organization", "LeaderName": "Superior leader", "LeaderCode": "Superior leader number", "Uwb": "UWB", "Price": "Unit price of maintenance cost(Hour)", "StartValidTime": "Unit price effective start time", "EndValidTime": "Unit price effective expiration time", "Rank": "position", "JobTitle": "Job title", "StarLevel": "Star rating", "Feishu": "<PERSON><PERSON><PERSON>", "actions": "actions"}, "SHIFT_RYSJ_RYCQ": {"Index": "Index", "Date": "Date", "Segment": "Segment", "StaffCode": "Employee number", "StaffName": "Employee Name", "Shift": "Shift", "Team": "Team", "Type": "Staff category", "Type2": "Type", "Type3": "Catelog", "ClockInTime": "Clock in", "ClockOffTime": "Clock out", "HrTimes": "Clocking time", "WorkTimes": "UWB piecework time", "UwbWorkTimes": "UWB attendance time", "Yield": "yield", "PieceworkPost": "Piecework position", "PieceworkWage": "Piecework wage", "Bonus": "Reward for overproduction", "Source": "Secondment source", "Line": "Production line", "Process": "Process", "State": "State", "StateDisplayName": "attendance", "ConfirmTimes": "Confirmed working hours", "actions1": ""}, "SHIFT_RYSJ_JJBZ": {"Index": "Index", "MaterielCode": "Material Code", "MaterielDescription": "Description of the material", "SubsectionOne": "Section", "ShiftSystem": "Shift system", "SubsectionTwo": "Process", "JobContent": "work title", "Department": "Sectoral classification", "JobTitle": "Configure the work title", "StageTarget": "Phase objectives", "ManMachineRatio": "Phase objectives", "BasicOutput": "Piece basis capacity", "TargetPpm": "Target PPM", "BasicWage": "Base wage", "BasicPrice": "Base unit price", "AllowanceDay": "Daily allowance standard", "actions": "actions"}, "SHIFT_RYSJ_JJBZ_CCBZ": {"Index": "Index", "MinPercent": "Minimum percentage", "MaxPercent": "Maximum percentage", "AllowancePercent": "Proportion of allowance", "actions": "actions"}, "SHIFT_RYSJ_RYCXPZ": {"LeaderName": "Leader<PERSON>ame", "Index": "Index", "StaffCode": "Employee number", "StaffName": "Employee name", "Line": "Production line", "Segment": "Section", "Process": "Process", "MaterialCode": "Material Code", "PricePost": "Piecework post", "AndonPost": "Andon post", "WokrHours": "Shift system", "Type": "Staff category", "Type2": "Type", "Type3": "Catelog", "ChangeShifts": "Transfer or not", "actions": "actions"}, "TPM_SBGL_SBGLZY": {"RepairCode": "Repair work order", "DeviceName": "Device name", "ProductlineName": "Name of production line", "actions": "actions"}, "TPM_SBGL_SBTZGL": {"Index": "Index", "Name": "Device name", "Code": "EAM code", "Type": "Device type", "EamCode": "EAM code", "EipCode": "EIP code", "LeaveCode": "Ex-factory code", "Eqmodel": "Model", "BarCode": "Bar Code", "PersonName": "Responsible person", "MyCycle": "Maintenance period", "Purchasedate": "Date of acquisition", "Manufacturer": "Manufacturer", "Contacts": "Contact person", "Tel": "Phone", "MaintainPosition": "Regular maintenance parts", "MaintainMethod": "Maintenance method", "MachineHour": "Maintenance time (minutes)", "MyProject": "Maintenance item", "MaintainPlandata": "Planned maintenance time", "FirstMaintenanceTime": "First maintenance time", "LastMaintenanceTime": "Last maintenance time", "ProductlineName": "Production line code", "LineName": "Name of production line", "SegmentName": "Station name", "Productiondate": "Date of manufacture", "Age": "Service life", "Status": "Device status", "Cost": "Cost", "Smq": "Scanner gun", "Dyj": "Printer", "Pmj": "Inkjet printer", "Remark": "Remark", "actions": "actions", "EquipCode": "EquipCode", "SubName": "SubName", "SuperiorEquit": "SuperiorEquit", "Equipnature": "Equipnature", "Factory": "Factory", "Department": "Department", "Area": "Area", "Group": "Group", "WorkCenter": "WorkCenter", "CostCenter": "CostCenter", "Accountingmethod": "Accountingmethod", "Assetnumber": "Assetnumber", "AssetName": "AssetName", "OldCode": "OldCode", "serialnumber": "serialnumber", "Licenseplatenumber": "Licenseplatenumber", "Factorycode": "Factorycode", "EquipItem": "EquipItem", "EquipType": "EquipType", "EquipGroup": "EquipGroup", "brand": "brand", "Specmodel": "Specmodel", "Number": "Number", "Unit": "Unit", "EquipStatus": "EquipStatus", "UseStatus": "UseStatus", "IncomeDate": "IncomeDate", "isImported": "isImported", "manufacturer": "manufacturer", "supplier": "supplier", "producer": "producer", "Manufacturingdate": "Manufacturingdate", "dateproduction": "dateproduction", "useYear": "useYear", "Expectedscrapdate": "Expectedscrapdate", "Scrapdate": "Scrapdate", "factoryDate": "factoryDate", "TestDate": "TestDate", "UseDate": "UseDate", "EquipValue": "EquipValue", "EquipSize": "EquipSize", "EquipWeight": "EquipWeight", "FixLimit": "FixLimit", "Storagelocation": "Storagelocation", "position": "position", "Nowposition": "Nowposition", "Inventorysituation": "Inventorysituation", "Keyequipmentstatistics": "Keyequipmentstatistics", "personresponsible": "personresponsible", "equipmentSupervisor": "equipmentSupervisor", "Inspectiontime": "Inspectiontime"}, "TPM_SBGL_SBTZGL_BPBJQD": {"Remark": "Remark", "Index": "Index", "Name": "Name of the spare part", "Code": "Spare parts code", "Jigtype": "Type of spare part", "Qty": "Apply Quantity", "Maxqty": "Maximum inventory quantity", "Safeqty": "Safety stock quantity", "SAPQty": "SAPQty", "brand": "Brand", "supplier": "Supplier", "OutStock": "OutStock", "Batch": "<PERSON><PERSON>", "OutWarehouse": "OutWarehouse", "Unit": "Unit", "actions": "actions", "Jigspec": "Jigspec"}, "TPM_SBGL_SBTZGL_SBWJ": {"Code": "Code", "Name": "Name", "GroupName": "Group Name", "DataType": "Data Type", "DefaultValue": "Default Value", "Remark": "Remark", "FileName": "FileName", "FileVersion": "FileVersion", "Type": "Type", "Size": "Size", "CreaterTime": "CreaterTime"}, "TPM_SBGL_SBTZGL_SBZB": {"Index": "Index", "RunningBeat": "Indicator name", "BeatSpeeding": "Indicator value", "MachineHour": "<PERSON><PERSON><PERSON><PERSON>", "actions": "actions"}, "TPM_SBGL_SBTZGL_SBBOM": {"Index": "Index", "ParentId": "Please select the parent level", "AccessoriesName": "Name", "AccessoriesCode": "Code", "Type": "Type", "Specifications": "Specification and model", "Remark": "Remark", "actions": "actions", "Quantity": "Quantity"}, "TPM_SBGL_SBTZGL_SBLL": {"WXDH": "Repair Order Number", "BXNL": "Repair content", "ZT": "Status", "JJD": "Emergency Level", "WXZT": "Repair Status", "SFTJ": "Shutdown or Not", "GDLX": "Type", "GDLY": "Source", "GLDH": "ReferOrderNo", "SparePartsCode": "SparePartsCode", "SparePartsName": "SparePartsName", "status": "Status", "requestNo": "requestNo", "SType": "SType", "SparePartsSpec": "SparePartsSpec", "Batch": "<PERSON><PERSON>", "supplier": "Supplier", "number": "Number", "BYZT": "Status", "BYJHSJ": "PlanMaintainDate", "BYWCSJ": "FinishMaintainDate", "ZRR": "Responsible Person", "BYJG": "Results", "DJZT": "Status", "DJJHSJ": "CheckPlanTime", "DJWCSJ": "ActualFinishTime", "DJR": "CheckBy", "DJJG": "Results", "SBZG": "Manager", "Index": "Index", "Action": "Action", "Behavior": "Behavior", "Creator": "Creator", "CreateDate": "CreateDate", "Remark": "Remarks"}, "TPM_SBGL_SBBJGL": {"Index": "Index", "Remark": "Shelf number", "SparePartsCode": "Spare part number", "SType": "Type of spare part", "SparePartsName": "Name of the spare part", "UnitPrice": "Unit", "CurrentStock": "Current inventory", "MaxStock": "Maximum inventory", "MinStock": "Minimum inventory", "actions": "actions", "Cycle": "Procurement cycle", "ExtendTime": "Suggest next purchase date", "Introduce": "Remark", "isselector": "The current stock is less than the minimum stock", "SparePartsSpec": "SparePartsSpec", "Batch": "<PERSON><PERSON>", "BookDate": "BookDate", "Stock": "Stock", "Warehouse": "Warehouse", "brand": "brand", "supplier": "supplier"}, "TPM_SBGL_SBBJGL_XQ": {"Index": "Index", "Buynum": "State", "Usednum": "quantity", "Owner": "user", "actions": "actions"}, "TPM_SBGL_SBBJGL_RK": {"Index": "Index", "Remark": "Shelf number", "SparepartCode": "Stock number", "SparepartName": "Stock name", "Buynum": "Storage quantity"}, "TPM_SBGL_SBBJGL_CK": {"Index": "Index", "DeviceCode": "EAM code", "DeviceName": "Device name", "PartsCode1": "Stock number", "Parts1": "Stock name", "Parts1Num": "Quantity delivered"}, "TPM_SBGL_SBBJGL_CRK": {"Index": "Index", "Buynum": "Type", "Remark": "Shelf number", "SparepartCode": "Spare part number", "SparepartName": "Spare part name", "Usednum": "Quantity", "CreateDate": "Time"}, "TPM_SBGL_BJZSJ": {"PurchaseCycle": "PurchaseCycle", "MinPurchaseQty": "MinPurchaseQty", "Remark": "Remark", "SparePartsCode": "Parts Code", "SparePartsName": "Parts Name", "SType": "Parts Type", "SparePartsSpec": "Parts Spec", "Min": "Min", "Max": "Max", "Useyears": "Useyears", "Unit": "Unit"}, "TPM_SBGL_BJKZGL": {"Unit": "Unit", "SparePartsCode": "Parts Code", "SparePartsName": "Parts Name", "SType": "Parts ype", "SparePartsSpec": "Parts Spec", "BatchCode": "BatchCode", "InstoreDate": "InstoreDate", "Warehouse": "Warehouse", "StorageBin": "StorageBin", "Stock": "Stock", "Brand": "Brand", "Supplier": "Supplier"}, "TPM_SBGL_SBBJGL_CKSZ": {"Index": "Index", "Code": "Code", "Name": "Name", "Remark": "Remark", "actions": "actions", "Type": "Type", "Spec": "Spec", "Min": "Min", "Max": "Max", "Useyears": "Useyears", "Unit": "Unit"}, "TPM_SBGL_SBWXGD": {"_ZPR": "Assign<PERSON><PERSON>", "Index": "Index", "FaultCode": "Fault type", "RepairCode": "Repair No", "EventNum": "Event number", "RepairStatus": "Maintenance status", "RepairType": "Maintenance type", "InputType": "Input type", "EipCode": "EIP code", "_CX": "Line Code", "Eqmodel": "Model", "Purchasedate": "Manufacturing date", "LeaveCode": "Factory code", "DeviceCode": "EAM code", "DeviceName": "Equipment name", "ProductlineCode": "Production line ", "ProductlineName": "production line name", "LineCode": "Section code", "LineName": "Section name", "ShiftName": "Shift", "BomName": "BOM", "Allday": "Full shift or not", "AbnormalDesc": "Description of the exception", "PlanWorkuser": "Appoint the executor", "CompleteWorkuser": "Actual executor", "PlanManhours": "Scheduled work", "Comstardate": "Maintenance start time", "Completedate": "Maintenance completion time", "Remark": "Remark", "PriceSum": "Cost", "WorkSum": "Working hours", "actions": "actions"}, "TPM_SBGL_SBWXJL_WXMX": {"Index": "Index", "ExceptionDesc": "Description of the exception", "RepairProcess": "Description of the maintenance process", "CurrentSituation": "Cause analysis-current situation", "RepairStatus": "Maintenance status", "Reasons1": "Cause analysis", "Parts1": "Consumable spare parts", "Parts1Num": "Quantity consumed", "RepairUser": "Undertaker", "StartTime": "Start Time", "EndTime": "End Time", "RepairHours": "Maintenance time(hours)", "RepairPrice": "Labor cost", "FaultPhenomenon": "Fault phenomenon", "IsCase": "Is it a case?", "actions": "actions"}, "TPM_SBGL_SBWXJL_TSMX": {"Index": "Index", "Debugtime": "Repair time", "Debugperson": "Name of applicant", "Dealtime": "Debugging start time", "Fixedtime": "Debug end time", "Maintenanceperson": "Name of handler", "Confirmtime": "Confirmation time", "Confirmperson": "Confirmer's name", "Debugtype": "Debugging type", "Debugcode": "Debugging code", "Failuredescription": "Debugging description", "Remark": "Remark", "Status": "Status", "Tools": "Tools", "actions": "actions"}, "TPM_SBGL_SBWXJL_WXCB": {"Index": "Index", "PartsRepairPrice": "Spare parts cost", "RepairPrice": "Labor cost"}, "TPM_SBGL_SBBYXM": {"Status": "Status", "Index": "Index", "UsingTime": "UsingTime", "DeviceCategory": "Device type", "McProject": "Maintenance item", "CheckStandard": "Inspection standard", "Methods": "Measures and methods", "Tools": "Tool", "ProjectCategory": "Type of project", "PersonName": "Responsible person", "UpperLimit": "Upper limit", "LowerLimit": "Lower limit", "InputType": "Input mode", "DataFrom": "Source of data", "Classify": "Classification", "Duration": "Maintenance time (minutes)", "MaintainCycle": "Maintenance interval", "Isenable": "Status", "Remark": "Remark", "actions": "actions", "ProjectCode": "Project Code", "ProjectName": "Project Name", "byzq": "MaintainCycle", "bypl": "MaintainFrequency", "Inscycle": "Inscycle", "Insfrequency": "Insfrequency", "executiontime": "executiontime", "Inspectiontime": "Inspectiontime", "File": "File"}, "TPM_SBGL_WXJY": {"DeviceCategoryName": "DeviceCategoryName", "DeviceCategoryId": "DeviceCategoryId", "Source": "Source", "Type": "Type", "Description": "Description", "IsStop": "IsStop", "Urgency": "Urgency", "RepairDuration": "RepairDuration（min）", "FaultCategory": "FaultCategory", "ReasonCategory": "ReasonCategory", "RepairRecordDesc": "RepairRecordDesc", "Reason": "Reason", "ReasonResult": "ReasonResult", "RepairNature": "RepairNature", "QueryCount": "QueryCount", "Keyword": "Keyword", "Remark": "Remark"}, "TPM_SBGL_SBDJXM": {"Context": "Context", "Index": "Index", "DeviceCategory": "Device type", "McProject": "Spot check item", "CheckStandard": "Inspection standard", "Methods": "Measures and methods", "Tools": "Tool", "ProjectCategory": "Type of project", "PersonName": "Responsible person", "UpperLimit": "Upper limit", "LowerLimit": "Lower limit", "InputType": "Input mode", "DataFrom": "Source of data", "Classify": "Classification", "Duration": "Inspection duration (minutes)", "MaintainCycle": "Spot check cycle", "Isenable": "Status", "Remark": "Remark", "actions": "actions"}, "TPM_SBGL_SBBYJH": {"Wo": "Wo", "Index": "Index", "ProductlineCode": "Production line ", "ProductlineName": "production line name", "LineCode": "Section code", "LineName": "Section name", "DeviceId": "EAM code", "DeviceCode": "Equipment  name", "DeviceCategory": "Device type", "MaintainUsername": "Maintenance manager", "PersonName": "Maintenance manager", "PlanTimeTask": "Scheduled maintenance date", "StartTime": "Scheduled start time", "EndTime": "Scheduled end time", "ActualFinishTime": "Actual finish time", "Executor": "Executor", "Status": "Maintenance status", "MaintainUser": "Keeper", "Remark": "Remark", "PropertyCode": "Maintenance type", "actions": "actions", "FinishMaintainDate": "FinishMaintainDate", "PlanMaintainDate": "PlanMaintainDate", "UsingTime": "UsingTime", "Area": "Area", "Groups": "Groups", "MaintainBy": "MaintainBy", "Manager": "Manager"}, "TPM_SBGL_SBBYJH_XQ": {"MaintainBy": "MaintainBy", "Status": "Status", "Index": "Index", "MaintainProject": "Maintenance items", "MaintainValue": "Maintenance value", "MaintainStatus": "Maintenance status", "MaintainUser1": "Keeper", "MaintainDate": "Start time", "PlanMaintainDate": "Plan Maintain time", "ActualFinishTime": "Actual finish time", "Executor": "Actual executor", "Remark": "Remark", "actions": "actions", "byzq": "Maintain Cycle", "bypl": "Maintain Frequency", "IsStop": "IsStop", "UsingTime": "UsingTime", "InputType": "InputType", "McProject": "McProject", "McValue": "McV<PERSON>ue", "McStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FjStatus": "FjStatus", "FjValue": "FjValue", "FjExecutor": "FjExecutor", "McUser": "<PERSON><PERSON><PERSON><PERSON>", "McDate": "McDate", "ProjectCode": "ProjectCode", "ProjectName": "ProjectName", "CheckStandard": "CheckStandard", "Methods": "Methods", "Tools": "Tools", "CheckCycle": "CheckCycle", "SparePartsCode": "SparePartsCode", "SparePartsName": "SparePartsName", "SType": "SType", "SparePartsSpec": "SparePartsSpec", "Batch": "<PERSON><PERSON>", "supplier": "supplier", "number": "number", "status": "status", "requestNo": "requestNo"}, "TPM_SBGL_SBBYGZ": {"Index": "Index", "MaintainProject": "Device type", "MaintainCycle": "Maintenance interval", "MaintenancePeriod": "Maintenance cycle (days)", "Remark": "Reason for modification", "Isenable": "Status", "Startime": "Start time", "Endtime": "End time", "Duration": "Maintenance time (minutes)", "actions": "actions"}, "TPM_SBGL_SBDJJH": {"InputContextMaintenance": "Please enter the maintenance results", "InputContext": "Please enter the inspection results", "_DJXMQD": "Check Item List", "DeviceName": "DeviceName", "CheckPlanTime": "Plan Check Time", "factory": "Factory", "area": "Area", "group": "Group", "PersonName": "Person", "equipmentSupervisor": "Supervisor", "Index": "Index", "DeviceCode": "Equipment  name", "DeviceId": "EAM code", "DeviceCategory": "Device type", "Shift": "Shift", "ActualFinishTime": "Actual finish time", "McStatus": "Spot inspection status", "FileCode": "File number", "Remark": "Remark", "actions": "actions"}, "TPM_SBGL_SBDJJH_XQ": {"Context": "Context", "Index": "Index", "CheckFile": "CheckFile", "FilePath": "FilePath", "McProject": "Spot check items", "McValue": "Spot check value", "McStatus": "Spot check condition", "FjValue": "Reinspection record", "FjStatus": "Recheck status", "FjExecutor": "<PERSON><PERSON><PERSON><PERSON>", "McUser": "inspector", "Executor": "Actual executor", "PlanCheckDate": "Plan Check Time", "ActualFinishTime": "Actual finish time", "McDate": "Spot check time", "ProjectCode": "Project Number", "CheckStandard": "Inspection standard", "Methods": "Measures and methods", "Tools": "Tool", "CheckCycle": "Spot detection period", "Remark": "Remark", "actions": "actions"}, "TPM_SBGL_SBDJGZ": {"Index": "Index", "McProject": "Spot check items", "McCycle": "Routine inspection cycle", "ImplementTime": "Execution time", "StartUsing": "Whether or not to enable", "Remark": "Remark", "actions": "actions"}, "TPM_SBGL_GZZSK": {"Index": "Index", "RepairProject": "Case name", "CheckStandard": "Inspection standard", "PersonName": "Cause of the problem", "Methods": "Measures and methods", "Tools": "Tool", "Remark": "Remark", "actions": "actions"}, "TPM_SBTJJL": {"Line": "Production line", "Segment": "Section", "Unit": "Work Station", "MachineCode": "Equipment", "Shift": "Shift", "Reason": "Cause of shutdown", "Difftime": "Downtime(minutes)", "LossQty": "Amount of loss", "StartTime": "Start time", "EndTime": "End time", "actions1": ""}, "TPM_SBTJJL_XQ": {"Line": "Production line", "Segment": "Section", "Unit": "Work Station", "MachineCode": "Equipment", "StopReason": "Cause of shutdown", "Duration": "Duration(minutes)", "StartTime": "Start time", "EndTime": "End time", "LossNum": "Amount of loss", "ReworkNum": "Amount of rework", "Remark": "Remark", "actions1": ""}, "ariaLabel": {"sortDescending": "Sorted descending.", "sortAscending": "Sorted ascending.", "sortNone": "Not sorted.", "activateNone": "Activate to remove sorting.", "activateDescending": "Activate to sort descending.", "activateAscending": "Activate to sort ascending."}, "INV_KCQD": {"Sapformula": "Sap Formula", "Bucketnum": "Bucket <PERSON>", "Suppiername": "Suppier Name", "Formula": "Formula", "detail": "Detail", "Material": "Material", "PpmPro": "ProcessOrder", "Class": "Class", "BatchStatus": "BatchStatus", "Batch": "<PERSON><PERSON>", "Remark": "Remark", "SSCCStatus": "SSCCStatus", "SSCC/Container": "SSCC/Container", "Quantity": "Quantity", "Location": "Location", "Expiration": "Expiration", "Allergens": "Allergens", "Created": "Created", "UOM": "UOM", "Lot": "Lot", "Result": "Result"}, "INV_SCLS": {"Formula": "Formula", "Mblnr": "sap凭证号", "Msg": "sapMsg", "Type": "sapResult", "ProcessOrder": "Process Order", "Machine": "Machine", "Destination": "Destination", "ShiftId": "Shift Id", "Source": "Source", "SAP": "SAP", "Reason": "Reason", "Comment": "Comment", "ProductDate": "Product Date", "Date": "Date", "Material": "Material", "BatchStatus": "BatchStatus", "SendStates": "SendStates", "Batch": "<PERSON><PERSON>", "SSCCStatus": "SSCCStatus", "SSCC": "TraceCode", "operate": "operate", "Quantity": "Quantity"}, "INV_WLKCPD": {"BatchCode": "BatchCode", "InventQty": "InventQty", "MoveActureQty": "Move ActureQty", "DifferenceQty": "DifferenceQty", "Sapno": "Sapno", "Remark": "Remark", "Isread": "IsRead", "Inventtype": "Invent Type", "Over": "Over", "Tasktype": "Tasktype", "MaterialName": "MaterialName", "MaterialCode": "MaterialCode", "Material": "Material", "StatusLot": "", "LotId": "LotId", "StatusSlot": "", "SubLotId": "SubLotId", "CurrentQuantity": "Current Quantity", "Equipment": "Equipment", "Movetime": "Move Time", "ActualQuantity": "Actual Quantity", "operate": "Operate", "Result": "Result", "Difference": "Difference", "Diff": "<PERSON><PERSON>", "Reason": "Reason", "detail": "Detail", "Plandate": "Plan Date", "TaskStatus": "Task Status", "ModifyDate": "Modify Date", "CreateUserId": "CreateUser"}, "INV_HTKCPD": {"Over": "盘存结束", "MaterialName": "MaterialName", "Sapformula": "Sapform<PERSON>", "MaterialCode": "MaterialCode", "Material": "Material", "StatusLot": "", "LotId": "LotId", "StatusSlot": "", "SubLotId": "SubLotId", "CurrentQuantity": "Current Quantity", "Equipment": "Equipment", "Movetime": "Move Time", "ActualQuantity": "Actual Quantity", "operate": "Operate", "Result": "Result", "Difference": "Difference", "Diff": "<PERSON><PERSON>", "Reason": "Reason", "detail": "Detail", "Plandate": "Plan Date", "TaskStatus": "Task Status", "ModifyDate": "Modify Date", "CreateUserId": "CreateUser"}, "INV_YLKCPD": {"Over": "盘存结束", "MaterialName": "MaterialName", "MaterialCode": "MaterialCode", "Material": "Material", "StatusLot": "", "LotId": "LotId", "StatusSlot": "", "SubLotId": "SubLotId", "CurrentQuantity": "Current Quantity", "Equipment": "Equipment", "Movetime": "Move Time", "ActualQuantity": "Actual Quantity", "operate": "Operate", "Result": "Result", "Difference": "Difference", "Diff": "<PERSON><PERSON>", "Reason": "Reason", "detail": "Detail", "Plandate": "Plan Date", "TaskStatus": "Task Status", "ModifyDate": "Modify Date", "CreateUserId": "CreateUser"}, "INV_SCTJ": {"Formula": "Formula", "detail": "Detail", "ProcessOrder": "Process Order", "ExecutionStatus": "Execution Status", "Machine": "Machine", "Material": "Material", "Shift": "Shift", "ShiftDate": "ShiftDate", "Line": "Line", "Planned": "Planned", "Daily": "Daily", "Total": "Total", "Complete": "% Complete", "Destination": "Destination", "LastProduction": "Last Production", "Batch": "<PERSON><PERSON>", "Quantity": "Quantity", "Inventory": "Inventory", "SublotCount": "Sublot Count", "operate": "operate"}, "INV_YJC": {"Tipping": "Tipping", "TraceCode": "TraceCode", "BatchPallet": "BatchPallet", "Loaction": "Loaction", "ProcessOrder": "ProcessOrder", "Material": "Material", "Batch": "<PERSON><PERSON>", "Quantity": "Quantity", "Deadline": "Deadline", "PreCheck": "PreCheck"}, "INV_XFHL": {"SSCCValue": "SSCC", "Suppiername": "Suppiername", "Mblnr": "sap凭证号", "Msg": "sapMsg", "Type": "sapResult", "SendStates": "SendStates", "ProcessOrder": "Process Order", "Machine": "Machine", "Destination": "Destination", "ShiftId": "Shift", "Source": "Source", "SAP": "SAP", "Reason": "Reason", "Comment": "Comment", "ProductDate": "Product Date", "Date": "Date", "Material": "Material", "BatchStatus": "BatchStatus", "Batch": "<PERSON><PERSON>", "SSCCStatus": "SSCCStatus", "SSCC": "TraceCode", "operate": "operate", "Quantity": "Quantity"}, "INV_CSLS": {"Suppiername": "Suppier name", "OldLocation": "Source", "NewLocation": "Source Material", "BatchStatus": "Batch Status", "Batch": "<PERSON><PERSON>", "NewLotId": "<PERSON><PERSON>", "OldLotId": "<PERSON><PERSON>", "SSCCStatus": "TraceCode Status", "SSCC": "TraceCode", "sscc/container1": "TraceCode", "sscc/container2": "TraceCode", "Destination": "Destination", "DestinationMaterial": "Destination Material", "Quantity": "Quantity", "SAP": "SAP", "Type": "Type", "Date": "Date", "WmsPrintno": "WMS PNO", "SapPrintno": "Sap PNO", "Tranremarks": "Remarks", "OldProBatch": "OldProBatch", "NewProBatch": "NewProBatch", "operate": ""}, "INV_TPQD": {"detail": "Detail", "Machine": "Machine", "ProcessOrder": "Process Order", "Material": "Material", "Pallet": "<PERSON><PERSON><PERSON>", "Destination": "Destination", "Quantity": "Quantity", "PrintCount": "Print Count", "InsortedAt": "Insorted At", "Verified": "Verified", "Produced": "Produced", "operate": ""}, "INV_PCTP": {"Sequence": "Sequence", "detail": "Detail", "PlanTime": "PlanTime", "Formula": "Formula", "id": "Id", "Loaction": "Loaction", "Bin": "Bin", "PO": "PO", "Machine": "Machine", "Batch": "<PERSON><PERSON>", "Material": "Material", "FullBags": "Full Bags", "PartialBags": "Partial Bags", "TotalBags": "Total Bags", "Complete": "Complete", "CreatedBy": "CreatedBy", "Date": "Date", "BatchStatus": "BatchStatus", "SSCCStatus": "SSCCStatus", "SSCC": "TraceCode", "Type": "Type", "Expiry": "Expiry", "User": "User", "TransferDate": "TransferDate", "SourceSSCC": "SourceSSCC", "Quantity": "Quantity", "Action": "Action", "Details": "Details"}, "INV_RQGL": {"detail": "Detail", "split": "Split", "Status": "Status", "Location": "Location", "Material": "Material", "BatchStatus": "Batch Status", "Batch": "<PERSON><PERSON>", "SSCCStatus": "SSCC Status", "SSCC": "TraceCode", "id": "Id", "Quantity": "Quantity", "Expiration": "Expiration", "PO": "PO(Batch)", "StatusTime": "Status Time", "SSCC/Container": "SSCC/Container", "Result": "Result", "BinSLOC": "Bin/SLOC", "Allergens": "Allergens", "Action": "Action", "Comment": "Comment", "User": "User", "Date": "Date"}, "INV_YLBQ": {"Remark": "Remark", "Sapformula": "Formula No", "Bucketnum": "Bucket <PERSON>", "index": "Index", "ProcessOrder": "Process Order", "BNumber": "Process Batch", "Material": "Material", "TagerQty": "<PERSON><PERSON><PERSON><PERSON>", "BagSize": "BagSize", "LotCode": "LotCode", "SubLotcode": "SubLotcode", "InventQty": "InventQty", "FullPage": "FullPage", "InventPqty": "InventPqty", "Expiration": "Expiration", "operate": "OPerate"}, "INV_CLZB": {"TagertQuantity": "Tagert Quantity", "Starts": "PlanDate", "NowState": "Now State", "SAPDate": "SAP Date", "Sequence": "Sequence", "JLCode": "Sauce Code", "JLName": "Sauce Name", "JLNumber": "Sauce Number", "Mdetialdesc": "Material Desc", "SSCC/Batch": "SSCC/Batch", "Expiry": "Expiry", "ShiftName": "Shift Name", "FormulaNo": "Formula No", "SbStatus": "Sb Status", "LStatus": "Line Status", "SSCC": "TraceCode", "Line": "Line", "TransferDate": "Transfer Date", "User": "User", "BatchStatus": "Batch Status", "SSCCStatus": "SSCC Status", "ProcessOrder": "Process Order", "Resource": "Resource", "POStatus": "PO Status", "Material": "Material", "SSCC/Container": "SSCC/Container", "Type": "Type", "Location": "Location", "Expiration": "Expiration", "Bin": "Bin", "ProductFamily": "Product Family", "Quantity": "Quantity", "Batches": "Batches", "Sequences": "Batches", "Start": "Start", "Batch": "<PERSON><PERSON>", "PrepStatus": "Prep Status", "TippingDone": "Tipping Done", "Ingredients": "Ingredients", "Complete": "Complete"}, "INV_CLQD": {"LineCode": "Line Code", "Check": "Check", "PO": "PO", "Machine": "Machine", "Phase": "Phase", "Batch": "<PERSON><PERSON>", "QuantityRequired": "Quantity Required", "Material": "Material", "Quantity": "Quantity", "Inventory": "Inventory Quantity Available", "BagSize": "Bag Size", "FullBags": "Full Bags", "PartialBags": "Partial Bags", "Complete": "Complete", "CompleteStates": "Complete", "Consumed": "Consumed"}, "PRO_Weight": {"detail": "Detail", "Sequence": "Sequence", "Po": "PO", "Batch": "<PERSON><PERSON>", "Item": "<PERSON><PERSON>", "Formula": "Formula", "ModifyDate": "DateTime", "ModifyUserId": "ModifyUserId", "AverageWeight": "AverageWeight", "Weight": "Weight", "operate": "operate"}, "PRO_Overview": {"Sequence": "Sequence", "Formula": "Formula", "PlantNode": "Plant Node", "po": "PO", "Material": "Material", "Batch": "<PERSON><PERSON>", "BatchQty": "<PERSON><PERSON>", "Planned": "Planned", "Total": "Total", "Complete": "% Complete"}, "Sampling": {"gc": "<PERSON><PERSON>", "TestingTime": "Testing Time", "qysj": "Sampling Time", "rqbh": "ContainerId", "gdh": "WoCode", "Formula": "Formula", "sbmc": "Equipment Name", "cpbm": "Production Code", "rqzt": "Container Status", "glbdzt": "Associate Form Status", "Type": "Sampling Type"}, "PRO_QualityResult": {"operate": "", "Formula": "Formula", "CreateUserId": "Sam<PERSON><PERSON>ser", "Equipment": "Equipment", "GroupName": "GroupName", "Date": "Date", "Shift": "Shift", "Performance": "Performance", "PO": "PO", "Batch": "<PERSON><PERSON>", "SSCC/Container": "SSCC/Container", "Material": "Material", "Score": "Score", "Status": "Status", "Approvals": "Approvals", "ok": "Ok", "SH": "Audit", "Comments": "Comments"}, "PRO_QAQualityResult": {"operate": "", "Formula": "Formula", "CreateUserId": "Sam<PERSON><PERSON>ser", "Equipment": "Equipment", "GroupName": "GroupName", "Date": "Date", "Shift": "Shift", "Performance": "Performance", "PO": "PO", "Batch": "<PERSON><PERSON>", "SSCC/Container": "SSCC/Container", "Material": "Material", "Score": "Score", "Status": "Status", "Approvals": "Approvals", "ok": "Ok", "SH": "Audit", "Comments": "Comments"}, "PRO_Logsheets": {"Comment": "Comment", "Formula": "Formula", "Date": "Date", "Shift": "Shift", "User": "User", "Status": "Status", "ProcessOrder": "Process Order", "Supplier": "Supplier", "Quantity": "Quantity", "Code": "Code", "Defectiveproject": "Defective project", "Spec": "Spec", "Parameter": "Parameter", "value": "value", "Safety": "Limit", "ProdictAttributes": "ProdictAttributes", "Instructions": "Instructions", "Logsheet": "Logsheet", "operate": "OPerate", "Machine": "Machine", "PO": "PO", "CheckResult": "input Status", "Frequency": "Frequency", "LastEntry": "Last Entry"}, "PRO_POManagement": {"Segment": "Segment", "CipDetail": "CIP", "SapDate": "SAP Order Date", "BoilingStatus": "BoilingStatus", "Grille": "Grille", "IsHavePreservative": "IsHavePreservative", "BatchCode": "BatchCode", "Sequence": "Sequence", "Formula": "Formula", "ProcessOrder": "Process Order", "Resource": "Resource", "Material": "Material", "Instruction": "Instruction", "TargetQuantity": "Target Quantity", "LineNominalSpeed": "Line Nominal Speed", "ScheduledStart": "Scheduled Start", "Quantity": "Quantity", "Execution": "Execution", "operate": "Operate", "start": "start", "end": "end", "SAP": "SAP", "NominalSpeed": "Nominal Speed", "User": "Operator", "Comment": "Comment", "FillLineCode": "FillLine"}, "PRO_Consume": {"MaterialLotNo": "Material LotNo", "Formula": "Formula", "Operation": "Operation", "Type": "Type", "Batch": "<PERSON><PERSON>", "BatchStatus": "Batch Status", "SSCCStatus": "SSCC Status", "Expiration": "Expiration", "Loaction": "Loaction", "Select": "Select", "Material": "Material", "Quantity": "Quantity", "Available": "Available", "StorageBin": "Storage Bin", "Date": "Date", "BatchCode": "Batch Code", "Shift": "Shift", "SSCC": "TraceCode", "Destination": "Destination", "Source": "Source", "User": "User", "Comment": "Comment", "operate": ""}, "PRO_Tipping": {"SortOrder": "Tipping Sort", "Material": "Material", "Description": "Description", "Required": "Required", "Staged": "Staged", "IsOver": "IsOver", "Consumed": "Consumed", "operate": ""}, "PRO_ParameterDownload": {"Step": "Step", "Equipment": "Equipment", "Parameter": "Parameter", "Target": "Target"}, "PRO_Tippingscan": {"SSCCStatus": "SSCC Status", "SSCC/Container": "SSCC", "Material": "Material", "Group": "Group(s)", "BatchStatus": "Batch Status", "Batch": "<PERSON><PERSON>", "Quantity": "Quantity", "Location": "Location", "Expiration": "Expiration"}, "PRO_Performance": {"CrewSize": "Crew Size", "Formula": "Formula", "Line": "Line", "detail": "Detail", "Category": "Category", "Machine": "Machine", "SubCategory": "Sub Category", "Reason": "Reason", "Code": "Code", "ProcessOrder": "Process Order", "StartTime": "Start Time", "EndTime": "End Time", "Duration": "Duration", "Comment": "Comment", "Updated": "Updated", "User": "User", "SourceType": "Source Type", "Group": "Group", "AMM": "AMM Work Order", "PO": "PO"}, "PRO_POLIST": {"Landz": "Landz", "Ltext1": "BOM Text", "SapDate": "SAP Order Date", "BoilingStatus": "BoilingStatus", "Bezei": "Bezei", "Grille": "Grille", "IsHavePreservative": "IsHavePreservative", "Sequence": "Sequence", "SegmentCode": "SegmentCode", "WaitingforQA": "WaitingforQA", "QApassed": "QApassed", "QaStatus": "QaStatus", "Formula": "Formula", "ProcessOrder": "Process Order", "Source": "Source", "detail": "Detail", "ShiftName": "Shift Name", "GuideBook": "Guide Book", "Batches": "Batches", "Status": "Status", "Execute": "Execute", "Material": "Material", "MaterialCode": "Material Code", "productionversion": "Production Version", "PlanQty": "PlanQty", "ActualQty": "ActualQty", "ProduceStatus": "ProduceStatus", "Reason": "Reason", "PlanStartDate": "Plan Start Date", "PlanEndDate": "Plan End Date", "operate": "Operate", "CipDetail": "CIP", "FillLineCode": "FillLine"}, "POLIST_Execute": {"BatchCode": "BatchCode", "Machine": "Machine", "Stage": "Stage", "StageCode": "Stage Identity", "Status": "Status", "TargetNum": "Number of targets", "StartDate": "Start time", "EndDate": "End time", "User": "Operator", "Comment": "Comments"}, "POLIST_Batch": {"operate": "operation", "Batch": "Batch number", "Material": "Material", "TargetNum": "Target", "Status": "Status", "readiness": "Readiness", "delete": "delete"}, "POLIST_Produce": {"Stage": "Stage", "Resource": "Resource", "Material": "Material", "Quantity": "Quantity", "ActualProduce": "ActualProduce", "StorageArea": "Storage Area"}, "POLIST_parameter": {"Equipment": "Equipment", "Recipe": "Recipe", "SectionVersion": "Section[Version]", "ContextType": "Context Type", "Machine": "Machine", "Phase": "Phase", "Group": "Group", "Parameter": "Parameter", "Icon": "Icon", "ShortDescription": "Short Description", "Limits": "Limits", "Target": "Target", "Default": "<PERSON><PERSON><PERSON>", "UOM": "UOM", "Required": "Required", "UpdatedBy": "Updated By", "ContextVersion": "Context Version"}, "POLIST_consume": {"MaterialLotNo": "Material LotNo", "Action": "Action", "Source": "Source", "Stage": "Stage", "Material": "Material", "Quantity": "Quantity", "AdjustPercentQuantity": "AdjustPercentQuantity", "Order": "Sort Order", "StorageArea": "Storage Area", "OriginalPhase": "Original Phase", "Item": "<PERSON><PERSON>", "ActualConsume": "Actual Consume"}, "POLIST_property": {"name": "name", "value": "value"}, "INV_Storage": {"Workcenter": "Work Center", "detail": "Detail", "Material": "Material", "Class": "Class", "BatchStatus": "BatchStatus", "Precheckestatus": "Status", "Batch": "<PERSON><PERSON>", "SSCCStatus": "SSCC Status", "SSCC/Container": "SSCC", "Quantity": "Quantity", "Expiration": "Expiration", "Allergens": "Allergens", "Created": "Created", "UOM": "UOM", "Lot": "Lot", "Result": "Result"}, "SCJX_KPI": {"EvenType": "Even Type", "ConfirmDate": "Confirm Date", "Comment": "Comment", "UnitName": "Unit", "actions": "actions", "detail": "actions", "SpecCode": "Spec Code", "SpecName": "Spec Name", "MaterialBigGroup": "Material BigGroup", "MaterialSmallGroup": "Material SmallGroup", "Saucecategory": "Sauce Category", "Target": "Target", "Name": "Name", "Type": "Type", "Model": "Model", "Year": "Year", "Month": "Month", "Value": "Value", "Unit": "Unit", "Line": "Line", "Spec": "Spec", "SpecGroup": "SpecGroup", "MaterialGroup": "MaterialGroup", "BudgetType": "BudgetType", "BudgetOutput": "BudgetOutput"}, "ExperienceLibrary": {"czfa": "Disposal plan", "cycs": "Number of uses", "SolutionDesc": "Disposal plan", "AdoptionCount": "Number of uses"}, "sortBy": "Sort by"}, "datePicker": {"itemsSelected": "{0} selected", "nextMonthAriaLabel": "Next month", "nextYearAriaLabel": "Next year", "prevMonthAriaLabel": "Previous month", "prevYearAriaLabel": "Previous year"}, "noDataText": "No data available", "carousel": {"prev": "prev", "next": "next", "ariaLabel": {"delimiter": "Carousel slide {0} of {1}"}}, "calendar": {"moreEvents": "Still {0} events"}, "fileInput": {"counter": "{0} files", "counterSize": "{0}files(Total {1})"}, "timePicker": {"am": "AM", "pm": "PM"}, "pagination": {"ariaLabel": {"wrapper": "wrapper", "next": "next", "previous": "previous", "page": "Transfer to {0}", "currentPage": "current page {0}"}}, "rating": {"ariaLabel": {"icon": "Rating {0} of {1}"}}, "SBKB_SBRW": {"Loaction": "Loaction", "EquipmentName": "EquipmentName", "McProject": "McProject", "status": "status", "PersonName": "PersonName"}}, "Inventory": {"Delivery": "Delivery", "NotSend": "Not Send", "BeenSent": "<PERSON>", "WorkShop": "WorkShop", "NotFound": "未找到转换", "bagstags": "该计算结果仅供参考，具体以WMS创建结果为准!", "Formula": "Formula", "Bucketnum": "Bucket <PERSON>", "ClearLine": "Clear Line", "UpDateBatch": "Up<PERSON>ate <PERSON>", "CreateWMS": "Create WMS Label", "ImgOnly": "Only upload Img", "ChooseDestination": "Choose Destination", "Batch": "<PERSON><PERSON>", "MaterialCode": "Material Code", "BatchStatus": "Batch Status", "SSCCStatus": "TraceCode Status", "Location": "Location", "fullbagweight": "FullBagWeight", "bags": "Bags", "SAPLoaction": "SAP Loaction", "Container": "Container", "ProductionDate": "Production Date", "showProcessCode": "show Process Code", "refresh": "refresh", "scrap": "scrap", "Return": "Return", "Transfer": "Transfer", "AddInventory": "Add Inventory", "Remark": "Add Remark", "Class": "Material Class", "Material": "Material", "SSCC": "TraceCode", "Quantity": "Quantity", "expirationdate": "Expiration Date", "validate": "validate", "InventoryDetails": "Inventory Details", "InventoryBlock": "Inventory Block", "InventoryUnBlock": "Inventory UnBlock", "Split": "Split", "Block": "Batch Block", "UnBlock": "<PERSON><PERSON>", "UnBlock2": "UnBlock", "Print": "Print", "Allergens": "Allergens", "unit": "Unit", "Type": "Type", "FullBag": "Full Bag", "Comment": "Comment", "Destination": "Destination", "selectprinter": "Select printer", "Reprintsourcelabel": "Reprint source label", "Lot": "Lot", "offullbags": "# of full bags", "BagSize": "Bag Size", "SplitQuantity": "Split Quantity", "BlockSure": "Are you sure you want to block this inventory?", "UnBlockSure": "Are you sure you want to Unblock this inventory?", "MergeInventoryInto": "<PERSON><PERSON>", "leasttwo": "Select at least two pieces of data!", "ToOver": "Please complete the information!", "PleaseSelectPrinter": "Please select printer!", "ToGetSSCC": "Please Get SSCC", "QuantityOver": "The number of packs cannot be greater than the original quantity", "ReverseOver": "The number of recoil cannot be greater than the original number"}, "ProductionHistory": {"Batchs": "<PERSON><PERSON>", "scan": "<PERSON><PERSON>", "ScanContainerCode": "Scan SSCC", "Machine": "Machine", "Material": "Material", "Batch": "<PERSON><PERSON>", "SSCC": "TraceCode", "ProcessOrder": "Process Order", "Container": "Container", "MaterialClass": "Material Class", "Operator": "Operator", "Destination": "Destination", "Comment": "Comment", "ReasonCode": "Reason Code", "Reason": "Reason", "Line": "Line", "Shift": "Shift", "Quantity": "Quantity", "SSCCOnly": "TraceCode Only", "NonSSCC": "Non TraceCode", "SAPMessageStatus": "SAP Message Status", "ReverseProduce": "Reverse Produce", "Reverse": "Reverse", "ReverseQuantity": "Reverse Quantity"}, "ProductionSummary": {"Productionsbyshift": "Productions by Shift", "Machine": "Machine", "ProcessOrder": "Process Order"}, "ConsumptionHistory": {"IsSauce": "IsSauce", "Machine": "Machine", "Material": "Material", "Batch": "<PERSON><PERSON>", "SSCC": "TraceCode", "ProcessOrder": "Process Order", "Container": "Container", "MaterialClass": "Material Class", "Operator": "Operator", "Destination": "Destination", "Comment": "Comment", "ReasonCode": "Reason Code", "Reason": "Reason", "Line": "Line", "Shift": "Shift", "Quantity": "Quantity", "SSCCOnly": "TraceCode Only", "NonSSCC": "Non TraceCode", "NoBatchId": "Please select the batch index!", "SAPMessageStatus": "SAP Message Status", "ReverseProduce": "Reverse Produce", "ReverseConsum": "Reverse Consum", "Reverse": "Reverse", "ReverseQuantity": "Reverse Quantity", "Source": "Source", "ConsumedQuantity": "Consumed Quantity", "SourceBin": "Source SAP Location"}, "TransferHistory": {"SourceMaterial": "Source Material", "DestinationMaterial": "Destination Material", "SourceBatch": "Source Batch", "DestinationBatch": "Destination Batch", "SourceSSCC": "Source TraceCode", "DestinationSSCC": "Destination TraceCode", "Source": "Source", "SourceBin": "Source SAP Location", "Destination": "Destination", "DestinationBin": "Destination SAP Location", "SAPMessageStatus": "SAP Message Status", "OldProBatch": "OldProBatch", "NewProBatch": "NewProBatch", "Type": "Type"}, "SparePart": {"Code": "Code", "Name": "Name", "PartType": "PartType", "Model": "Model", "LowerBound": "LowerBound", "UpperBound": "UpperBound", "ServiceLife": "ServiceLife", "PurchaseCycle": "PurchaseCycle（天）", "MinPurchaseQty": "MinPurchaseQty", "Unit": "Unit", "Remark": "Remark"}, "PalletList": {"AutoRefreshseconds": "Auto Refresh(30 seconds)", "Destination": "Destination", "Area": "Area", "VerifiedStatus": "Verified Status", "Scantoverify": "Scan to verify", "Verified": "Verified", "Verify": "Verify", "Reprint": "Reprint", "PalletDetails": "<PERSON><PERSON><PERSON>", "Material": "Material", "Type": "Type", "BatchStatus": "<PERSON><PERSON>(Current Status)", "SSCCStautus": "TraceCode(Current Status)", "Quantity": "Quantity", "StorageBin": "Storage SAP", "ExpirationDate": "Expiration Date", "PrintCount": "Print Count", "Block": "Block", "ReverseProduce": "Reverse Produce", "Reverse": "Reverse", "ProcessOrder": "Process Order", "Batch": "<PERSON><PERSON>", "SSCC": "TraceCode", "ReverseQuantity": "Reverse Quantity", "Reason": "Reason", "SelectPrinter": "Select Printer", "Comment": "Comment"}, "BatchPallets": {"BatchPalletId": "Batch Pallet Id", "ProcessOrder": "Process Order", "ShowCompleted": "Show Completed", "Location": "Location", "Status": "Status", "Bin": "SAP Loaction", "Machine": "Machine", "HideEmptyBatchPallets": "Hide Empty Batch Pallets", "Transfer": "Transfer", "Reassign": "Reassign", "Destination": "Destination", "Id": "ID", "PO": "PO", "QuickSearch": "Quick Search", "Batch": "<PERSON><PERSON>", "Contents": "Contents", "TransferHistory": "Transfer History", "NewPoBatch": "New PO/Batch", "ReassignBatchPallet": "Reassign <PERSON>", "TransferBatchPallet": "Transfer Batch Pallet", "ReprintPalletLabel": "Reprint Pallet Label", "ReprintLabel": "Reprint Label", "TransferPallet": "Transfer Pallet", "RemoveBag": "Remove Bag"}, "ContainerManagement": {"ContainerId": "Container Id", "ContainerClasses": "Container Classes", "Statues": "Statues", "Material": "Material", "Location": "Location", "Batch": "<PERSON><PERSON>", "SSCC": "TraceCode", "ProcessOrder": "Process Order", "Transfer": "Transfer", "Containers": "Containers", "ChangeStatus": "Change Status", "Empty": "Empty", "Details": "Details", "Inventory": "Inventory", "History": "History", "Name": "Name", "Destination": "Destination", "Groups": "Groups", "LastMaterial": "Last Material", "Class": "Class", "Comment": "Comment", "Status": "Status", "CycleCount": "Cycle Count", "TareWeight": "<PERSON><PERSON>", "MaxWeight": "Max Weight", "ContentWeight": "Content Weight", "BinSLOC": "SAP Loaction", "Container": "Container", "CurrentStatus": "Current Status", "CurrentWeight": "Current Weight", "NewStatus": "New Status", "TransferContainer": "Transfer Container", "AddInventory": "Add Inventory", "RemoveInventory": "Scrap Inventory", "Delete": "Delete"}, "MaterialPreparation": {"NoSame": "The selected data is inconsistent Please reselect!", "QuickSearch": "Quick Search", "Over": "the InventQty cannot be greater than the BagSize!", "Lot": "Please enter batch number", "Line": "Line", "ShiftName": "Shift Name", "ProductFamily": "Product Family", "MaterialCode": "Material Code", "Room": "Room", "CheckRoom": "Please Selected Room"}, "Repeatedweighing": {"Target": "Target", "Default": "<PERSON><PERSON><PERSON>", "Weight": "Weight", "Defaultconfirm": "<PERSON><PERSON><PERSON> Confirm", "RepeatedInventory": "Repeated Inventory", "result": "Repeated Inventory Result", "tip": "Scan or choose SSCC.Select transfer typr,Input #bags,Bag weight,then click'transfer'", "Difference": "Difference", "Diff": "<PERSON><PERSON>", "ProductionOrderNo": "ProductionOrderNo", "BatchCode": "Batch Code", "confirm": "Confirm"}, "MaterialPreparationBuild": {"PLCJTP": "C<PERSON> <PERSON><PERSON><PERSON>", "NoTransfer": "This state cannot be transferred", "NoMaterial": "There is no next material", "OpenKeyDown": "Open KeyDown", "CloseKeyDown": "Close KeyDown", "Addsmalllabels": "Add Small Labels", "PartialBagMerge": "<PERSON><PERSON>", "POTransfer": "Transfer", "zts": "Total Pallets", "dqgxh": "Current cylinder number", "ts": "pallets", "jsgs": "Water Remarks", "xzdyj": "Select a Printer", "FormulaNo": "Formula No", "OverExpirationDate": "Over ExpirationDate!", "InQuantityNotEnough": "Select Inventory Shortage!", "QtyOverMax": "Quantity exceeds the maximum value!", "BatchPalletsEmpty": "Please Add BatchPallets First!", "OpenPallet": "OpenPallet", "Operate": "Operate", "Addpallets": "<PERSON><PERSON>", "ReprintBtn": "Reprint", "Reprint": "<PERSON><PERSON><PERSON>", "Deletepallets": "Delete Pa<PERSON>ts", "FullBags": "Full Bags", "RemoveBags": "Remove Bags", "Currentmaterialonly": "Current material only", "CompletePallet": "Complete Pallet", "BatchPallets": "<PERSON><PERSON>", "ChooseScale": "Choose Scale", "BagWeight": "Bag Weight", "FullBag": "Full Bag", "Bags": "# Bags", "Selectthematerialtobeprepared": "Select the material to be prepared", "ByBatch": "By Batch", "ByMaterial": "By Material", "SelectMaterial": "Select Material", "SelectBatch": "Select Batch", "Transfer": "Transfer", "Merge": "<PERSON><PERSON>", "Remaining": "Remaining", "scale": "scale", "OverRide": "OverRide", "printer": "Printer", "PartialBag": "Partial Bag", "PartialBags": "Partial Bags", "FullAmount": "Full Amount", "PO": "PO", "Batch": "<PERSON><PERSON>", "Partial": "Partial", "TotalRequired": "Total Required", "TotalCompleted": "Total Completed", "Theoretical": "Theoretical", "MaterialSplit": "Material Split", "SplitQuantity": "Split Quantity", "Split": "Split", "ShowSplit": "Show Split", "HideSplit": "Hide Split", "SelectedMaterial": "Selected Material", "AvailableQuantity": "Available Quantity", "BatchNumber": "Batch Number", "SplitConditionNotMet": "Split condition not met", "SplitSuccess": "Split successful", "SplitFailed": "Split failed", "AvallableInventory": "Avallable Inventory", "Min": "Min", "Actual": "Actual", "Target": "Target", "Max": "Max", "POSelection": "PO Selection", "Previous": "Previous", "MaterialTransfer": "Material Transfer", "POInventory": "PO Inventory", "NextMaterial": "Next Material", "AddPallet": "AddPallet", "IngredientSelection": "Ingredient Selection", "tiptitle": "Scan or select an TraceCode. Select transfer type, enter #f bags, bag weight, then click the 'Transfer' button.", "BatchSelection": "Batch Selection", "Selectthebatchesingredienttobeprepared": "Select the batches ingredient to be prepared", "Selectthebatchestobeprepared": "Select the batches to be prepared", "Hidecompleted": "Hide completed", "Ingredients": "Ingredients", "buildpallets": "Build Pallets"}, "PAGINATION": {"TOTAL_CN": "Total CN", "TOTAL": "Items", "PER_PAGE": "per page", "GO_TO": "Go to:", "OF": "of", "PAGE": "Page", "MYPAGE": "per/Page"}, "Overview": {"CGBM": "Storage Tank", "GC": "<PERSON><PERSON><PERSON>", "SampleWeighing": "SampleWeighing", "POList": "POList", "Sequence": "Sequence", "TextGood": "The text Comparison passed", "IsUpdateLtxt": "The long text of the work order process is inconsistent with the latest formula long text.", "Comments": "Comments", "ProcessText": "ProcessText", "QrCode": "QrCode", "SendColos": "Send Colos", "ParameterDownload": "ParameterDownload", "BatchCodeLong": "BatchCode need to be less than or equal to ten digits", "Logsheets": "Logsheets", "Material": "Material", "CommentsOption": "Comments Option", "Overview": "Overview", "Performance": "Performance", "PerformanceEvents": "Performance Events", "Back": "Back", "LotCode": "LotCode", "Tipping": "Tipping", "Storage": "Storage", "POManagement": "PO Management", "Produce": "Produce", "Consume": "Consume", "ActiveOrders": "Active Orders", "AvailableOrders": "Available Orders", "History": "History", "QuickSearch": "Quick Search", "status": "Status", "start": "start", "Batch": "<PERSON><PERSON>", "NoActiveProcessOrder": "No Active Process Order", "ChooseEquipment": "Choose Equipment", "StartOrder": "Start Order", "StartTime": "Start Time", "BatchCode": "Batch Code", "ProductionDate": "Batch Date", "ExpirationDate": "Expiration Date", "TargetQuantity": "Target Quantity", "CrewSize": "Crew Size", "bottleneck": "bottleneck", "Start": "Start", "Cancel": "Cancel", "Note1": "Note: This machine is already running the selected order .", "Note2": " .The order will be automatically stopped if you continue.", "Stop": "Stop", "Hold": "Hold", "Resume": "Resume", "UpdateOrder": "Update Order", "AutoReport": "Consume Report", "UpdateRemark": "Update Remark", "NextBatch": "Next Batch", "EndTime": "End Time", "CompletePO": "Complete PO", "StopNote": "Are you sure you would like to stop the selected executions?", "HoldNote": "Are you sure you would like to hold the selected executions?", "UpdateNote": "Update running order ", "DefaultBatchCode": "Default Batch Code "}, "Consume": {"Scan": "<PERSON><PERSON>", "Select": "Select", "Search": "Search", "Source": "Source", "activation": "Activation", "NotActive": "not active", "Consume": "Consume", "Required": "Required", "Consumptions": "Consumptions", "Remaining": "Remaining", "Uom": "U<PERSON>", "Transfer": "Transfer", "SSCC": "TraceCode", "ProcessOrder": "Process Order", "Location": "Location", "Material": "Material", "StorageBin": "Storage Bin", "Over2": "The consumption quantity cannot be greater than the remaining quantity!", "Over": "The consumption quantity cannot be greater than the inventory quantity!", "Produced": "Produced", "Lot": "Lot", "Quantity": "Quantity", "Back": "Back", "Produce": "Produce", "ProductionDate": "Production Date", "ExpirationDate": "Expiration Date", "Batch": "<PERSON><PERSON>", "printlabel": "Print Label", "selectprinter": "select printer"}, "Processlongtext": {"Release": "Release"}, "POList": {"Calculate": "Calculate", "Reopen": "Reopen PO", "NotFoundPO": "NotFound PO", "SourceStroage": "Source", "ThroatOutput": "Throat Output", "WCSUpDate": "WCS UpDate", "SegmentCode": "SegmentCode", "PleaseSelect": "PleaseSelect", "WaitingforQA": "WaitingforQA", "QApassed": "QApassed", "QaStatus": "QaStatus", "Inspection": "Inspection", "Quantity": "Quantity", "ActualQty": "ActualQty", "ProduceStatus": "ProduceStatus", "NotComplete": "NotComplete", "OverComplete": "OverComplete", "CompleteAtOnce": "CompleteAtOnce", "Reason": "Reason", "AddMaterialPreShift": "Add Material PreShift", "MaterialPreShift": "Material PreShift", "CHECKMaterialPreShift": "Check Material PreShift", "Material": "Material", "Source": "Source", "Processlongtext": "Process long text", "ProcessOrder": "ProcessOrder", "Available": "Available", "Property": "attribute", "EditTitle": "Production Ticket Modification", "Edit": "Modify", "ProductionOrderNo": "Works", "MaterialVersion": "Product Version", "PlanStartTime": "Plan Start Time", "PlanEndTime": "Plan End Time", "Speed": "rated speed", "constructingbatches": "Constructing Batches", "BindPoRecipe": "BindPoRecipe", "KGHOUR": "KG/HOUR", "Status": "Status", "Num": "Quantity", "Release": "Release", "Go": "<PERSON><PERSON><PERSON>", "Complete": "Complete the ticket", "RevokeRelease": "Withdraw Release", "Constructingbatches": "Re-parse build batches", "Execute": "Execute", "batch": "batch", "consume": "consumption", "formula": "formula", "produce": "production", "warningText": "The long text of the process is inconsistent with the long text of the latest formula. Please confirm the long text and process parameters.", "parameter": "FormList", "property": "property"}, "POListBatch": {"AddBatch": "<PERSON><PERSON>", "EditBatch": "<PERSON>", "stage": "Stage", "BatchNum": "Batch Number", "BatchSize": "BatchSize", "MinimumSize": "Minimum Size", "MaximumSize": "Maximum Size", "CopyBatch": "Are you sure you want to copy this batch?", "DeleteBatch": "Are you sure you want to cancel this batch?", "ChangeBatch": "Are you sure you want to change preparation status tO PENDING?"}, "POListparameter": {"Recipe": "Recipe", "Dom": "Recipe Part", "ShowContextDetails": "Show Context Details", "UpdateFromEffective": "Update From Effective"}, "ParameterDownload": {"DomTable": "DomTable", "ParameterTable": "ParameterTable", "Parameter": "Parameter", "Target": "Target"}, "Tippingscan": {"MaterielNumber": "Materiel Number", "BarCode": "BarCode", "NotSelect": "Not Selected", "Tipping": "Tipping", "Dock": "Dock", "Destinationselect": "Select Destination", "PZSdockacontainter": "Please dock a container", "DockedContainerInformation": "Docked Container Information", "Destination": "Destination", "AvailableInventory": "Available Inventory"}, "POListconsume": {"Expandall": "Expand all", "Collapseall": "Collapse all", "showempty": "Show Empty"}, "POSampleWeighing": {"Scale": "Scale", "SampleNumber": "Sample Number", "SampleWeighing": "<PERSON><PERSON> Weighing"}, "POListTipping": {"Tipping": "Tipping", "TippingOver": "TippingOver", "ForcedCompletion": "ForcedCompletion", "StartTipping": "Start Tipping", "Scan": "<PERSON><PERSON>", "TippingText": "Click the Start Tipping button to begin tipping"}, "POLogsheets": {"QaConfirm": "QaConfirm", "tgjg": "Please select the result!", "OverLimit": "Exceeding the limit!", "NeedtoComments": "Please fill in the comments!", "Reopen": "ReOpen", "UpdatedAt": "Updated At", "LogsheetImages": "Logsheet Images", "EntryList": "Entry List", "IncludePhoto": "Include Photo", "Comment": "Comment", "Complete": "Complete", "CompleteClose": "Complete & Close", "Date": "Date", "Status": "Status", "More": "More", "Void": "Void", "SaveComment": "SaveComment", "Dashboard": "Dashboard", "GotoLatest": "<PERSON><PERSON>st", "Entry": "Entry", "NewEntry": "New Entry", "ExternalUrl": "External Url"}, "QualityResult": {"Status": "Logsheet status", "Group": "Group", "ProcessOrder": "Process Order", "Material": "Material", "Batch": "<PERSON><PERSON>", "InpectionLotRelated/Unrelated": "Inpection Lot Related/Unrelated", "NoGrouping": "No Grouping", "TLApprove": "TL Approve", "QAApprove": "QA Approve", "ShowEntrieswithoutValues": "Show Entries without Values", "ShowOnlyFailedEntries": "Show Only Failed Entries", "ShowEntriesPendingVerificationOnly": "Show Entries Pending Verification Only"}, "POPerformance": {"Event": "Event List", "MonthlySettlementReport": "Monthly Settlement Report", "Shift": "Shift", "Category": "Reason Category", "EquipmentStatus": "Equipment Status", "POStatus": "PO Status", "ByReason": "By Reason", "ByReckassification": "By Reckassification", "Running": "Running", "Overtip": "Creating this event will modify or overwrite the following events. Please confirm that you wish to proceed.", "OverwritingEvents": "Overwriting Events", "PZSSelectedreason": "Please Selected reason", "Selectedreason": "Selected reason", "Comments": "Comments", "StartTime": "Start Time", "Duration": "Duration", "EndTime": "End Time", "ActualLabourMinutes": "Actual Labour Minutes", "SelectCode": "Please select a reason code", "AllEvent": "All Event", "MergeEvent": "Merge Event", "SplitEvent": "Split Event", "SelectOne": "Please Select the master event", "Line": "Line", "Machine": "Machine", "New": "New", "CrewSize": "Crew Size", "Reclassify": "Reclassify", "Delete": "Delete", "NewEvent": "New Event", "EditEvent": "Edit Event", "Merge": "<PERSON><PERSON>", "EventCollection": "Event Collection", "ON": "ON", "Off": "Off", "EventHistory": "Event History", "HistoryofEventChanges": "History of Event Changes", "MergeTitle": "Select the master event to merge into"}, "precheck": {"groupBy": "Group By", "BatchPallet": "<PERSON><PERSON>", "now": "Now", "TraceCode": "TraceCode", "all": "All", "redistribution": "Redistribution", "bag": "bag", "scan": "<PERSON><PERSON>", "PrecheckOver": "Precheck Over", "Precheck": "Precheck", "beginPrecheck": "Start Precheck", "orderBatch": "<PERSON><PERSON> By"}, "Storage": {"Receipt": "Receipt", "Sugarpretreatment": "Sugar Pre-treatment", "Scan": "<PERSON><PERSON>", "Receiving": "Recive", "empty": "SSCC can't be empty", "noSscc": "No SSCC"}, "Formula": {"title": {"Production_scheduling": "Production Scheduling", "CIPCleaning": "CIP Cleaning", "CipSwitch": "CIP Switch", "BoilingTankCapacity": "Boiling Tank Capacity", "RecommendedFormulaCylinderCapacity": "Recommended Formula Cylinder Capacity", "BOMInjection": "BOMInjection", "PlannedWork": "Planned Work", "ResourceDefinition": "Resource Definition", "SectionProcessDefinition": "Section Process Definition", "ProcessAndEquipmentAssociation": "Process And Equipment Association", "ProductBOMDefinition": "Product BOM Definition", "ProductBOMMaterialDetails": "Product BOM Material Details", "CylinderWeightMaintenance": "Cylinder Weight Maintenance"}, "Cylinder_Weight_Maintenance": {"LineName": "Line Name", "MatName": "Material Name", "SapFormula": "SapF<PERSON><PERSON>", "MtrCode": "MtrCode", "ContainerCode": "ContainerCode", "ContainerName": "size", "Weight": "Boiling Weight", "TargetlineName": "Target Line", "Targetweight": "Target Weight"}, "Production_scheduling": {}, "CIP_Ceaning": {"LineName": "Line Name", "Switchname": "Switch Name", "Switchtime": "Switch time"}, "Boiling_Tank_Capacity": {}, "Recommended_Formula_Cylinder_Capacity": {"LineName": "Line Name", "MatName": "Material Name", "ContainerName": "Container Name ", "Recommandsize": "Recommand size ", "Uom": "UOM"}, "Cip_Switch": {"PrematName": "Pre Material Name", "PrematCode": "Pre Formula Code", "PreMaterialCode": "Pre Material Code", "PostmatName": "Post Material Name", "PostmatCode": "Post Formula Code", "PostMaterialCode": "Post Material Code", "Switchname": "Switch Name"}, "Throataddition": {"MatName": "Material Name", "MatCode": "MatCode", "MatAddableName": "Material Addable Name", "SapFormula": "SapF<PERSON><PERSON>", "Rate": "Rate"}, "Cooking_Loss": {"LineName": "Line Name", "MatCode": "Formula Code", "MtrCode": "Material Code", "MatName": "Material Name", "TankCapacity": "Tank Capacity(t)", "TankwallLose": "<PERSON>(kg)", "PipeLose": "<PERSON><PERSON>(kg) ", "ReservoirLose": "Reservoir Lose(kg) ", "PertankLose": "<PERSON>(kg)"}, "Formula_Loss": {"LineName": "Line Name", "MatName": "Material Name", "TankWeight": "Tank Weight", "Tankquantity": "Tank Quantity", "EstimateLoss": "Estimate Loss ", "Productdate": "Product Date "}, "BOM_Injection": {"MaterialGroup": "Material Group", "MaterialCode": "Material Code", "MaterialName": "Material Name", "MaterialVersionNumber": "Material Version Number", "ProductSapSegmentName": "Product SAP Segment Name", "ConsumeSapSegmentName": "Consume SAP Segment Name", "ProductMaterialName": "Product Material Name", "ProductMaterialUnitId": "Product Material Unit Id", "PercentQuantity": "Percent Quantity", "Priority": "Priority", "ConsumeQuantity": "Consume Quantity"}, "Resource_Definition": {"Code": "Code", "Name": "Name"}, "Resouce_Operation": {"SegmentName": "Segment Name", "Remark": "Remark"}, "Resouce_Phase": {"SegmentName": " Segment Name", "Remark": "Remark"}, "Resouce_Phase_Sales": {"SalesContainer": "Sales Container", "Description": "Description"}, "Resouce_Phase_Machine": {"SapSegmentName": "SAP Segment Name", "EquipmentName": "Equipment Name"}, "Section_Process_Definition": {"ID": "ID", "SapEquipmentId": "SAP Equipment Id", "SegmentCode": "Segment Code", "SegmentName": "Segment Name", "Remark": "Remark", "CreateDate": "Create Date"}, "Process_And_Equipment_Association": {"EquipmentId": "Equipment Id", "SapSegmentId": "SAP Segment Id", "CreateDate": "Creat Date"}, "Product_BOM_Definition": {"Material": "Material", "Operation": "Operation", "Phase": "Phase", "Unit": "Unit", "NeedPreprocess": "Need Preprocess", "_XZTLK": "Please Select Feed Port", "IsSubcontract": "Is Need Subcontract", "FeedPort": "Feed Port"}, "Product_BOM_Material_Details": {"SortOrder": "Sort Order", "Material": "Material", "Unit": "Unit", "Quantity": "Quantity", "ParentQuantity": "Parent Quantity", "LossQuantity": "Loss Quantity", "AdjustPercentQuantity": "Adjust Percen Quantity", "IsSubcontract": "Is Need Subcontract", "FeedPort": "Feed Port", "CreateDate": "Creat Date"}, "Preprocess_Text": {"TextVersion": "Text Version", "ProcessData": "Process Data", "Status": "Status", "CreateUserId": "Create UserId", "CreateDate": "Create Date", "Reviewuserid": "Review Userid", "Reviewtime": "Review Time"}, "Plant_Reason_Codes": {"Description": "Description", "type": "type", "Status": "Status"}, "Line_Configuration": {"LineName": "Line", "Machines": "Machines", "LineCode": "LineCode"}, "Line_Configuration_Expand": {"EquipmentName": "Machine", "ManualData": "Manual Codes", "AutoCount": "Auto Codes"}, "Auto_Matic": {"Reason Description": "Reason Description", "PlcCode": "PLC Code", "PlcCodeDescription": "PLC Code Description", "Category": "Category", "Reason Groups": "Reason Groups", "ModifyDate": "Last Update", "ModifyUserId": "User"}, "Manual": {"ID": "Description", "Group": "Group", "Category": "Category", "Status": "Status", "Quick Link": "Quick Link"}, "Work_Hour_Report": {"ProductionOrderNo": "ProductionOrderNo", "SegmentName": "SegmentName", "Resource": "LineCode", "Formula": "Formula", "Description": "Description", "GoodCount": "GoodCount", "Unit1": "Actual Unit", "WasteCount": "WasteCount", "Unit2": "Scattered Unit", "Code": "Code", "PlanQty": "PlanQty", "PoStatus": "PoStatus", "Status": "Send status", "TotalCount": "Total Count", "CovGoodCount": "CovGood Count", "EquipmentCodes": "Equipment", "Msg": "sapMsg", "CreateDate": "CreateDate"}, "Work_Hour_Report_Cook": {"ProductionOrderNo": "ProductionOrderNo", "Formula": "Formula", "Sequence": "Sequence", "MaterialCode": "Production Code", "MaterialDescription": "Production Name", "LineCode": "Line", "PlanQty": "PlanQty", "PoStatus": "PoStatus", "Status": "Send status", "Unit": "Unit", "CreateDate": "CreateDate"}, "Material_Mapping": {"ID": "ID", "Description": "Description", "MappingType": "Type", "Type": "Include/Exclude"}, "Capacity": {"Material": "Material", "MaxQuantity": "Max Capacity", "ModifyUserId": "Update By", "ModifyDate": "Update Date"}, "Auxiliary_Working_Hours": {"EquipmentName": "EquipmentName", "GroupName": "GroupName", "ReasonName": "ReasonName", "StartTime": "StartTime", "EndTime": "EndTime", "Duration": "Duration", "CrewSize": "CrewSize", "ActualLaborMinutes": "ActualLaborMinutes", "Remark": "Remark"}, "LineRelation": {"PrelineCode": "CookLineCode", "PrelineName": "CookLine", "LineCode": "FillLineCode", "LineName": "FillLine"}, "ChangeModel": {"LineCode": "LineCode", "LineName": "LineName", "Presalescontainer": "Presalescontainer", "PresalescontainerDescription": "PresalescontainerDescription", "Salescontainer": "Salescontainer", "SalescontainerDescription": "SalescontainerDescription", "Changetime": "Changetime"}, "ProductSpeed": {"Wccode": "Work Center Code", "MatName": "Mat Name", "MatCode": "MatCode", "SalescontainerCode": "Sales container Code", "Schedulespeed": "Schedule speed", "Oeespeed": "Oee speed", "Uom": "Unit"}, "OilFormula": {"FormulaMaterialCode": "FormulaMaterialCode", "FormulaMaterialName": "FormulaMaterialName", "OilMaterialCode": "OilMaterialCode", "OilMaterialName": "OilMaterialName", "Rate": "Rate", "Remark": "Remark"}}, "PlanWork": {"title": {"PlannedWork": "Planned Work"}, "button": {"PackagingWorkOrder": "Packaging WorkOrder", "FormulaMerge": "Formula Merge", "FormulaSplit": "Formula Split", "ModifyBOM": "Modify PV", "GeneratePlanOrder": "Generate Plan Order", "UploadSAP": "Upload SAP", "QueryBOM": "QueryBOM", "Query": "Query", "CalculateTheDemandForPlannedWorkOrders": "Calculate The Demand For Planned Work Orders"}, "Packaging_Work_Order": {"a": "Maximum Feed Weight", "a1": "Receipe", "a2": "Product Order Number", "a3": "work center", "a4": "Product code", "a5": "MRP", "a6": "Product Name", "a7": "Semi-Finished Product Number", "a8": "Batch Number", "a9": "Product Quantity", "a10": "Unit", "a11": "Actual Product Number", "a12": "Sales Order", "a13": "Date of Departure", "a14": "Remark"}, "Formula_Merge": {"SapOrderNo": "SapOrderNo", "SapStatus": "SAP Status", "ProduceDate": "Date", "MesOrderNo": "Plan Order Number", "LineName": "Product Line", "SapContainer": "Size", "MatName": "Recipe Name", "SapFormula": "Recipe", "MatCode": "Material Code", "ProdVersionText": "BOM Version", "Planweight": "Planweight", "Scheduleweight": "Scheduleweight", "Lossweight": "Lossweight", "Inlightweight": "Inlightweight", "Throatweight": "Throatweight", "Adjustweight": "Adjustweight"}, "Edit_BOM_Version": {"Matnr": "Material Number", "Verid": "Version Id", "Text1": "Description"}, "Formula_Split": {"a": "SAP Status", "a1": "Date", "a2": "Plan Order Number", "a3": "Product Line", "a4": "Size", "a5": "Recipe Name", "a6": "Recipe", "a7": "Material Code", "a8": "BOM Version", "a9": "Scedule Weight"}, "Modify_BOM": {"a": "Material Code", "a1": "Material Name", "a2": "Carry Weight", "a3": "Cook Quantity", "a4": "Remark"}, "Generate_Plan_Order": {"a": "Date", "a2": "Plan Order Name", "a3": "Produce Line", "a4": "size", "a5": "Recipe Name", "a6": "Recipe", "a7": "Material Code", "a8": "BOM Version", "a9": "Scedule Weight", "a10": "Loss", "a11": "Throat", "a12": "Adjust", "a13": "Single Weight", "a14": "Schedule Weight", "a15": "Tank Quantity", "a16": "Per Tank Quantity", "a17": "Total", "a18": "Reamrk"}, "OrderThroat": {"OrderCode": "MES工单号", "MatName": "喉头名称", "SapFormula": "喉头配方", "ThroatCode": "喉头物料编号", "Throatformorder": "批号", "SSCC": "追溯码", "Inweight": "投入量", "Rate": "添加比例", "StandardRate": "标准最大比例"}}, "ProductionWork": {"title": {"ProductionWork": "Production Work"}, "button": {"GenerateLargeTable": "Generate Large Table", "PreliminaryProdSchedule": "Pre Liminary Production Schedule", "LossSelection": "Loss Selection", "GenerateProdTable": "Generate Production Table", "EditProdTable": "Edit Production Table", "GenerateWorkOrder": "Generate Work Order", "EditWorkOrder": "Edit Work Order", "RecipeSort": "Recipe Sort", "OrderSearch": "Order Search", "PublicWorkOrder": "Public Work Order", "RobotMotion": "Robot Motion", "CreateWorkOrder": "Create WorkOrder", "QueryBom": "Query BOM", "SAPUpload": "上传SAP", "CancelWorkOrder": "Cancel Work Order", "FormulaThroatDetail": "Recipe Throat Detail"}, "Material_Info": {"ProduceDate": "Date", "LineName": "Line", "SapContainer": "size", "SapFormula": "FormulaCode", "CipMethod": "CipMethod", "MatCode": "Recipe", "MatName": "Recipe Name", "ProdVersionText": "ProdVersion", "Planweight": "Plan Weight", "Scheduleweight": "Scedule Weight", "TankquantityText": "Tankquantity", "TankweightText": "Tankweight", "Lossweight": "Loss", "Inlightweight": "Inlightweight", "Throatweight": "Throat", "Adjustweight": "Adjust"}, "Work_Order_Info": {"ProductionOrderNo": "SAP No", "MesOrderCode": "MES No", "PoStatus": "Work Order Status", "OrderType": "OrderType", "FillLine": "FillLine", "PlanQty": "Standard Batch Quantity", "ThroatDetail": "Throat", "CipTypes": "CIP", "MatName": "Recipe Name", "MatCode": "Recipe", "date": "Produce Date", "LineCode": "Line", "PlanStartTime": "Plan Start Time", "PlanEndTime": "Plan End Time", "SegmentCode": "Work Center"}, "Generate_Large_Table": {"a": "Date", "a1": "Line", "a2": "size", "a3": "Recipe Name", "a4": "Recipe", "a5": "Material Code", "a6": "Scedule Weight", "a7": "Loss", "a8": "Throat", "a9": "Adjust", "a10": "Single Weight", "a11": "Schedule Weight", "a12": "Tank Quantity", "a13": "Per Tank Quantity", "a14": "Total", "a15": "Reamrk"}, "Preliminary_Prod_Schedule": {"a": "Date", "a1": "Line", "a2": "size", "a3": "Recipe Name", "a4": "Recipe", "a5": "Material Code", "a6": "Scedule Weight", "a7": "Loss", "a8": "Throat", "a9": "Adjust", "a10": "Single Weight", "a11": "Schedule Weight", "a12": "Tank Quantity", "a13": "Per Tank Quantity", "a14": "Total", "a15": "Reamrk"}, "Loss_Selection": {"a": "Date", "a1": "Line", "a2": "size", "a5": "Recipe Name", "a6": "Recipe", "a7": "Material Code", "a8": "Scedule Weight"}, "Loss_Selection_Table": {"a9": "Loss Selection"}, "Generate_Prod_Table": {"a": "Date", "a1": "Line", "a2": "size", "a3": "Recipe Name", "a4": "Recipe", "a5": "Material Code", "a6": "Scedule Weight", "a7": "Loss", "a8": "Throat", "a9": "Adjust", "a10": "Single Weight", "a11": "Schedule Weight", "a12": "Tank Quantity", "a13": "Per Tank Quantity", "a14": "Total", "a15": "Reamrk"}, "Edit_Prod_Table": {"a": "Recipe Number", "a2": "Loss", "a12": "Adjust", "a13": "Single Weight", "a14": "Schedule Weight", "a15": "Tank Quantity", "a16": "Per Tank Weight", "a17": "BOM Version"}, "Generate_Work_Order": {"a": "Work Order Number", "a2": "Work Order Status", "a12": "Standard Batch Quantity", "a13": "Actual Batch Quantity", "a14": "Recipe Name", "a15": "Recipe", "a16": "Produce Date", "a17": "Plan Start Time"}, "Edit_Work_Order": {"a": "Work Order Number", "a2": "Work Order Status", "a12": "Standard Batch Quantity", "a13": "Actual Batch Quantity", "a14": "Recipe Name", "a15": "Recipe", "a16": "Produce Date", "a17": "Plan Start Time"}, "Cancel_Work_Order": {"a": "Work Order Number", "a2": "Work Order Status", "a12": "Standard Batch Quantity", "a13": "Actual Batch Quantity", "a14": "Recipe Name", "a15": "Recipe", "a16": "Produce Date", "a17": "Plan Start Time"}, "Recipe_Sort": {"date": "Date", "LineCode": "Line Name", "PlanStartTime": "Product Time", "MatName": "Recipe", "TextVersion": "Recipe Version"}, "Order_Search": {"e": "PO Name", "a": "PO Number", "b": "Material Number", "c": "Start Time", "d": "End Time"}, "Public_Work_Order": {"a": "Work Order Number", "a2": "Work Order Status", "a12": "Standard Batch Quantity", "a13": "Actual Batch Quantity", "a14": "Recipe Name", "a15": "Recipe", "a16": "Produce Date", "a17": "Plan Start Time"}, "Robot_Motion": {"dataIndex": "Index", "LineCode": "Line", "Msg": "Notice Information"}, "Create_Work_Order": {"a": "Work Order Number", "a2": "Work Order Status", "a12": "Standard Batch Quantity", "a13": "Actual Batch Quantity", "a14": "Recipe Name", "a15": "Recipe", "a16": "Produce Date", "a17": "Plan Start Time"}, "Formula_Throat_Detail": {"MatName": "Recipe Number", "SapFormula": "FormulaCode", "ThroatCode": "Recipe Code", "Throatformorder": "Lot No", "Inweight": "Planed Used Quantity"}}, "scjxkpi": {"Comment": "Comment", "ConfirmDate": "Confirm Date", "EventType": "Event Type", "MaterialBigGroup": "Material BigGroup", "MaterialSmallGroup": "Material SmallGroup", "Saucecategory": "Sauce Category", "Target": "Target", "ChooseModel": "ChooseModel", "year": "year", "month": "month", "Name": "Name", "Type": "Type", "Value": "Value", "Unit": "Unit", "Line": "Line", "Spec": "Spec", "SpecCode": "Spec Code", "SpecName": "Spec Name", "MaterialGroup": "MaterialGroup", "SpecGroup": "SpecGroup", "BudgetType": "BudgetType", "BudgetOutput": "BudgetOutput", "Importtonnagebudget": "Import tonnage budget", "Importboxquantitybudget": "Import box quantity budget"}, "PackagingWorkOrder": {"title": {"packagingWorkOrder": "Packaging WorkOrder"}, "button": {"PackagingWorkOrder": "Packaging WorkOrder", "PackOrderSort": "Auto Sort", "OrderSort": "Order Sort", "OrderSearch": "Order Search", "PublicWorkOrder": "Public WorkOrder", "EditAttribute": "Edit Attribute", "RobotMotion": "Robot Motion", "rework": "rework"}, "Table_Head": {"SegmentCode": "SegmentCode", "SapStatus": "SapStatus", "ProDate": "PlanDate", "Formula": "Formula", "MatName": "<PERSON><PERSON><PERSON>", "PackOrderNo": "PackOrderNo", "Sequence": "Sequence", "MatCode": "MatCode", "LTEXT6": "LTEXT6", "MAKTX_C_FW": "MAKTX_C_FW", "Slmkt": "Slmkt", "IHRAN": "IHRAN", "SaleContainer": "SaleContainer", "VText": "VText", "PlanQty": "PlanQty", "UnitCode": "UnitCode", "PlanStartTime": "PlanStartTime", "PlanEndTime": "PlanEndTime", "MRP": "MRP", "PoStatus": "PoStatus", "WorkOrderType": "WorkOrderType", "ResponsibleDepartment": "ResponsibleDepartment", "ReworkCategory": "ReworkCategory", "ReasonForRework": "ReasonForRework", "ReworkMethod": "ReasonForRework"}, "Order_Sort": {"date": "Date", "LineCode": "Line Code", "PlanStartTime": "Plan Start Time", "MatName": "Material Name", "PlanEndTime": "Plan End Time"}, "Public_Order": {"a": "Date", "b": "Line Code", "c": "Size", "d": "Material Name", "e": "Material", "f": "Material Code", "g": "Planned Weight", "h": "Loss"}, "work_order_bom": {"ItemDecs": "name", "ItemId": "COde", "ItemQuantityWithLoss": "Quantity with loss", "ItemQuantityWithoutLoss": "No loss quantity", "ItemUnit": "unit"}}, "CommunicationStation": {"title": {"communicationStation": "Communication Station", "type": "type", "code": "code", "name": "name", "url": "URL"}, "Station_Table": {"CODE": "code", "NAME": "name", "TYPE": "Name", "URL": "URL", "REMARKS": "Reamrk"}}, "CollectionPoint": {"title": {"collectionPoint": "Collection Point", "station": "Station", "type": "Type", "isSave": "Is Save"}, "Collection_Point_Table": {"Name": "Name", "Tag": "Tag", "SaveCyc": "Save Cycle(sec)", "IsSave": "Is Save", "Type": "Type", "ServerName": "Server Name", "Describe": "Describe", "Formula": "Formula"}}, "ConsoleGroup": {"title": {}, "table": {"EquipmentNames": "EquipmentNames", "Description": "Description", "ConsoleGroup": "ConsoleGroup", "SortOrder": "SortOrder"}, "EquipmentGroupEquip": {"SortOrder": "Sort", "EquipmentName": "Equipment Name", "Type": "Type", "LineName": "Line Name"}}, "RecipeTable": {"table": {"Step": "Step", "Remark": "Remark", "StillTime": "Still Time", "Order": "Order", "WaterQty": "Water"}}, "Recipe": {"contentTile": "RecipeManagement", "name": "name", "code": "code", "describe": "describe", "activation": "activation", "timeEffect": "timeEffect", "startTime": "startTime", "endTime": "endTime", "opinion": "opinion", "AllowSwitching": "AllowSwitching", "formSubmit": "Save", "close": "close", "recipeName": "recipeName", "ApprovalType": "ApprovalType", "Status": "Status", "Parameter": "Parameter", "type": "type", "dataType": "dataType", "accuracy": "accuracy", "MeasurementUnitId": "MeasurementUnitId", "PointTag": "PointTag", "AutomaticCollection": "AutomaticCollection", "referredToAs": "referredToAs", "QAReview": "QAReview", "GenerateFrequency": "GenerateFrequency", "frequencyValue": "frequencyValue", "remarks": "remarks"}, "sampling": {"WoCode": "WoCode", "MaterialCode": "MaterialCode", "Container": "Container", "Equipment": "Equipment"}, "MaterialLabelTable": {"GetLotNumber": "GetLotNumber"}, "report": {"MonthProductionDetail": {"Year": "Year", "Month": "Month", "LineId": "LineId", "StartTime": "StartTime", "ProductionOrderId": "ProductionOrder", "MaterialId": "MaterialId", "MaterialDecs": "MaterialDecs", "Formula": "Formula", "FormulaType": "FormulaType", "SalesContainer": "SalesContainer", "SalesContainerGroup": "SalesContainerGroup", "MrpDesc": "MrpDesc", "ActualQtyBox": "ActualQtyBox", "ActualQtyBottle": "ActualQtyBottle", "ActualNetWeightKg": "ActualNetWeightKg", "ActualQtyKg": "ActualQtyKg", "ActualQtyTon": "ActualQtyTon", "LaborHour": "LaborHour", "HourType": "HourType", "MachHour": "MachHour", "HourType2": "HourType2", "WorkCenter": "WorkCenter", "MrpCode": "MrpCode", "FormulaGroup": "FormulaGroup", "PlatesNum": "PlatesNum", "BoxPerPlate": "BoxPerPlate"}}, "PackSpeed": {"table": {"WorkCenter": "WorkCenter", "FormulaCode": "FormulaCode", "SalescontainerCode": "SalescontainerCode", "SalescontainerName": "SalescontainerName", "Packingunit": "Packingunit", "PackingunitName": "PackingunitName", "ProductionCategory": "ProductionCategory", "Mrp": "Mrp", "Speed": "Speed", "Remark": "Remark"}}, "WeekSchedule": {"table": {"Workshop": "workshop", "LineCode": "lineCode", "Category": "category", "MaterialCode": "materialCode", "MaterialName": "materialName", "Factory": "factory", "PackSize": "packSize", "DesignCode": "designCode", "Unit": "unit", "Output": "output", "StartWorkday": "startWorkday", "StartShift": "startShift", "FinishWorkday": "finishWorkday", "FinishShift": "finishShift", "PlanQuantity": "planQuantity", "ReworkMaterialCode": "reworkMaterialCode", "ReworkMaterialName": "reworkMaterialName", "ReworkQuantity": "reworkQuantity", "OrderNo": "orderNo", "Type": "type", "Status": "status", "SapOrderNo": "sapOrderNo", "SapStatus": "sapStatus", "SapFeedbackQuantity": "sapFeedbackQuantity", "WorkCenter": "workCenter", "ActualStartWorkday": "actualStartWorkday", "ActualStartTime": "actualStartTime", "ActualStartShift": "actualStartShift", "ActualFinishWorkday": "actualFinishWorkday", "ActualFinishTime": "actualFinishTime", "ActualFinishShift": "actualFinishShift", "ActualQuantity": "actualQuantity", "Remark": "remark"}, "bomDetail": {"SegmentName": "SegmentName", "Sort": "Sort", "MaterialCode": "MaterialCode", "MaterialName": "MaterialName", "MaterialType": "MaterialType", "InsteadMaterialCode": "InsteadMaterialCode", "InsteadMaterialName": "InsteadMaterialName", "Unit": "Unit", "StandardQuantity": "StandardQuantity", "PlanQuantity": "PlanQuantity", "Remark": "Remark"}}, "WeekFormulation": {"table": {"OrderNo": "orderNo", "Type": "type", "LineCode": "lineCode", "Factory": "factory", "MaterialCode": "materialCode", "MaterialName": "materialName", "StartWorkday": "startWorkday", "FinishWorkday": "finishWorkday", "PlanQuantity": "planQuantity", "Status": "status", "SapFeedbackQuantity": "sapFeedbackQuantity"}, "bomDetail": {"SegmentName": "SegmentName", "Sort": "Sort", "MaterialCode": "MaterialCode", "MaterialName": "MaterialName", "MaterialType": "MaterialType", "InsteadMaterialCode": "InsteadMaterialCode", "InsteadMaterialName": "InsteadMaterialName", "Unit": "Unit", "StandardQuantity": "StandardQuantity", "PlanQuantity": "PlanQuantity", "Remark": "Remark"}, "poListDetail": {"Sequence": "Sequence", "ProductionOrderNo": "ProductionOrderNo", "MaterialCode": "MaterialCode", "MaterialName": "MaterialName", "PlanQty": "PlanQty", "PlanDate": "PlanDate", "PrepareShiftid": "Shift", "PlanStartTime": "PlanStartTime", "PlanEndTime": "PlanEndTime", "StartTime": "StartTime", "EndTime": "EndTime", "Type": "Type", "PoStatus": "PoStatus", "ReleaseStatus": "ReleaseStatus", "ProduceStatus": "ProduceStatus", "SegmentCode": "SegmentCode", "LineCode": "LineCode", "BomVersion": "BomVersion", "Remark": "Remark"}}, "BatchDcs": {"table": {"LineName": "LineName", "PoNo": "PoNo", "BatchtNo": "BatchtNo", "MaterialCode": "MaterialCode", "Unit": "Unit", "StandardQuantity": "StandardQuantity", "PlanQuantity": "PlanQuantity", "Status": "Status", "SendData": "SendData", "ResponseData": "ResponseData", "Remark": "Remark", "CreateDate": "createdate", "CreateUserId": "createuserid", "ModifyDate": "modifydate", "ModifyUserId": "modifyuserid"}}, "StandardPeriodLot": {"table": {"LineCode": "lineCode", "MaterialCode": "materialCode", "MaterialName": "materialName", "Type": "type", "FirstLotPeriod": "firstLotPeriod", "MiddleLotPeriod": "middleLotPeriod", "LastLotPeriod": "lastLotPeriod", "PlanQuantity": "planQuantity", "MinLotQuantity": "minLotQuantity", "MaxLotQuantity": "maxLotQuantity", "Remark": "remark", "Createdate": "createdate", "Createuserid": "createuserid", "Modifydate": "modifydate", "Modifyuserid": "modifyuserid"}}, "FormulaTankCapacity": {"table": {"FormulaCode": "FormulaCode", "LineName": "LineName", "MaterialName": "MaterialName", "TankCapacity": "TankCapacity", "MinWeight": "MinWeight", "MaxWeight": "MaxWeight"}}, "CIPInfo": {"table": {"EquipmentCode": "EquipmentCode", "OrderCode": "OrderCode", "CipOrderCode": "CipOrderCode", "CipType": "CipType", "CipStation": "CipStation", "CipStart": "CipStart", "CipEnd": "CipEnd", "CheckResult": "CheckResult", "CheckUserName": "CheckUserName", "CheckTime": "CheckTime", "Remark": "Remark"}}, "EnteringLightweight": {"table": {"MaterialCode": "MaterialCode", "MaterialName": "MaterialName", "SaleContainerCode": "SaleContainerCode", "SaleContainerName": "SaleContainerName", "Weight": "Weight", "Remark": "Remark"}}, "SOP": {"DirName": "Directory Name", "DirCode": "Directory Code (Unique)", "DirOwner": "Directory Owner", "EnterDirName": "Please enter directory name", "EnterDirCode": "Please enter directory code (unique)", "PrimaryKey": "Primary Key", "ParentDirId": "Parent Directory ID", "DirOwnerId": "Directory Owner ID", "CreateTime": "Create Time", "CreateUserId": "Creator ID", "ModifyTime": "Modify Time", "ModifyUserId": "Modifier ID", "Timestamp": "Timestamp", "DeleteFlag": "Delete Flag (0-Not Deleted 1-Deleted)", "ParentDirIdRequired": "Parent Directory ID cannot be empty", "DirNameRequired": "Directory Name cannot be empty", "DirCodeRequired": "Directory Code (Unique) cannot be empty", "DirOwnerRequired": "Directory Owner ID cannot be empty", "DirNameLength": "Length should be 2 to 50 characters", "DirCodeFormat": "Only letters, numbers, underscores and hyphens are allowed", "TargetId": "Target Object ID (Directory ID/Document ID)", "TargetType": "Object Type (1-Directory 2-Document)", "GrantType": "Grant Type (1-User 2-Department)", "GrantId": "Granted Object ID", "SelectTargetId": "Please select Target Object ID (Directory ID/Document ID)", "SelectTargetType": "Please select Object Type (1-Directory 2-Document)", "SelectGrantType": "Please select Grant Type (1-User 2-Department)", "SelectGrantId": "Please select Granted Object ID"}, "SopPermission": {"table": {"TargetId": "Target Object ID", "TargetType": "Object Type", "GrantType": "Grant Type", "GrantId": "Granted Object ID", "PermLevel": "Permission Level", "CreateTime": "Create Time", "CreateUserId": "Creator ID", "ModifyTime": "Modify Time", "ModifyUserId": "Modifier ID"}}}