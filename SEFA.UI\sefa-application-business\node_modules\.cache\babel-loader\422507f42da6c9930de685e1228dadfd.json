{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopAudit.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\SOP\\sopAudit.js", "mtime": 1750218996662}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlsL3JlcXVlc3QnOwppbXBvcnQgeyBjb25maWdVcmwgfSBmcm9tICdAL2NvbmZpZyc7CmNvbnN0IGJhc2VVUkwgPSBjb25maWdVcmxbcHJvY2Vzcy5lbnYuVlVFX0FQUF9TRVJWRV0uYmFzZVVSTF9ERk07Ci8qKgogKiDmlofmoaPlj5jmm7TlrqHorqHooajliIbpobXmn6Xor6IKICogQHBhcmFtIHvmn6Xor6LmnaHku7Z9IGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gZ2V0U29wQXVkaXRMaXN0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGJhc2VVUkwgKyAnL2FwaS9Tb3BBdWRpdC9HZXRQYWdlTGlzdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQovKioKICog5L+d5a2Y5paH5qGj5Y+Y5pu05a6h6K6h6KGoCiAqIEBwYXJhbSBkYXRhCiAqLwoKZXhwb3J0IGZ1bmN0aW9uIHNhdmVTb3BBdWRpdEZvcm0oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYmFzZVVSTCArICcvYXBpL1NvcEF1ZGl0L1NhdmVGb3JtJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YQogIH0pOwp9Ci8qKgogKiDojrflj5bmlofmoaPlj5jmm7TlrqHorqHooajor6bmg4UKICogQHBhcmFtIHtJZH0KICovCgpleHBvcnQgZnVuY3Rpb24gZ2V0U29wQXVkaXREZXRhaWwoaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGJhc2VVUkwgKyAnL2FwaS9Tb3BBdWRpdC9HZXRFbnRpdHknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBpZAogIH0pOwp9Ci8qKgogKiDliKDpmaTmlofmoaPlj5jmm7TlrqHorqHooagKICogQHBhcmFtIHvkuLvplK59IGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gZGVsU29wQXVkaXQoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogYmFzZVVSTCArICcvYXBpL1NvcEF1ZGl0L0RlbGV0ZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "configUrl", "baseURL", "process", "env", "VUE_APP_SERVE", "baseURL_DFM", "getSopAuditList", "data", "url", "method", "saveSopAuditForm", "getSopAuditDetail", "id", "delSopAudit"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/api/SOP/sopAudit.js"], "sourcesContent": ["import request from '@/util/request'\nimport { configUrl } from '@/config'\nconst baseURL = configUrl[process.env.VUE_APP_SERVE].baseURL_DFM\n\n/**\n * 文档变更审计表分页查询\n * @param {查询条件} data\n */\nexport function getSopAuditList(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/GetPageList',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 保存文档变更审计表\n * @param data\n */\nexport function saveSopAuditForm(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/SaveForm',\n        method: 'post',\n        data\n    })\n}\n\n/**\n * 获取文档变更审计表详情\n * @param {Id}\n */\nexport function getSopAuditDetail(id) {\n    return request({\n        url: baseURL + '/api/SopAudit/GetEntity',\n        method: 'post',\n        data: id\n    })\n}\n\n/**\n * 删除文档变更审计表\n * @param {主键} data\n */\nexport function delSopAudit(data) {\n    return request({\n        url: baseURL + '/api/SopAudit/Delete',\n        method: 'post',\n        data\n    })\n}\n\n\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB;AACA,SAASC,SAAT,QAA0B,UAA1B;AACA,MAAMC,OAAO,GAAGD,SAAS,CAACE,OAAO,CAACC,GAAR,CAAYC,aAAb,CAAT,CAAqCC,WAArD;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;EAClC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,2BADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASG,gBAAT,CAA0BH,IAA1B,EAAgC;EACnC,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,wBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASI,iBAAT,CAA2BC,EAA3B,EAA+B;EAClC,OAAOb,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,yBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF,IAAI,EAAEK;EAHK,CAAD,CAAd;AAKH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASC,WAAT,CAAqBN,IAArB,EAA2B;EAC9B,OAAOR,OAAO,CAAC;IACXS,GAAG,EAAEP,OAAO,GAAG,sBADJ;IAEXQ,MAAM,EAAE,MAFG;IAGXF;EAHW,CAAD,CAAd;AAKH"}]}