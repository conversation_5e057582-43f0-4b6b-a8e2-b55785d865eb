<template>
    <div class="root usemystyle">
        <div class="root-layout" v-loading="initLoading">
            <split-pane
                :min-percent="15"
                :max-percent="40"
                :default-percent="20"
                split="vertical">
                <template slot="pane1">
                    <div class="root-left">
                        <div class="tree-toolbar">
                            <el-button-group>
                                <el-button
                                    size="small"
                                    icon="el-icon-refresh"
                                    @click="getDirTree">刷新</el-button>
                                <el-button
                                    size="small"
                                    @click="expandAll">展开</el-button>
                                <el-button
                                    size="small"
                                    @click="collapseAll">收起</el-button>
                            </el-button-group>
                        </div>
                        <el-tree
                            ref="tree"
                            :data="treeData"
                            :props="defaultProps"
                            highlight-current
                            @node-click="handleNodeClick"
                            v-loading="treeLoading">
                            <span class="custom-tree-node" slot-scope="{ node }">
                                <div style="line-height: 22px;">
                                    <div class="tree-title">{{ node.data.name }}</div>
                                </div>
                            </span>
                        </el-tree>
                    </div>
                </template>
                <template slot="pane2">
                    <div class="root-right">
                        <div class="root-head">
                            <div class="selected-dir-info" v-if="selectedDir">
                                <span>当前目录：{{ selectedDir.name }}</span>
                            </div>
                        </div>
                        <div class="root-main">
                            <vxe-table
                                class="mt-3"
                                :height="mainH"
                                border
                                :data="tableData"
                                style="width: 100%"
                                :edit-config="{ trigger: 'click', mode: 'cell' }"
                                v-loading="tableLoading"
                                ref="xTable">

                                <vxe-column field="RoleName" :title="$t('SopPermission.table.RoleName')" width="150"></vxe-column>

                                <vxe-column field="Upload" :title="$t('SopPermission.table.Upload')" width="80" align="center">
                                    <template #default="{ row }">
                                        <vxe-checkbox v-model="row.Upload" @change="updatePermLevel(row)"></vxe-checkbox>
                                    </template>
                                </vxe-column>

                                <vxe-column field="Download" :title="$t('SopPermission.table.Download')" width="80" align="center">
                                    <template #default="{ row }">
                                        <vxe-checkbox v-model="row.Download" @change="updatePermLevel(row)"></vxe-checkbox>
                                    </template>
                                </vxe-column>

                                <vxe-column field="Preview" :title="$t('SopPermission.table.Preview')" width="80" align="center">
                                    <template #default="{ row }">
                                        <vxe-checkbox v-model="row.Preview" @change="updatePermLevel(row)"></vxe-checkbox>
                                    </template>
                                </vxe-column>

                                <vxe-column field="Search" :title="$t('SopPermission.table.Search')" width="80" align="center">
                                    <template #default="{ row }">
                                        <vxe-checkbox v-model="row.Search" @change="updatePermLevel(row)"></vxe-checkbox>
                                    </template>
                                </vxe-column>

                                <vxe-column field="Delete" :title="$t('SopPermission.table.Delete')" width="80" align="center">
                                    <template #default="{ row }">
                                        <vxe-checkbox v-model="row.Delete" @change="updatePermLevel(row)"></vxe-checkbox>
                                    </template>
                                </vxe-column>

                                <vxe-column :title="$t('SopPermission.table.Operation')" width="160" align="center">
                                    <template #default="{ row }">
                                        <vxe-button type="text" @click="selectAll(row)">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>
                                        <vxe-button type="text" @click="cancelAll(row)">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>
                                    </template>
                                </vxe-column>
                            </vxe-table>
                        </div>
                        <div class="root-footer">
                            <el-pagination
                                class="mt-3"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                :current-page="searchForm.pageIndex"
                                :page-sizes="[10, 20, 50, 100, 500]"
                                :page-size="searchForm.pageSize"
                                layout="->,total, sizes, prev, pager, next, jumper"
                                :total="total"
                                background
                            ></el-pagination>
                        </div>
                    </div>
                </template>
            </split-pane>
        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import SplitPane from '../components/split-pane';
import { getSopPermissionList } from '@/api/SOP/sopPermission';
import { getSopDirTree } from '@/api/SOP/sopDir';

export default {
    name: 'index.vue',
    components: {
        SplitPane
    },
    data() {
        return {
            // 初始化加载状态
            initLoading: false,
            // 树相关数据
            treeData: [],
            treeLoading: false,
            selectedDir: null,
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            // 表格相关数据
            searchForm: {
                pageIndex: 1,
                pageSize: 20,
                targetId: undefined,
                targetType: undefined,
                grantType: undefined,
                grantId: undefined,
                permLevel: undefined
            },
            total: 0,
            tableData: [],
            tableLoading: false,
            mainH: 0
        };
    },
    async mounted() {
        try {
            this.initLoading = true;
            await this.getDirTree();
            this.$nextTick(() => {
                this.mainH = this.$webHeight(
                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,
                    document.getElementsByClassName('root')[0]?.clientHeight || 0
                );
            });
            window.onresize = () => {
                this.mainH = this.$webHeight(
                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,
                    document.getElementsByClassName('root')[0]?.clientHeight || 0
                );
            };
        } catch (err) {
            console.error('页面初始化失败:', err);
            this.$message.error('页面初始化失败，请刷新重试');
        } finally {
            this.initLoading = false;
        }
    },
    methods: {
        // 获取目录树
        async getDirTree() {
            this.treeLoading = true;
            try {
                const res = await getSopDirTree();
                if (res.success) {
                    this.treeData = res.response || [];
                } else {
                    this.$message.error(res.msg || '获取目录树失败');
                }
            } catch (err) {
                console.error('获取目录树失败:', err);
                this.$message.error('获取目录树失败');
                throw err;
            } finally {
                this.treeLoading = false;
            }
        },

        // 处理树节点点击
        handleNodeClick(data) {
            this.selectedDir = data;
            // 保留分页配置,重置其他搜索条件
            const { pageIndex, pageSize } = this.searchForm;
            this.searchForm = {
                pageIndex,
                pageSize,
                targetId: data.id,
                targetType: 1, // 1表示目录
                grantType: undefined,
                grantId: undefined,
                permLevel: undefined
            };
            this.getTableData();
        },

        // 展开所有节点
        expandAll() {
            const nodes = this.$refs.tree.store._getAllNodes();
            nodes.forEach(node => {
                this.expandNodes(node, true);
            });
        },

        // 收起所有节点
        collapseAll() {
            const nodes = this.$refs.tree.store._getAllNodes();
            nodes.forEach(node => {
                this.expandNodes(node, false);
            });
        },

        // 树节点展开关闭
        expandNodes(node, type) {
            node.expanded = type;
            for (let i = 0; i < node.childNodes.length; i++) {
                node.childNodes[i].expanded = type;
                if (node.childNodes[i].childNodes.length > 0) {
                    this.expandNodes(node.childNodes[i], type);
                }
            }
        },

        // 权限级别更新方法
        updatePermLevel(row) {
            let permLevel = 0;
            if (row.Preview) permLevel |= 1;    // 预览 (1)
            if (row.Download) permLevel |= 2;   // 下载 (2)
            if (row.Search) permLevel |= 3;     // 检索 (3)
            if (row.Upload) permLevel |= 4;     // 上传 (4)
            if (row.Delete) permLevel |= 8;     // 删除 (8)

            row.PermLevel = permLevel.toString();
            // 这里可以调用API保存权限变更
            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);
        },

        // 全选权限
        selectAll(row) {
            row.Preview = true;
            row.Download = true;
            row.Search = true;
            row.Upload = true;
            row.Delete = true;
            this.updatePermLevel(row);
        },

        // 取消全选
        cancelAll(row) {
            row.Preview = false;
            row.Download = false;
            row.Search = false;
            row.Upload = false;
            row.Delete = false;
            this.updatePermLevel(row);
        },

        // 解析权限级别字符串
        parsePermLevel(permLevel, row) {
            const level = parseInt(permLevel) || 0;
            row.Preview = (level & 1) > 0;      // 预览 (1)
            row.Download = (level & 2) > 0;     // 下载 (2)
            row.Search = (level & 3) > 0;       // 检索 (3)
            row.Upload = (level & 4) > 0;       // 上传 (4)
            row.Delete = (level & 8) > 0;       // 删除 (8)
        },
        handleCurrentChange(page) {
            this.searchForm.pageIndex = page;
            this.getTableData();
        },
        handleSizeChange(size) {
            this.searchForm.pageSize = size;
            this.getTableData();
        },
        getSearchBtn() {
            this.searchForm.pageIndex = 1;
            this.getTableData();
        },



        async getTableData() {
            if (!this.searchForm.targetId) {
                this.tableData = [];
                this.total = 0;
                return;
            }

            this.tableLoading = true;
            try {
                const res = await getSopPermissionList(this.searchForm);
                if (res.success) {
                    this.tableData = res.response.data || [];
                    this.total = res.response.dataCount || 0;

                    // 解析每行的权限级别
                    this.tableData.forEach(row => {
                        this.parsePermLevel(row.PermLevel, row);
                    });
                } else {
                    this.$message.error(res.msg || '获取权限数据失败');
                }
            } catch (err) {
                console.error('获取权限数据失败:', err);
                // 添加一些测试数据用于演示
                this.tableData = [
                    {
                        ID: '1',
                        RoleName: '管理员',
                        PermLevel: '15', // 1+2+4+8 = 15 (所有权限)
                        TargetId: this.searchForm.targetId,
                        GrantType: 1,
                        GrantId: 'admin-role'
                    },
                    {
                        ID: '2',
                        RoleName: '普通用户',
                        PermLevel: '3', // 1+2 = 3 (预览+下载)
                        TargetId: this.searchForm.targetId,
                        GrantType: 1,
                        GrantId: 'user-role'
                    },
                    {
                        ID: '3',
                        RoleName: '访客',
                        PermLevel: '1', // 1 (仅预览)
                        TargetId: this.searchForm.targetId,
                        GrantType: 1,
                        GrantId: 'guest-role'
                    }
                ];
                this.total = this.tableData.length;

                // 解析每行的权限级别
                this.tableData.forEach(row => {
                    this.parsePermLevel(row.PermLevel, row);
                });
            } finally {
                this.tableLoading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.root-layout {
  height: calc(100% - 60px);
}

.root-left {
  height: 100%;
  padding: 10px;
  overflow: auto;
  background-color: #f5f7fa;
  border-radius: 4px;

  .tree-toolbar {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;
    .tree-title {
      font-weight: 500;
    }
  }

  .tree-node-actions {
    display: none;
  }

  .el-tree-node__content:hover {
    .tree-node-actions {
      display: inline-block;
    }
  }
}

.root-right {
  padding: 0 12px;
  height: 100%;
  overflow: auto;

  .root-head {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;

    .selected-dir-info {
      padding: 4px;

      span {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .root-main {
    margin-top: 12px;

    .vxe-table {
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .root-footer {
    margin-top: 12px;
    padding: 8px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }
}
</style>
