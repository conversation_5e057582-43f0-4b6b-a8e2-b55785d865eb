<template>
    <div class="root">
        <div class="root-left">
            <sop-dir @node-click="handleDirClick" />
        </div>
        <div class="root-right">
            <div class="root-head">
                <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
                    <el-form-item :label="$t('SOP.DirName')" prop="dirName">
                        <el-input v-model="searchForm.dirName" :placeholder="$t('SOP.EnterDirName')" />
                    </el-form-item>

                    <el-form-item :label="$t('SOP.DirCode')" prop="dirCode">
                        <el-input v-model="searchForm.dirCode" :placeholder="$t('SOP.EnterDirCode')" />
                    </el-form-item>

                    <el-form-item class="mb-2">
                        <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
                            {{ $t('DFM_JSGL._FPQX') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="root-main">
                <el-table class="mt-3" :height="mainH" border :data="tableData" style="width: 100%">
                    <el-table-column
                        v-for="item in tableName"
                        :default-sort="{ prop: 'date', order: 'descending' }"
                        :key="item.ID"
                        :prop="item.field"
                        :label="item.label"
                        :width="item.width"
                        :align="item.alignType"
                        sortable
                        show-overflow-tooltip
                    >
                        <template slot-scope="scope">
                            {{ scope.row[item.field] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
                        <template slot-scope="scope">
                            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="root-footer">
                <el-pagination
                    class="mt-3"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.pageIndex"
                    :page-sizes="[10, 20, 50, 100, 500]"
                    :page-size="searchForm.pageSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="total"
                    background
                ></el-pagination>
            </div>
            <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import FormDialog from './form-dialog';
import SopDir from '../sopDir/index.vue';
import { delSopPermission, getSopPermissionList } from '@/api/SOP/sopPermission';
import { getTableHead } from '@/util/dataDictionary.js';

import { sopPermissionColumn } from '@/api/SOP/sopPermission.js';

export default {
    name: 'index.vue',
    components: {
        FormDialog,
        SopDir
    },
    data() {
        return {
            searchForm: {
                pageIndex: 1,
                pageSize: 20,
                dirName: undefined,
                dirCode: undefined,
                targetId: undefined,
                targetType: undefined,
                grantType: undefined,
                grantId: undefined,
                permLevel: undefined
            },
            total: 0,
            tableData: [{}],
            hansObj: this.$t('SopPermission.table'),
            tableName: [],
            loading: false,
            tableOption: [],
            mainH: 0,
            buttonOption: {
                name: 'SopPermission',
                serveIp: 'baseURL_SOP',
                uploadUrl: '/api/SopPermission/ImportData', //导入
                exportUrl: '/api/SopPermission/ExportData', //导出
                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板
            }
        };
    },
    mounted() {
        this.getZHHans();
        this.getTableData();
        this.$nextTick(() => {
            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);
        });
        window.onresize = () => {
            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);
        };
    },
    methods: {
        handleDirClick(node) {
            this.searchForm.targetId = node.id;
            this.searchForm.targetType = 1; // 1表示目录
            this.getTableData();
        },

        getZHHans() {
            for (let key in this.hansObj) {
                this.tableName = getTableHead(this.hansObj, this.tableOption);
            }
        },
        showDialog(row) {
            this.$refs.formDialog.show(row);
        },
        handleCurrentChange(page) {
            this.searchForm.pageIndex = page;
            this.getTableData();
        },
        handleSizeChange(size) {
            this.searchForm.pageSize = size;
            this.getTableData();
        },
        getSearchBtn() {
            this.searchForm.pageIndex = 1;
            this.getTableData();
        },

        delRow(row) {
            this.$confirms({
                title: this.$t('GLOBAL._TS'),
                message: this.$t('GLOBAL._COMFIRM'),
                confirmText: this.$t('GLOBAL._QD'),
                cancelText: this.$t('GLOBAL._QX')
            })
                .then(async () => {
                    delSopPermission([row.ID]).then(res => {
                        this.$message.success(res.msg);
                        this.getTableData();
                    });
                })
                .catch(err => {
                    console.log(err);
                });
        },

        getTableData(data) {
            getSopPermissionList(this.searchForm).then(res => {
                this.tableData = res.response.data;
                this.total = res.response.dataCount;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.root {
    display: flex;
    height: 100%;

    .root-left {
        width: 280px;
        padding: 10px;
        border-right: 1px solid #ebeef5;
        overflow: auto;
    }

    .root-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .root-head {
        padding: 10px;
        background: #fff;
    }

    .root-main {
        flex: 1;
        padding: 0 10px;
        overflow: auto;
    }

    .root-footer {
        padding: 10px;
        background: #fff;
    }
}

.el-form-item--small.el-form-item {
    margin-bottom: 0px;
}

.mt-8p {
    margin-top: 8px;
}

.pd-left {
    padding-left: 5px;
}
</style>
