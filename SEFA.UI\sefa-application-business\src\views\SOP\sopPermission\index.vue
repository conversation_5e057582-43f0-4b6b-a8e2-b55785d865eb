<template>
    <div class="root">
        <div class="root-left">
            <sop-dir @node-click="handleDirClick" />
        </div>
        <div class="root-right">
            <div class="root-head">
                <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
                    <el-form-item :label="$t('SOP.DirName')" prop="dirName">
                        <el-input v-model="searchForm.dirName" :placeholder="$t('SOP.EnterDirName')" />
                    </el-form-item>

                    <el-form-item :label="$t('SOP.DirCode')" prop="dirCode">
                        <el-input v-model="searchForm.dirCode" :placeholder="$t('SOP.EnterDirCode')" />
                    </el-form-item>

                    <el-form-item class="mb-2">
                        <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
                    </el-form-item>

                </el-form>
            </div>
            <div class="root-main">
                <vxe-table
                    class="mt-3"
                    :height="mainH"
                    border
                    :data="tableData"
                    style="width: 100%"
                    :edit-config="{ trigger: 'click', mode: 'cell' }"
                    ref="xTable">

                    <vxe-column field="RoleName" :title="$t('SopPermission.table.RoleName')" width="150"></vxe-column>

                    <vxe-column field="Upload" :title="$t('SopPermission.table.Upload')" width="80" align="center">
                        <template #default="{ row }">
                            <vxe-checkbox v-model="row.Upload" @change="updatePermLevel(row)"></vxe-checkbox>
                        </template>
                    </vxe-column>

                    <vxe-column field="Download" :title="$t('SopPermission.table.Download')" width="80" align="center">
                        <template #default="{ row }">
                            <vxe-checkbox v-model="row.Download" @change="updatePermLevel(row)"></vxe-checkbox>
                        </template>
                    </vxe-column>

                    <vxe-column field="Preview" :title="$t('SopPermission.table.Preview')" width="80" align="center">
                        <template #default="{ row }">
                            <vxe-checkbox v-model="row.Preview" @change="updatePermLevel(row)"></vxe-checkbox>
                        </template>
                    </vxe-column>

                    <vxe-column field="Search" :title="$t('SopPermission.table.Search')" width="80" align="center">
                        <template #default="{ row }">
                            <vxe-checkbox v-model="row.Search" @change="updatePermLevel(row)"></vxe-checkbox>
                        </template>
                    </vxe-column>

                    <vxe-column field="Delete" :title="$t('SopPermission.table.Delete')" width="80" align="center">
                        <template #default="{ row }">
                            <vxe-checkbox v-model="row.Delete" @change="updatePermLevel(row)"></vxe-checkbox>
                        </template>
                    </vxe-column>

                    <vxe-column :title="$t('SopPermission.table.Operation')" width="160" align="center">
                        <template #default="{ row }">
                            <vxe-button type="text" @click="selectAll(row)">{{ $t('SopPermission.table.SelectAll') }}</vxe-button>
                            <vxe-button type="text" @click="cancelAll(row)">{{ $t('SopPermission.table.CancelAll') }}</vxe-button>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>
            <div class="root-footer">
                <el-pagination
                    class="mt-3"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="searchForm.pageIndex"
                    :page-sizes="[10, 20, 50, 100, 500]"
                    :page-size="searchForm.pageSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="total"
                    background
                ></el-pagination>
            </div>

        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import SopDir from '../sopDir/index.vue';
import { getSopPermissionList } from '@/api/SOP/sopPermission';

export default {
    name: 'index.vue',
    components: {
        SopDir
    },
    data() {
        return {
            searchForm: {
                pageIndex: 1,
                pageSize: 20,
                dirName: undefined,
                dirCode: undefined,
                targetId: undefined,
                targetType: undefined,
                grantType: undefined,
                grantId: undefined,
                permLevel: undefined
            },
            total: 0,
            tableData: [],
            loading: false,
            mainH: 0,
            buttonOption: {
                name: 'SopPermission',
                serveIp: 'baseURL_SOP',
                uploadUrl: '/api/SopPermission/ImportData', //导入
                exportUrl: '/api/SopPermission/ExportData', //导出
                DownLoadUrl: '/api/SopPermission/DownLoadTemplate' //下载模板
            }
        };
    },
    mounted() {
        this.getTableData();
        this.$nextTick(() => {
            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);
        });
        window.onresize = () => {
            this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight);
        };
    },
    methods: {
        handleDirClick(node) {
            this.searchForm.targetId = node.id;
            this.searchForm.targetType = 1; // 1表示目录
            this.getTableData();
        },

        // 权限级别更新方法
        updatePermLevel(row) {
            let permLevel = 0;
            if (row.Preview) permLevel |= 1;    // 预览 (1)
            if (row.Download) permLevel |= 2;   // 下载 (2)
            if (row.Search) permLevel |= 3;     // 检索 (3)
            if (row.Upload) permLevel |= 4;     // 上传 (4)
            if (row.Delete) permLevel |= 8;     // 删除 (8)

            row.PermLevel = permLevel.toString();
            // 这里可以调用API保存权限变更
            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);
        },

        // 全选权限
        selectAll(row) {
            row.Preview = true;
            row.Download = true;
            row.Search = true;
            row.Upload = true;
            row.Delete = true;
            this.updatePermLevel(row);
        },

        // 取消全选
        cancelAll(row) {
            row.Preview = false;
            row.Download = false;
            row.Search = false;
            row.Upload = false;
            row.Delete = false;
            this.updatePermLevel(row);
        },

        // 解析权限级别字符串
        parsePermLevel(permLevel, row) {
            const level = parseInt(permLevel) || 0;
            row.Preview = (level & 1) > 0;      // 预览 (1)
            row.Download = (level & 2) > 0;     // 下载 (2)
            row.Search = (level & 3) > 0;       // 检索 (3)
            row.Upload = (level & 4) > 0;       // 上传 (4)
            row.Delete = (level & 8) > 0;       // 删除 (8)
        },
        handleCurrentChange(page) {
            this.searchForm.pageIndex = page;
            this.getTableData();
        },
        handleSizeChange(size) {
            this.searchForm.pageSize = size;
            this.getTableData();
        },
        getSearchBtn() {
            this.searchForm.pageIndex = 1;
            this.getTableData();
        },



        getTableData() {
            getSopPermissionList(this.searchForm).then(res => {
                this.tableData = res.response.data || [];
                this.total = res.response.dataCount || 0;

                // 解析每行的权限级别
                this.tableData.forEach(row => {
                    this.parsePermLevel(row.PermLevel, row);
                });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.root {
    display: flex;
    height: 100%;

    .root-left {
        width: 280px;
        padding: 10px;
        border-right: 1px solid #ebeef5;
        overflow: auto;
    }

    .root-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .root-head {
        padding: 10px;
        background: #fff;
    }

    .root-main {
        flex: 1;
        padding: 0 10px;
        overflow: auto;
    }

    .root-footer {
        padding: 10px;
        background: #fff;
    }
}

.el-form-item--small.el-form-item {
    margin-bottom: 0px;
}

.mt-8p {
    margin-top: 8px;
}

.pd-left {
    padding-left: 5px;
}
</style>
